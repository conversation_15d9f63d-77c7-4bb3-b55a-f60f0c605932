﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class AddedShippingInstructionNoAndDate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateOnly>(
                name: "LetterDate",
                table: "T_MDOC",
                type: "date",
                nullable: true);

            migrationBuilder.AddColumn<DateOnly>(
                name: "ShippingInstructionDate",
                table: "T_MDOC",
                type: "date",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ShippingInstructionNo",
                table: "T_MDOC",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LetterDate",
                table: "T_MDOC");

            migrationBuilder.DropColumn(
                name: "ShippingInstructionDate",
                table: "T_MDOC");

            migrationBuilder.DropColumn(
                name: "ShippingInstructionNo",
                table: "T_MDOC");
        }
    }
}
