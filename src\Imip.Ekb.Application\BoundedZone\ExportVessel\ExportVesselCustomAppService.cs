using ExportVesselEntity = Imip.Ekb.BoundedZone.ExportVessels.ExportVessel;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.ExportVessels;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp;

namespace Imip.Ekb.BoundedZone.ExportVessel;

[Authorize]
[RemoteService(false)]
public class ExportVesselCustomAppService : ApplicationService, IExportVesselCustomAppService
{
    private readonly IExportVesselRepository _exportVesselRepository;
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly ExportVesselMapper _exportVesselMapper;
    private readonly VesselMapper _vesselMapper;
    private readonly ZoneDetailMapper _zoneDetailMapper;
    private readonly ILogger<ExportVesselCustomAppService> _logger;

    public ExportVesselCustomAppService(
        IExportVesselRepository exportVesselRepository,
        IZoneDetailRepository zoneDetailRepository,
        ExportVesselMapper exportVesselMapper,
        VesselMapper vesselMapper,
        ZoneDetailMapper zoneDetailMapper,
        ILogger<ExportVesselCustomAppService> logger)
    {
        _exportVesselRepository = exportVesselRepository;
        _zoneDetailRepository = zoneDetailRepository;
        _exportVesselMapper = exportVesselMapper;
        _vesselMapper = vesselMapper;
        _zoneDetailMapper = zoneDetailMapper;
        _logger = logger;
    }

    public async Task<PagedResultDto<ExportVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters)
    {
        await CheckGetListPolicyAsync();

        var entityQuery = await _exportVesselRepository.GetQueryableWithIncludesAsync();
        entityQuery = ApplyDynamicQuery(entityQuery, parameters);

        System.Linq.Expressions.Expression<Func<ExportVesselEntity, ExportVesselProjectionDto>> selector = x => new ExportVesselProjectionDto
        {
            Id = x.Id,
            ConcurrencyStamp = x.ConcurrencyStamp,
            DocEntry = x.DocEntry,
            DocNum = x.DocNum,
            PostingDate = x.PostingDate,
            VesselName = x.Vessel != null ? x.Vessel.Name : null,
            Shipment = x.Shipment,
            VesselArrival = x.VesselArrival,
            VesselDeparture = x.VesselDeparture,
            PortOrigin = x.PortOrigin,
            DestinationPort = x.DestinationPort,
            Voyage = x.Voyage,
            GrossWeight = x.GrossWeight,
            DocStatus = x.DocStatus,
            Remarks = x.Remarks,
            DocType = x.DocType,
            BerthingDate = x.BerthingDate,
            AnchorageDate = x.AnchorageDate,
            ReportDate = x.ReportDate,
            UnloadingDate = x.UnloadingDate,
            FinishUnloadingDate = x.FinishUnloadingDate,
            GrtWeight = x.GrtWeight,
            InvoiceStatus = x.InvoiceStatus,
            StatusBms = x.StatusBms,
            JettyId = x.JettyId,
            VesselId = x.VesselId,
            MasterAgentId = x.MasterAgentId,
            MasterTradingId = x.MasterTradingId,
            MasterSurveyorId = x.MasterSurveyorId,
            MasterAgent = x.MasterAgent == null ? null : new Master.Dtos.AgentProjectionDto
            {
                Id = x.MasterAgent.Id,
                Name = x.MasterAgent.Name,
                Status = x.MasterAgent.Status,
                Type = x.MasterAgent.Type,
                NpwpNo = x.MasterAgent.NpwpNo,
                BdmSapcode = x.MasterAgent.BdmSapcode,
                TaxCode = x.MasterAgent.TaxCode,
                AddressNpwp = x.MasterAgent.AddressNpwp,
                Address = x.MasterAgent.Address,
                SapcodeS4 = x.MasterAgent.SapcodeS4,
            },
            MasterTrading = x.MasterTrading == null ? null : new Master.Dtos.TradingProjectionDto
            {
                Id = x.MasterTrading.Id,
                Name = x.MasterTrading.Name,
                Address = x.MasterTrading.Address,
                Npwp = x.MasterTrading.Npwp,
                IsActive = x.MasterTrading.IsActive,
            },
            MasterSurveyor = x.MasterSurveyor == null ? null : new Master.Dtos.SurveyorProjectionDto
            {
                Id = x.MasterSurveyor.Id,
                Name = x.MasterSurveyor.Name,
                Address = x.MasterSurveyor.Address,
                Npwp = x.MasterSurveyor.Npwp,
                IsActive = x.MasterSurveyor.IsActive,
            },
            MasterJetty = x.MasterJetty == null ? null : new Master.Dtos.JettyProjectionDto
            {
                Id = x.MasterJetty.Id,
                Name = x.MasterJetty.Name,
                Alias = x.MasterJetty.Alias,
                Max = x.MasterJetty.Max,
                Port = x.MasterJetty.Port,
                IsCustomArea = x.MasterJetty.IsCustomArea,
                Deleted = x.MasterJetty.Deleted,
                DocEntry = x.MasterJetty.DocEntry,
            },
            Vessel = x.Vessel == null ? null : new Master.Dtos.CargoProjectionDto
            {
                Id = x.Vessel.Id,
                Name = x.Vessel.Name,
                Alias = x.Vessel.Alias,
                Flag = x.Vessel.Flag,
                GrossWeight = x.Vessel.GrossWeight,
                Type = x.Vessel.Type,
                LoaQty = x.Vessel.LoaQty,
                Status = x.Vessel.Status,
                DocEntry = x.Vessel.DocEntry,
            },
        };

        var projectedQuery = entityQuery.Select(selector);

        var totalCount = await AsyncExecuter.CountAsync(projectedQuery);
        var items = await AsyncExecuter.ToListAsync(
            projectedQuery.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        return new PagedResultDto<ExportVesselProjectionDto>
        {
            TotalCount = totalCount,
            Items = items
        };
    }

    public async Task<ExportVesselWithItemsDto> GetWithItemsAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var vesselWithItems = await _exportVesselRepository.GetQueryableWithItemsSplitAsync(id);

        if (vesselWithItems == null)
        {
            throw new UserFriendlyException("ExportVessel not found");
        }

        var dto = new ExportVesselWithItemsDto
        {
            Id = vesselWithItems.Id,
            ConcurrencyStamp = vesselWithItems.ConcurrencyStamp,
            DocEntry = vesselWithItems.DocEntry,
            DocNum = vesselWithItems.DocNum,
            PostingDate = vesselWithItems.PostingDate,
            Shipment = vesselWithItems.Shipment,
            VesselArrival = vesselWithItems.VesselArrival,
            VesselDeparture = vesselWithItems.VesselDeparture,
            PortOrigin = vesselWithItems.PortOrigin,
            DestinationPort = vesselWithItems.DestinationPort,
            Voyage = vesselWithItems.Voyage,
            GrossWeight = vesselWithItems.GrossWeight,
            DocStatus = vesselWithItems.DocStatus,
            Remarks = vesselWithItems.Remarks,
            DocType = vesselWithItems.DocType,
            BerthingDate = vesselWithItems.BerthingDate,
            AnchorageDate = vesselWithItems.AnchorageDate,
            ReportDate = vesselWithItems.ReportDate,
            UnloadingDate = vesselWithItems.UnloadingDate,
            FinishUnloadingDate = vesselWithItems.FinishUnloadingDate,
            GrtWeight = vesselWithItems.GrtWeight,
            InvoiceStatus = vesselWithItems.InvoiceStatus,
            StatusBms = vesselWithItems.StatusBms,
            JettyId = vesselWithItems.JettyId,
            VesselId = vesselWithItems.VesselId,
            MasterAgentId = vesselWithItems.MasterAgentId,
            MasterTradingId = vesselWithItems.MasterTradingId,
            MasterSurveyorId = vesselWithItems.MasterSurveyorId,
            MasterJetty = vesselWithItems.MasterJetty == null ? null : new Master.Jetties.Dtos.JettyDto
            {
                Id = vesselWithItems.MasterJetty.Id,
                Name = vesselWithItems.MasterJetty.Name,
                Alias = vesselWithItems.MasterJetty.Alias,
                Max = vesselWithItems.MasterJetty.Max,
                Port = vesselWithItems.MasterJetty.Port,
                IsCustomArea = vesselWithItems.MasterJetty.IsCustomArea,
            },
            Vessel = vesselWithItems.Vessel == null ? null : new Master.Cargos.Dtos.CargoDto
            {
                Id = vesselWithItems.Vessel.Id,
                Name = vesselWithItems.Vessel.Name,
                Alias = vesselWithItems.Vessel.Alias,
                Flag = vesselWithItems.Vessel.Flag,
                GrossWeight = vesselWithItems.Vessel.GrossWeight,
                Type = vesselWithItems.Vessel.Type,
                LoaQty = vesselWithItems.Vessel.LoaQty,
            },
            MasterAgent = vesselWithItems.MasterAgent == null ? null : new Master.Agents.Dtos.AgentDto
            {
                Id = vesselWithItems.MasterAgent.Id,
                Name = vesselWithItems.MasterAgent.Name,
                Status = vesselWithItems.MasterAgent.Status,
                Type = vesselWithItems.MasterAgent.Type,
                NpwpNo = vesselWithItems.MasterAgent.NpwpNo,
                BdmSapcode = vesselWithItems.MasterAgent.BdmSapcode,
                TaxCode = vesselWithItems.MasterAgent.TaxCode,
                AddressNpwp = vesselWithItems.MasterAgent.AddressNpwp,
                Address = vesselWithItems.MasterAgent.Address,
                SapcodeS4 = vesselWithItems.MasterAgent.SapcodeS4,
            },
            MasterTrading = vesselWithItems.MasterTrading == null ? null : new Master.Tradings.Dtos.TradingDto
            {
                Id = vesselWithItems.MasterTrading.Id,
                Name = vesselWithItems.MasterTrading.Name,
                Address = vesselWithItems.MasterTrading.Address,
                Npwp = vesselWithItems.MasterTrading.Npwp,
            },
            MasterSurveyor = vesselWithItems.MasterSurveyor == null ? null : new Master.Surveyors.Dtos.SurveyorDto
            {
                Id = vesselWithItems.MasterSurveyor.Id,
                Name = vesselWithItems.MasterSurveyor.Name,
                Address = vesselWithItems.MasterSurveyor.Address,
                Npwp = vesselWithItems.MasterSurveyor.Npwp,
            },
            Items = vesselWithItems.Items?.Select(static zoneDetail => new Dtos.VesselItemDto
            {
                Id = zoneDetail.Id,
                DocEntry = zoneDetail.DocEntry,
                DocNum = zoneDetail.DocNum ?? 0,
                TenantName = zoneDetail.Tenant != null ? zoneDetail.Tenant.Name : null,
                ItemName = zoneDetail.ItemName,
                ItemQty = zoneDetail.ItemQty,
                UnitQty = zoneDetail.UnitQty,
                Cargo = zoneDetail.Cargo,
                Shipment = zoneDetail.Shipment,
                Remarks = zoneDetail.Remarks,
                NoBl = zoneDetail.NoBl,
                DateBl = zoneDetail.DateBl,
                AjuNo = zoneDetail.AjuNo,
                RegNo = zoneDetail.RegNo,
                RegDate = zoneDetail.RegDate,
                SppbNo = zoneDetail.SppbNo,
                SppbDate = zoneDetail.SppbDate,
                SppdNo = zoneDetail.SppdNo,
                SppdDate = zoneDetail.SppdDate,
                GrossWeight = zoneDetail.GrossWeight,
                UnitWeight = zoneDetail.UnitWeight,
                HeaderId = zoneDetail.HeaderId,
                LetterNo = zoneDetail.LetterNo,
                DocType = zoneDetail.DocType,
                VesselType = "ExportVessel",
                ShippingInstructionNo = zoneDetail.ShippingInstructionNo,
                ShippingInstructionDate = zoneDetail.ShippingInstructionDate,
                LetterDate = zoneDetail.LetterDate,
                TenantId = zoneDetail.TenantId,
                BcTypeId = zoneDetail.BcTypeId,
                BusinessPartnerId = zoneDetail.BusinessPartnerId,
                AgentId = zoneDetail.AgentId,
                MasterExportClassificationId = zoneDetail.MasterExportClassificationId,
                Tenant = zoneDetail.Tenant == null ? null : new Dtos.TenantShortDto
                {
                    Id = zoneDetail.Tenant.Id,
                    Name = zoneDetail.Tenant.Name,
                    FullName = zoneDetail.Tenant.FullName,
                    DocEntry = zoneDetail.Tenant.DocEntry
                },
                BcType = zoneDetail.BcType == null ? null : new Dtos.BcTypeShortDto
                {
                    Id = zoneDetail.BcType.Id,
                    Type = zoneDetail.BcType.Type,
                    TransName = zoneDetail.BcType.TransName,
                },
                BusinessPartner = zoneDetail.BusinessPartner == null ? null : new Dtos.BusinessPartnerShortDto
                {
                    Id = zoneDetail.BusinessPartner.Id,
                    Name = zoneDetail.BusinessPartner.Name,
                    Npwp = zoneDetail.BusinessPartner.Npwp,
                    Nitku = zoneDetail.BusinessPartner.Nitku,
                    DocEntry = zoneDetail.BusinessPartner.DocEntry
                },
                MasterAgent = zoneDetail.MasterAgent == null ? null : new Dtos.AgentShortDto
                {
                    Id = zoneDetail.MasterAgent.Id,
                    Name = zoneDetail.MasterAgent.Name,
                    Type = zoneDetail.MasterAgent.Type,
                },
                Attachments = zoneDetail.DocAttachment != null ? zoneDetail.DocAttachment.Select(att => new Dtos.DocAttachmentSortDto
                {
                    Id = att.Id,
                    DocType = att.DocType,
                    TransType = att.TransType,
                    Description = att.Description,
                    BlobName = att.BlobName,
                    ReferenceId = att.ReferenceId,
                    FileName = att.FileName,
                    StreamUrl = att.FilePath,
                    TypePa = att.TypePa
                }).ToList() : new List<Dtos.DocAttachmentSortDto>()
            }).ToList() ?? new List<Dtos.VesselItemDto>()
        };

        return dto;
    }

    private IQueryable<ExportVesselEntity> ApplyDynamicQuery(IQueryable<ExportVesselEntity> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ExportVesselEntity>.ApplyFilters(query, parameters.FilterGroup);
        }

        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ExportVesselEntity>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }

    private async Task CheckGetListPolicyAsync()
    {
        // Implement your policy check logic here if needed
        await Task.CompletedTask;
    }

    private async Task CheckGetPolicyAsync()
    {
        // Implement your policy check logic here if needed
        await Task.CompletedTask;
    }
}