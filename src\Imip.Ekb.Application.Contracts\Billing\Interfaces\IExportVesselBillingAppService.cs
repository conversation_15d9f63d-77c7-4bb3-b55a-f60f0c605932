using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.Ekb.Billing.LocalVesselBillings.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Billing.LocalVesselBillings.Interfaces;

public interface IExportVesselBillingAppService :
    ICrudAppService<
        ExportVesselBillingDto,
        Guid,
        QueryParametersDto,
        CreateUpdateExportVesselBillingDto>
{
    Task<ExportVesselBillingWithItemsDto> GetWithItemsAsync(Guid id);
    Task<List<BillingItemDto>> GetItemsAsync(int docEntry);
    Task<PagedResultDto<ExportVesselBillingDto>> FilterListAsync(QueryParametersDto parameters);
}