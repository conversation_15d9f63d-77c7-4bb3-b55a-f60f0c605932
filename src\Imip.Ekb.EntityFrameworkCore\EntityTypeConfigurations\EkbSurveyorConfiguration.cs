using Imip.Ekb.Master.Surveyors;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;


namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbSurveyorConfiguration : IEntityTypeConfiguration<Surveyor>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbSurveyorConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<Surveyor> b)
    {
        b.ToTable("master_surveyors", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        // Configure DocEntry property to ensure it's stored as bigint in database
        b.Property(x => x.DocEntry)
            .HasColumnType("bigint");

        b.Property(x => x.UpdatedBy)
            .HasColumnType("bigint");
    }
}