using Imip.Ekb.Billing.ExportVesselBillings;
using Imip.Ekb.Billing.LocalVesselBillings.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class ExportVesselBillingMapper : IMapperlyMapper
{
    [MapProperty(nameof(ExportVesselBilling.Id), nameof(ExportVesselBillingDto.Id))]
    [MapProperty(nameof(ExportVesselBilling.DocEntry), nameof(ExportVesselBillingDto.DocEntry))]
    [MapProperty(nameof(ExportVesselBilling.PortService), nameof(ExportVesselBillingDto.PortService))]
    [MapProperty(nameof(ExportVesselBilling.Status), nameof(ExportVesselBillingDto.Status))]
    [MapProperty(nameof(ExportVesselBilling.YearArrival), nameof(ExportVesselBillingDto.YearArrival))]
    [MapProperty(nameof(ExportVesselBilling.Jetty), nameof(ExportVesselBillingDto.Jetty))]
    [MapProperty(nameof(ExportVesselBilling.ExportId), nameof(ExportVesselBillingDto.ExportId))]
    [MapProperty(nameof(ExportVesselBilling.CreatedBy), nameof(ExportVesselBillingDto.CreatedBy))]
    [MapProperty(nameof(ExportVesselBilling.UpdatedBy), nameof(ExportVesselBillingDto.UpdatedBy))]
    [MapProperty(nameof(ExportVesselBilling.CreatedAt), nameof(ExportVesselBillingDto.CreatedAt))]
    [MapProperty(nameof(ExportVesselBilling.UpdatedAt), nameof(ExportVesselBillingDto.UpdatedAt))]
    [MapProperty(nameof(ExportVesselBilling.DocNum), nameof(ExportVesselBillingDto.DocNum))]
    [MapProperty(nameof(ExportVesselBilling.PostingDate), nameof(ExportVesselBillingDto.PostingDate))]
    [MapProperty(nameof(ExportVesselBilling.Remarks), nameof(ExportVesselBillingDto.Remarks))]
    [MapProperty(nameof(ExportVesselBilling.PeriodDate), nameof(ExportVesselBillingDto.PeriodDate))]
    [MapProperty(nameof(ExportVesselBilling.BillingNoteDate), nameof(ExportVesselBillingDto.BillingNoteDate))]
    [MapProperty(nameof(ExportVesselBilling.JettyId), nameof(ExportVesselBillingDto.JettyId))]
    public partial ExportVesselBillingDto MapToDto(ExportVesselBilling entity);

    [MapperIgnoreTarget(nameof(ExportVesselBilling.Id))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.DocEntry))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.CreatedBy))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.CreatedAt))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.CreationTime))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.CreatorId))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.LastModificationTime))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.LastModifierId))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.IsDeleted))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.DeleterId))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.DeletionTime))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.ExtraProperties))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.MasterJetty))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.Items))]
    public partial void MapToEntity(CreateUpdateExportVesselBillingDto dto, ExportVesselBilling entity);

    [MapperIgnoreTarget(nameof(ExportVesselBilling.Id))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.DocEntry))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.CreatedBy))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.CreatedAt))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.CreationTime))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.CreatorId))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.LastModificationTime))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.LastModifierId))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.IsDeleted))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.DeleterId))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.DeletionTime))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.ExtraProperties))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.MasterJetty))]
    [MapperIgnoreTarget(nameof(ExportVesselBilling.Items))]
    public partial ExportVesselBilling MapToEntity(CreateUpdateExportVesselBillingDto dto);

    public partial List<ExportVesselBillingDto> MapToDtoList(List<ExportVesselBilling> entities);
    public partial IEnumerable<ExportVesselBillingDto> MapToDtoEnumerable(IEnumerable<ExportVesselBilling> entities);

    public ExportVesselBillingWithItemsDto MapToDtoWithItems(ExportVesselBilling entity, List<BillingItemDto> items)
    {
        var dto = MapToDto(entity);
        var withItemsDto = new ExportVesselBillingWithItemsDto
        {
            Id = dto.Id,
            DocEntry = dto.DocEntry,
            PortService = dto.PortService,
            Status = dto.Status,
            YearArrival = dto.YearArrival,
            Jetty = dto.Jetty,
            ExportId = dto.ExportId,
            CreatedBy = dto.CreatedBy,
            UpdatedBy = dto.UpdatedBy,
            CreatedAt = dto.CreatedAt,
            UpdatedAt = dto.UpdatedAt,
            DocNum = dto.DocNum,
            PostingDate = dto.PostingDate,
            Remarks = dto.Remarks,
            PeriodDate = dto.PeriodDate,
            BillingNoteDate = dto.BillingNoteDate,
            JettyId = dto.JettyId,
            MasterJetty = dto.MasterJetty,
            Items = items
        };
        return withItemsDto;
    }
}