using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Imip.Ekb.BoundedZone.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.BoundedZone.ExportVessels;

public class CreateUpdateExportVesselDto : EntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    [StringLength(50)]
    public string DocNum { get; set; } = null!;

    public DateOnly PostingDate { get; set; }

    public int VesselName { get; set; }

    public DateTime VesselArrival { get; set; }

    public DateTime? VesselDeparture { get; set; }

    [StringLength(100)]
    public string Voyage { get; set; } = null!;

    [StringLength(100)]
    public string Shipment { get; set; } = null!;

    public decimal VesselQty { get; set; }

    [StringLength(200)]
    public string PortOrigin { get; set; } = null!;

    [StringLength(255)]
    public string? DestinationPort { get; set; }

    public string? Remarks { get; set; }

    [StringLength(5)]
    public string Deleted { get; set; } = null!;

    public int UpdatedBy { get; set; }

    [StringLength(255)]
    public string DocStatus { get; set; } = null!;

    public decimal? GrossWeight { get; set; }

    [StringLength(255)]
    public string? VesselFlag { get; set; }

    [StringLength(255)]
    public string? VesselStatus { get; set; }

    public int? Jetty { get; set; }

    public DateTime? BerthingDate { get; set; }

    public DateTime? AnchorageDate { get; set; }

    public DateOnly? ReportDate { get; set; }

    public DateTime? UnloadingDate { get; set; }

    public DateTime? FinishUnloadingDate { get; set; }

    public decimal? DeadWeight { get; set; }

    public decimal? GrtWeight { get; set; }

    [StringLength(50)]
    public string? InvoiceStatus { get; set; }

    public long? AgentId { get; set; }

    [StringLength(255)]
    public string? AgentName { get; set; }

    [StringLength(100)]
    public string StatusBms { get; set; } = null!;

    public long? SurveyorId { get; set; }

    public long? TradingId { get; set; }

    [StringLength(255)]
    public string DocType { get; set; } = null!;

    public Guid? JettyId { get; set; }
    public Guid? VesselId { get; set; }
    public Guid? MasterAgentId { get; set; }
    public Guid? MasterTradingId { get; set; }
    public Guid? MasterSurveyorId { get; set; }
    /// <summary>
    /// Aside date and time
    /// </summary>
    public DateTime? AsideDate { get; set; }

    /// <summary>
    /// Cast off date and time
    /// </summary>
    public DateTime? CastOfDate { get; set; }
    public Guid? PortOriginId { get; set; }
    public Guid? DestinationPortId { get; set; }


    // Items for create/update
    public List<CreateUpdateVesselItemDto>? Items { get; set; }
}