﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.BoundedZone.ExportVessels;

public interface IExportVesselRepository : IRepository<ExportVessel, Guid>
{
    Task<IQueryable<ExportVessel>> GetQueryableWithIncludesAsync();
    Task<IQueryable<ExportVessel>> GetQueryableWithItemsAsync();
    Task<ExportVessel?> GetQueryableWithItemsSplitAsync(Guid id);
    Task<int> CountAsync();
}