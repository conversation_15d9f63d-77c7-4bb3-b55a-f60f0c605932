using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.Ekb.Billing.LocalVesselBillings.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Billing.LocalVesselBillings.Interfaces;

public interface ILocalVesselBillingAppService :
    ICrudAppService<
        LocalVesselBillingDto,
        Guid,
        QueryParametersDto,
        CreateUpdateLocalVesselBillingDto>
{
    Task<LocalVesselBillingWithItemsDto> GetWithItemsAsync(Guid id);
    Task<List<BillingItemDto>> GetItemsAsync(int docEntry);
    Task<PagedResultDto<LocalVesselBillingDto>> FilterListAsync(QueryParametersDto parameters);
}