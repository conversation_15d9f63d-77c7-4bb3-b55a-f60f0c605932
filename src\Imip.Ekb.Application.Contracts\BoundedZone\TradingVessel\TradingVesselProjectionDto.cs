using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.BoundedZone.TradingVessels;

public class TradingVesselProjectionDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public long DocNum { get; set; }
    public long Tenant { get; set; }
    [StringLength(255)]
    public string? Bp { get; set; }
    public DateOnly PostDate { get; set; }
    [StringLength(255)]
    public string? Contract { get; set; }
    public int Bc { get; set; }
    public int CreatedBy { get; set; }
    public int UpdatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    [StringLength(1)]
    public string Deleted { get; set; } = null!;
    [StringLength(255)]
    public string? Status { get; set; }
    public string? Remark { get; set; }
    public DateOnly? ContractDate { get; set; }
    [StringLength(255)]
    public string? SubconType { get; set; }
    [StringLength(255)]
    public string DocStatus { get; set; } = null!;
}