using Imip.Ekb.Master.PortOfLoadings;
using Imip.Ekb.Master.PortOfLoadings.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class PortOfLoadingMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(PortOfLoading.Id), nameof(PortOfLoadingDto.Id))]
    public partial PortOfLoadingDto MapToDto(PortOfLoading entity);


    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(PortOfLoading.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(PortOfLoading.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(PortOfLoading.CreatedAt))]
    public partial void MapToEntity(PortOfLoadingCreateUpdateDto dto, PortOfLoading entity);

    // Custom mapping methods for complex scenarios
    public PortOfLoading CreateEntityWithId(PortOfLoadingCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (PortOfLoading)Activator.CreateInstance(typeof(PortOfLoading), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<PortOfLoadingDto> MapToDtoList(List<PortOfLoading> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<PortOfLoadingDto> MapToDtoEnumerable(IEnumerable<PortOfLoading> entities);
}