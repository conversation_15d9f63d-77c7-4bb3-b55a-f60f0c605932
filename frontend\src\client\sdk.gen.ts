// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from '@hey-api/client-fetch';
import type { GetApiAbpApplicationConfigurationData, GetApiAbpApplicationConfigurationResponses, GetApiAbpApplicationConfigurationErrors, GetApiAbpMultiTenancyTenantsByNameByNameData, GetApiAbpMultiTenancyTenantsByNameByNameResponses, GetApiAbpMultiTenancyTenantsByNameByNameErrors, GetApiAbpMultiTenancyTenantsByIdByIdData, GetApiAbpMultiTenancyTenantsByIdByIdResponses, GetApiAbpMultiTenancyTenantsByIdByIdErrors, PostApiAccountRegisterData, PostApiAccountRegisterResponses, PostApiAccountRegisterErrors, PostApiAccountSendPasswordResetCodeData, PostApiAccountSendPasswordResetCodeResponses, PostApiAccountSendPasswordResetCodeErrors, PostApiAccountVerifyPasswordResetTokenData, PostApiAccountVerifyPasswordResetTokenResponses, PostApiAccountVerifyPasswordResetTokenErrors, PostApiAccountResetPasswordData, PostApiAccountResetPasswordResponses, PostApiAccountResetPasswordErrors, PostApiMasterAgentFilterListData, PostApiMasterAgentFilterListResponses, GetApiMasterAgentData, GetApiMasterAgentResponses, PostApiMasterAgentData, PostApiMasterAgentResponses, DeleteApiMasterAgentByIdData, DeleteApiMasterAgentByIdResponses, GetApiMasterAgentByIdData, GetApiMasterAgentByIdResponses, PutApiMasterAgentByIdData, PutApiMasterAgentByIdResponses, GetApiAuthLogoutData, GetApiAuthLogoutResponses, GetApiEkbBcTypeData, GetApiEkbBcTypeResponses, GetApiEkbBcTypeErrors, PostApiEkbBcTypeData, PostApiEkbBcTypeResponses, PostApiEkbBcTypeErrors, DeleteApiEkbBcTypeByIdData, DeleteApiEkbBcTypeByIdResponses, DeleteApiEkbBcTypeByIdErrors, GetApiEkbBcTypeByIdData, GetApiEkbBcTypeByIdResponses, GetApiEkbBcTypeByIdErrors, PutApiEkbBcTypeByIdData, PutApiEkbBcTypeByIdResponses, PutApiEkbBcTypeByIdErrors, PostApiEkbBcTypeFilterListData, PostApiEkbBcTypeFilterListResponses, PostApiEkbBcTypeFilterListErrors, PostApiEkbBoundedZoneListData, PostApiEkbBoundedZoneListResponses, PostApiEkbBoundedZoneListErrors, GetApiEkbBoundedZoneData, GetApiEkbBoundedZoneResponses, GetApiEkbBoundedZoneErrors, PostApiEkbBoundedZoneData, PostApiEkbBoundedZoneResponses, PostApiEkbBoundedZoneErrors, DeleteApiEkbBoundedZoneByIdData, DeleteApiEkbBoundedZoneByIdResponses, DeleteApiEkbBoundedZoneByIdErrors, GetApiEkbBoundedZoneByIdData, GetApiEkbBoundedZoneByIdResponses, GetApiEkbBoundedZoneByIdErrors, PutApiEkbBoundedZoneByIdData, PutApiEkbBoundedZoneByIdResponses, PutApiEkbBoundedZoneByIdErrors, PostApiEkbBoundedZoneFilterListData, PostApiEkbBoundedZoneFilterListResponses, PostApiEkbBoundedZoneFilterListErrors, GetApiEkbBusinessPartnerData, GetApiEkbBusinessPartnerResponses, GetApiEkbBusinessPartnerErrors, PostApiEkbBusinessPartnerData, PostApiEkbBusinessPartnerResponses, PostApiEkbBusinessPartnerErrors, DeleteApiEkbBusinessPartnerByIdData, DeleteApiEkbBusinessPartnerByIdResponses, DeleteApiEkbBusinessPartnerByIdErrors, GetApiEkbBusinessPartnerByIdData, GetApiEkbBusinessPartnerByIdResponses, GetApiEkbBusinessPartnerByIdErrors, PutApiEkbBusinessPartnerByIdData, PutApiEkbBusinessPartnerByIdResponses, PutApiEkbBusinessPartnerByIdErrors, PostApiEkbBusinessPartnerFilterListData, PostApiEkbBusinessPartnerFilterListResponses, PostApiEkbBusinessPartnerFilterListErrors, GetApiEkbCargoData, GetApiEkbCargoResponses, GetApiEkbCargoErrors, PostApiEkbCargoData, PostApiEkbCargoResponses, PostApiEkbCargoErrors, DeleteApiEkbCargoByIdData, DeleteApiEkbCargoByIdResponses, DeleteApiEkbCargoByIdErrors, GetApiEkbCargoByIdData, GetApiEkbCargoByIdResponses, GetApiEkbCargoByIdErrors, PutApiEkbCargoByIdData, PutApiEkbCargoByIdResponses, PutApiEkbCargoByIdErrors, PostApiEkbCargoFilterListData, PostApiEkbCargoFilterListResponses, PostApiEkbCargoFilterListErrors, GetApiEkbDestinationPortData, GetApiEkbDestinationPortResponses, GetApiEkbDestinationPortErrors, PostApiEkbDestinationPortData, PostApiEkbDestinationPortResponses, PostApiEkbDestinationPortErrors, DeleteApiEkbDestinationPortByIdData, DeleteApiEkbDestinationPortByIdResponses, DeleteApiEkbDestinationPortByIdErrors, GetApiEkbDestinationPortByIdData, GetApiEkbDestinationPortByIdResponses, GetApiEkbDestinationPortByIdErrors, PutApiEkbDestinationPortByIdData, PutApiEkbDestinationPortByIdResponses, PutApiEkbDestinationPortByIdErrors, PostApiEkbDestinationPortFilterListData, PostApiEkbDestinationPortFilterListResponses, PostApiEkbDestinationPortFilterListErrors, PostApiAccountDynamicClaimsRefreshData, PostApiAccountDynamicClaimsRefreshResponses, PostApiAccountDynamicClaimsRefreshErrors, GetApiHealthKubernetesData, GetApiHealthKubernetesResponses, GetApiEkbItemClassificationData, GetApiEkbItemClassificationResponses, GetApiEkbItemClassificationErrors, PostApiEkbItemClassificationData, PostApiEkbItemClassificationResponses, PostApiEkbItemClassificationErrors, DeleteApiEkbItemClassificationByIdData, DeleteApiEkbItemClassificationByIdResponses, DeleteApiEkbItemClassificationByIdErrors, GetApiEkbItemClassificationByIdData, GetApiEkbItemClassificationByIdResponses, GetApiEkbItemClassificationByIdErrors, PutApiEkbItemClassificationByIdData, PutApiEkbItemClassificationByIdResponses, PutApiEkbItemClassificationByIdErrors, PostApiEkbItemClassificationFilterListData, PostApiEkbItemClassificationFilterListResponses, PostApiEkbItemClassificationFilterListErrors, GetApiEkbJettyData, GetApiEkbJettyResponses, GetApiEkbJettyErrors, PostApiEkbJettyData, PostApiEkbJettyResponses, PostApiEkbJettyErrors, DeleteApiEkbJettyByIdData, DeleteApiEkbJettyByIdResponses, DeleteApiEkbJettyByIdErrors, GetApiEkbJettyByIdData, GetApiEkbJettyByIdResponses, GetApiEkbJettyByIdErrors, PutApiEkbJettyByIdData, PutApiEkbJettyByIdResponses, PutApiEkbJettyByIdErrors, PostApiEkbJettyFilterListData, PostApiEkbJettyFilterListResponses, PostApiEkbJettyFilterListErrors, PostApiAccountLoginData, PostApiAccountLoginResponses, PostApiAccountLoginErrors, GetApiAccountLogoutData, GetApiAccountLogoutResponses, GetApiAccountLogoutErrors, PostApiAccountCheckPasswordData, PostApiAccountCheckPasswordResponses, PostApiAccountCheckPasswordErrors, GetApiEkbPortOfLoadingData, GetApiEkbPortOfLoadingResponses, GetApiEkbPortOfLoadingErrors, PostApiEkbPortOfLoadingData, PostApiEkbPortOfLoadingResponses, PostApiEkbPortOfLoadingErrors, DeleteApiEkbPortOfLoadingByIdData, DeleteApiEkbPortOfLoadingByIdResponses, DeleteApiEkbPortOfLoadingByIdErrors, GetApiEkbPortOfLoadingByIdData, GetApiEkbPortOfLoadingByIdResponses, GetApiEkbPortOfLoadingByIdErrors, PutApiEkbPortOfLoadingByIdData, PutApiEkbPortOfLoadingByIdResponses, PutApiEkbPortOfLoadingByIdErrors, PostApiEkbPortOfLoadingFilterListData, PostApiEkbPortOfLoadingFilterListResponses, PostApiEkbPortOfLoadingFilterListErrors, GetApiEkbPortServiceData, GetApiEkbPortServiceResponses, GetApiEkbPortServiceErrors, PostApiEkbPortServiceData, PostApiEkbPortServiceResponses, PostApiEkbPortServiceErrors, DeleteApiEkbPortServiceByIdData, DeleteApiEkbPortServiceByIdResponses, DeleteApiEkbPortServiceByIdErrors, GetApiEkbPortServiceByIdData, GetApiEkbPortServiceByIdResponses, GetApiEkbPortServiceByIdErrors, PutApiEkbPortServiceByIdData, PutApiEkbPortServiceByIdResponses, PutApiEkbPortServiceByIdErrors, PostApiEkbPortServiceFilterListData, PostApiEkbPortServiceFilterListResponses, PostApiEkbPortServiceFilterListErrors, GetApiAccountMyProfileData, GetApiAccountMyProfileResponses, GetApiAccountMyProfileErrors, PutApiAccountMyProfileData, PutApiAccountMyProfileResponses, PutApiAccountMyProfileErrors, PostApiAccountMyProfileChangePasswordData, PostApiAccountMyProfileChangePasswordResponses, PostApiAccountMyProfileChangePasswordErrors, GetApiEkbSurveyorData, GetApiEkbSurveyorResponses, GetApiEkbSurveyorErrors, PostApiEkbSurveyorData, PostApiEkbSurveyorResponses, PostApiEkbSurveyorErrors, DeleteApiEkbSurveyorByIdData, DeleteApiEkbSurveyorByIdResponses, DeleteApiEkbSurveyorByIdErrors, GetApiEkbSurveyorByIdData, GetApiEkbSurveyorByIdResponses, GetApiEkbSurveyorByIdErrors, PutApiEkbSurveyorByIdData, PutApiEkbSurveyorByIdResponses, PutApiEkbSurveyorByIdErrors, PostApiEkbSurveyorFilterListData, PostApiEkbSurveyorFilterListResponses, PostApiEkbSurveyorFilterListErrors, DeleteApiMultiTenancyTenantsByIdData, DeleteApiMultiTenancyTenantsByIdResponses, DeleteApiMultiTenancyTenantsByIdErrors, GetApiMultiTenancyTenantsByIdData, GetApiMultiTenancyTenantsByIdResponses, GetApiMultiTenancyTenantsByIdErrors, PutApiMultiTenancyTenantsByIdData, PutApiMultiTenancyTenantsByIdResponses, PutApiMultiTenancyTenantsByIdErrors, GetApiMultiTenancyTenantsData, GetApiMultiTenancyTenantsResponses, GetApiMultiTenancyTenantsErrors, PostApiMultiTenancyTenantsData, PostApiMultiTenancyTenantsResponses, PostApiMultiTenancyTenantsErrors, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, GetApiMultiTenancyTenantsByIdDefaultConnectionStringData, GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, PutApiMultiTenancyTenantsByIdDefaultConnectionStringData, PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, GetApiEkbTenantData, GetApiEkbTenantResponses, GetApiEkbTenantErrors, PostApiEkbTenantData, PostApiEkbTenantResponses, PostApiEkbTenantErrors, DeleteApiEkbTenantByIdData, DeleteApiEkbTenantByIdResponses, DeleteApiEkbTenantByIdErrors, GetApiEkbTenantByIdData, GetApiEkbTenantByIdResponses, GetApiEkbTenantByIdErrors, PutApiEkbTenantByIdData, PutApiEkbTenantByIdResponses, PutApiEkbTenantByIdErrors, PostApiEkbTenantFilterListData, PostApiEkbTenantFilterListResponses, PostApiEkbTenantFilterListErrors, GetApiTestAuthStatusData, GetApiTestAuthStatusResponses, GetApiTestTokenInfoData, GetApiTestTokenInfoResponses, PostApiEkbVesselVesselHeadersData, PostApiEkbVesselVesselHeadersResponses, PostApiEkbVesselVesselHeadersErrors, PostApiEkbVesselVesselItemsData, PostApiEkbVesselVesselItemsResponses, PostApiEkbVesselVesselItemsErrors, PostApiEkbVesselByIdVesselHeaderData, PostApiEkbVesselByIdVesselHeaderResponses, PostApiEkbVesselByIdVesselHeaderErrors, PostApiEkbVesselByIdVesselItemData, PostApiEkbVesselByIdVesselItemResponses, PostApiEkbVesselByIdVesselItemErrors } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

export const getApiAbpApplicationConfiguration = <ThrowOnError extends boolean = false>(options?: Options<GetApiAbpApplicationConfigurationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAbpApplicationConfigurationResponses, GetApiAbpApplicationConfigurationErrors, ThrowOnError>({
        url: '/api/abp/application-configuration',
        ...options
    });
};

export const getApiAbpMultiTenancyTenantsByNameByName = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpMultiTenancyTenantsByNameByNameData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAbpMultiTenancyTenantsByNameByNameResponses, GetApiAbpMultiTenancyTenantsByNameByNameErrors, ThrowOnError>({
        url: '/api/abp/multi-tenancy/tenants/by-name/{name}',
        ...options
    });
};

export const getApiAbpMultiTenancyTenantsByIdById = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpMultiTenancyTenantsByIdByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAbpMultiTenancyTenantsByIdByIdResponses, GetApiAbpMultiTenancyTenantsByIdByIdErrors, ThrowOnError>({
        url: '/api/abp/multi-tenancy/tenants/by-id/{id}',
        ...options
    });
};

export const postApiAccountRegister = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountRegisterData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountRegisterResponses, PostApiAccountRegisterErrors, ThrowOnError>({
        url: '/api/account/register',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountSendPasswordResetCode = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountSendPasswordResetCodeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountSendPasswordResetCodeResponses, PostApiAccountSendPasswordResetCodeErrors, ThrowOnError>({
        url: '/api/account/send-password-reset-code',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountVerifyPasswordResetToken = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountVerifyPasswordResetTokenData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountVerifyPasswordResetTokenResponses, PostApiAccountVerifyPasswordResetTokenErrors, ThrowOnError>({
        url: '/api/account/verify-password-reset-token',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountResetPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountResetPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountResetPasswordResponses, PostApiAccountResetPasswordErrors, ThrowOnError>({
        url: '/api/account/reset-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiMasterAgentFilterList = <ThrowOnError extends boolean = false>(options: Options<PostApiMasterAgentFilterListData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiMasterAgentFilterListResponses, unknown, ThrowOnError>({
        url: '/api/master/agent/filter-list',
        ...options
    });
};

export const getApiMasterAgent = <ThrowOnError extends boolean = false>(options?: Options<GetApiMasterAgentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiMasterAgentResponses, unknown, ThrowOnError>({
        url: '/api/master/agent',
        ...options
    });
};

export const postApiMasterAgent = <ThrowOnError extends boolean = false>(options: Options<PostApiMasterAgentData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiMasterAgentResponses, unknown, ThrowOnError>({
        url: '/api/master/agent',
        ...options
    });
};

export const deleteApiMasterAgentById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMasterAgentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMasterAgentByIdResponses, unknown, ThrowOnError>({
        url: '/api/master/agent/{id}',
        ...options
    });
};

export const getApiMasterAgentById = <ThrowOnError extends boolean = false>(options: Options<GetApiMasterAgentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMasterAgentByIdResponses, unknown, ThrowOnError>({
        url: '/api/master/agent/{id}',
        ...options
    });
};

export const putApiMasterAgentById = <ThrowOnError extends boolean = false>(options: Options<PutApiMasterAgentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiMasterAgentByIdResponses, unknown, ThrowOnError>({
        url: '/api/master/agent/{id}',
        ...options
    });
};

export const getApiAuthLogout = <ThrowOnError extends boolean = false>(options?: Options<GetApiAuthLogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAuthLogoutResponses, unknown, ThrowOnError>({
        url: '/api/Auth/logout',
        ...options
    });
};

export const getApiEkbBcType = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbBcTypeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbBcTypeResponses, GetApiEkbBcTypeErrors, ThrowOnError>({
        url: '/api/ekb/bc-type',
        ...options
    });
};

export const postApiEkbBcType = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBcTypeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBcTypeResponses, PostApiEkbBcTypeErrors, ThrowOnError>({
        url: '/api/ekb/bc-type',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbBcTypeById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbBcTypeByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbBcTypeByIdResponses, DeleteApiEkbBcTypeByIdErrors, ThrowOnError>({
        url: '/api/ekb/bc-type/{id}',
        ...options
    });
};

export const getApiEkbBcTypeById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbBcTypeByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbBcTypeByIdResponses, GetApiEkbBcTypeByIdErrors, ThrowOnError>({
        url: '/api/ekb/bc-type/{id}',
        ...options
    });
};

export const putApiEkbBcTypeById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbBcTypeByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbBcTypeByIdResponses, PutApiEkbBcTypeByIdErrors, ThrowOnError>({
        url: '/api/ekb/bc-type/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbBcTypeFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBcTypeFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBcTypeFilterListResponses, PostApiEkbBcTypeFilterListErrors, ThrowOnError>({
        url: '/api/ekb/bc-type/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbBoundedZoneList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneListResponses, PostApiEkbBoundedZoneListErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbBoundedZone = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbBoundedZoneData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbBoundedZoneResponses, GetApiEkbBoundedZoneErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone',
        ...options
    });
};

export const postApiEkbBoundedZone = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneResponses, PostApiEkbBoundedZoneErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbBoundedZoneById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbBoundedZoneByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbBoundedZoneByIdResponses, DeleteApiEkbBoundedZoneByIdErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/{id}',
        ...options
    });
};

export const getApiEkbBoundedZoneById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbBoundedZoneByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbBoundedZoneByIdResponses, GetApiEkbBoundedZoneByIdErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/{id}',
        ...options
    });
};

export const putApiEkbBoundedZoneById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbBoundedZoneByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbBoundedZoneByIdResponses, PutApiEkbBoundedZoneByIdErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbBoundedZoneFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneFilterListResponses, PostApiEkbBoundedZoneFilterListErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbBusinessPartner = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbBusinessPartnerData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbBusinessPartnerResponses, GetApiEkbBusinessPartnerErrors, ThrowOnError>({
        url: '/api/ekb/business-partner',
        ...options
    });
};

export const postApiEkbBusinessPartner = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBusinessPartnerData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBusinessPartnerResponses, PostApiEkbBusinessPartnerErrors, ThrowOnError>({
        url: '/api/ekb/business-partner',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbBusinessPartnerById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbBusinessPartnerByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbBusinessPartnerByIdResponses, DeleteApiEkbBusinessPartnerByIdErrors, ThrowOnError>({
        url: '/api/ekb/business-partner/{id}',
        ...options
    });
};

export const getApiEkbBusinessPartnerById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbBusinessPartnerByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbBusinessPartnerByIdResponses, GetApiEkbBusinessPartnerByIdErrors, ThrowOnError>({
        url: '/api/ekb/business-partner/{id}',
        ...options
    });
};

export const putApiEkbBusinessPartnerById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbBusinessPartnerByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbBusinessPartnerByIdResponses, PutApiEkbBusinessPartnerByIdErrors, ThrowOnError>({
        url: '/api/ekb/business-partner/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbBusinessPartnerFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBusinessPartnerFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBusinessPartnerFilterListResponses, PostApiEkbBusinessPartnerFilterListErrors, ThrowOnError>({
        url: '/api/ekb/business-partner/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbCargo = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbCargoData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbCargoResponses, GetApiEkbCargoErrors, ThrowOnError>({
        url: '/api/ekb/cargo',
        ...options
    });
};

export const postApiEkbCargo = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbCargoData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbCargoResponses, PostApiEkbCargoErrors, ThrowOnError>({
        url: '/api/ekb/cargo',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbCargoById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbCargoByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbCargoByIdResponses, DeleteApiEkbCargoByIdErrors, ThrowOnError>({
        url: '/api/ekb/cargo/{id}',
        ...options
    });
};

export const getApiEkbCargoById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbCargoByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbCargoByIdResponses, GetApiEkbCargoByIdErrors, ThrowOnError>({
        url: '/api/ekb/cargo/{id}',
        ...options
    });
};

export const putApiEkbCargoById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbCargoByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbCargoByIdResponses, PutApiEkbCargoByIdErrors, ThrowOnError>({
        url: '/api/ekb/cargo/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbCargoFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbCargoFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbCargoFilterListResponses, PostApiEkbCargoFilterListErrors, ThrowOnError>({
        url: '/api/ekb/cargo/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbDestinationPort = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbDestinationPortData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbDestinationPortResponses, GetApiEkbDestinationPortErrors, ThrowOnError>({
        url: '/api/ekb/destination-port',
        ...options
    });
};

export const postApiEkbDestinationPort = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDestinationPortData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbDestinationPortResponses, PostApiEkbDestinationPortErrors, ThrowOnError>({
        url: '/api/ekb/destination-port',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbDestinationPortById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbDestinationPortByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbDestinationPortByIdResponses, DeleteApiEkbDestinationPortByIdErrors, ThrowOnError>({
        url: '/api/ekb/destination-port/{id}',
        ...options
    });
};

export const getApiEkbDestinationPortById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbDestinationPortByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbDestinationPortByIdResponses, GetApiEkbDestinationPortByIdErrors, ThrowOnError>({
        url: '/api/ekb/destination-port/{id}',
        ...options
    });
};

export const putApiEkbDestinationPortById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbDestinationPortByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbDestinationPortByIdResponses, PutApiEkbDestinationPortByIdErrors, ThrowOnError>({
        url: '/api/ekb/destination-port/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbDestinationPortFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDestinationPortFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbDestinationPortFilterListResponses, PostApiEkbDestinationPortFilterListErrors, ThrowOnError>({
        url: '/api/ekb/destination-port/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountDynamicClaimsRefresh = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountDynamicClaimsRefreshData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountDynamicClaimsRefreshResponses, PostApiAccountDynamicClaimsRefreshErrors, ThrowOnError>({
        url: '/api/account/dynamic-claims/refresh',
        ...options
    });
};

export const getApiHealthKubernetes = <ThrowOnError extends boolean = false>(options?: Options<GetApiHealthKubernetesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiHealthKubernetesResponses, unknown, ThrowOnError>({
        url: '/api/health/kubernetes',
        ...options
    });
};

export const getApiEkbItemClassification = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbItemClassificationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbItemClassificationResponses, GetApiEkbItemClassificationErrors, ThrowOnError>({
        url: '/api/ekb/item-classification',
        ...options
    });
};

export const postApiEkbItemClassification = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbItemClassificationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbItemClassificationResponses, PostApiEkbItemClassificationErrors, ThrowOnError>({
        url: '/api/ekb/item-classification',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbItemClassificationById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbItemClassificationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbItemClassificationByIdResponses, DeleteApiEkbItemClassificationByIdErrors, ThrowOnError>({
        url: '/api/ekb/item-classification/{id}',
        ...options
    });
};

export const getApiEkbItemClassificationById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbItemClassificationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbItemClassificationByIdResponses, GetApiEkbItemClassificationByIdErrors, ThrowOnError>({
        url: '/api/ekb/item-classification/{id}',
        ...options
    });
};

export const putApiEkbItemClassificationById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbItemClassificationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbItemClassificationByIdResponses, PutApiEkbItemClassificationByIdErrors, ThrowOnError>({
        url: '/api/ekb/item-classification/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbItemClassificationFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbItemClassificationFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbItemClassificationFilterListResponses, PostApiEkbItemClassificationFilterListErrors, ThrowOnError>({
        url: '/api/ekb/item-classification/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbJetty = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbJettyData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbJettyResponses, GetApiEkbJettyErrors, ThrowOnError>({
        url: '/api/ekb/jetty',
        ...options
    });
};

export const postApiEkbJetty = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbJettyData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbJettyResponses, PostApiEkbJettyErrors, ThrowOnError>({
        url: '/api/ekb/jetty',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbJettyById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbJettyByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbJettyByIdResponses, DeleteApiEkbJettyByIdErrors, ThrowOnError>({
        url: '/api/ekb/jetty/{id}',
        ...options
    });
};

export const getApiEkbJettyById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbJettyByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbJettyByIdResponses, GetApiEkbJettyByIdErrors, ThrowOnError>({
        url: '/api/ekb/jetty/{id}',
        ...options
    });
};

export const putApiEkbJettyById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbJettyByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbJettyByIdResponses, PutApiEkbJettyByIdErrors, ThrowOnError>({
        url: '/api/ekb/jetty/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbJettyFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbJettyFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbJettyFilterListResponses, PostApiEkbJettyFilterListErrors, ThrowOnError>({
        url: '/api/ekb/jetty/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountLogin = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountLoginData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountLoginResponses, PostApiAccountLoginErrors, ThrowOnError>({
        url: '/api/account/login',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAccountLogout = <ThrowOnError extends boolean = false>(options?: Options<GetApiAccountLogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAccountLogoutResponses, GetApiAccountLogoutErrors, ThrowOnError>({
        url: '/api/account/logout',
        ...options
    });
};

export const postApiAccountCheckPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountCheckPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountCheckPasswordResponses, PostApiAccountCheckPasswordErrors, ThrowOnError>({
        url: '/api/account/check-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbPortOfLoading = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbPortOfLoadingData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbPortOfLoadingResponses, GetApiEkbPortOfLoadingErrors, ThrowOnError>({
        url: '/api/ekb/port-of-loading',
        ...options
    });
};

export const postApiEkbPortOfLoading = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbPortOfLoadingData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbPortOfLoadingResponses, PostApiEkbPortOfLoadingErrors, ThrowOnError>({
        url: '/api/ekb/port-of-loading',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbPortOfLoadingById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbPortOfLoadingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbPortOfLoadingByIdResponses, DeleteApiEkbPortOfLoadingByIdErrors, ThrowOnError>({
        url: '/api/ekb/port-of-loading/{id}',
        ...options
    });
};

export const getApiEkbPortOfLoadingById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbPortOfLoadingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbPortOfLoadingByIdResponses, GetApiEkbPortOfLoadingByIdErrors, ThrowOnError>({
        url: '/api/ekb/port-of-loading/{id}',
        ...options
    });
};

export const putApiEkbPortOfLoadingById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbPortOfLoadingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbPortOfLoadingByIdResponses, PutApiEkbPortOfLoadingByIdErrors, ThrowOnError>({
        url: '/api/ekb/port-of-loading/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbPortOfLoadingFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbPortOfLoadingFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbPortOfLoadingFilterListResponses, PostApiEkbPortOfLoadingFilterListErrors, ThrowOnError>({
        url: '/api/ekb/port-of-loading/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbPortService = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbPortServiceData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbPortServiceResponses, GetApiEkbPortServiceErrors, ThrowOnError>({
        url: '/api/ekb/port-service',
        ...options
    });
};

export const postApiEkbPortService = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbPortServiceData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbPortServiceResponses, PostApiEkbPortServiceErrors, ThrowOnError>({
        url: '/api/ekb/port-service',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbPortServiceById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbPortServiceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbPortServiceByIdResponses, DeleteApiEkbPortServiceByIdErrors, ThrowOnError>({
        url: '/api/ekb/port-service/{id}',
        ...options
    });
};

export const getApiEkbPortServiceById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbPortServiceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbPortServiceByIdResponses, GetApiEkbPortServiceByIdErrors, ThrowOnError>({
        url: '/api/ekb/port-service/{id}',
        ...options
    });
};

export const putApiEkbPortServiceById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbPortServiceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbPortServiceByIdResponses, PutApiEkbPortServiceByIdErrors, ThrowOnError>({
        url: '/api/ekb/port-service/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbPortServiceFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbPortServiceFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbPortServiceFilterListResponses, PostApiEkbPortServiceFilterListErrors, ThrowOnError>({
        url: '/api/ekb/port-service/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAccountMyProfile = <ThrowOnError extends boolean = false>(options?: Options<GetApiAccountMyProfileData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAccountMyProfileResponses, GetApiAccountMyProfileErrors, ThrowOnError>({
        url: '/api/account/my-profile',
        ...options
    });
};

export const putApiAccountMyProfile = <ThrowOnError extends boolean = false>(options?: Options<PutApiAccountMyProfileData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<PutApiAccountMyProfileResponses, PutApiAccountMyProfileErrors, ThrowOnError>({
        url: '/api/account/my-profile',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountMyProfileChangePassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountMyProfileChangePasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountMyProfileChangePasswordResponses, PostApiAccountMyProfileChangePasswordErrors, ThrowOnError>({
        url: '/api/account/my-profile/change-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbSurveyor = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbSurveyorData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbSurveyorResponses, GetApiEkbSurveyorErrors, ThrowOnError>({
        url: '/api/ekb/surveyor',
        ...options
    });
};

export const postApiEkbSurveyor = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbSurveyorData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbSurveyorResponses, PostApiEkbSurveyorErrors, ThrowOnError>({
        url: '/api/ekb/surveyor',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbSurveyorById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbSurveyorByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbSurveyorByIdResponses, DeleteApiEkbSurveyorByIdErrors, ThrowOnError>({
        url: '/api/ekb/surveyor/{id}',
        ...options
    });
};

export const getApiEkbSurveyorById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbSurveyorByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbSurveyorByIdResponses, GetApiEkbSurveyorByIdErrors, ThrowOnError>({
        url: '/api/ekb/surveyor/{id}',
        ...options
    });
};

export const putApiEkbSurveyorById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbSurveyorByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbSurveyorByIdResponses, PutApiEkbSurveyorByIdErrors, ThrowOnError>({
        url: '/api/ekb/surveyor/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbSurveyorFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbSurveyorFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbSurveyorFilterListResponses, PostApiEkbSurveyorFilterListErrors, ThrowOnError>({
        url: '/api/ekb/surveyor/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMultiTenancyTenantsByIdResponses, DeleteApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options
    });
};

export const getApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<GetApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsByIdResponses, GetApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options
    });
};

export const putApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<PutApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiMultiTenancyTenantsByIdResponses, PutApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(options?: Options<GetApiMultiTenancyTenantsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsResponses, GetApiMultiTenancyTenantsErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants',
        ...options
    });
};

export const postApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(options?: Options<PostApiMultiTenancyTenantsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiMultiTenancyTenantsResponses, PostApiMultiTenancyTenantsErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const getApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<GetApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const putApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<PutApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const getApiEkbTenant = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbTenantData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbTenantResponses, GetApiEkbTenantErrors, ThrowOnError>({
        url: '/api/ekb/tenant',
        ...options
    });
};

export const postApiEkbTenant = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTenantData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbTenantResponses, PostApiEkbTenantErrors, ThrowOnError>({
        url: '/api/ekb/tenant',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbTenantById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbTenantByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbTenantByIdResponses, DeleteApiEkbTenantByIdErrors, ThrowOnError>({
        url: '/api/ekb/tenant/{id}',
        ...options
    });
};

export const getApiEkbTenantById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbTenantByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbTenantByIdResponses, GetApiEkbTenantByIdErrors, ThrowOnError>({
        url: '/api/ekb/tenant/{id}',
        ...options
    });
};

export const putApiEkbTenantById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbTenantByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbTenantByIdResponses, PutApiEkbTenantByIdErrors, ThrowOnError>({
        url: '/api/ekb/tenant/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbTenantFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTenantFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbTenantFilterListResponses, PostApiEkbTenantFilterListErrors, ThrowOnError>({
        url: '/api/ekb/tenant/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiTestAuthStatus = <ThrowOnError extends boolean = false>(options?: Options<GetApiTestAuthStatusData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTestAuthStatusResponses, unknown, ThrowOnError>({
        url: '/api/test/auth-status',
        ...options
    });
};

export const getApiTestTokenInfo = <ThrowOnError extends boolean = false>(options?: Options<GetApiTestTokenInfoData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTestTokenInfoResponses, unknown, ThrowOnError>({
        url: '/api/test/token-info',
        ...options
    });
};

export const postApiEkbVesselVesselHeaders = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbVesselVesselHeadersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbVesselVesselHeadersResponses, PostApiEkbVesselVesselHeadersErrors, ThrowOnError>({
        url: '/api/ekb/vessel/vessel-headers',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbVesselVesselItems = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbVesselVesselItemsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbVesselVesselItemsResponses, PostApiEkbVesselVesselItemsErrors, ThrowOnError>({
        url: '/api/ekb/vessel/vessel-items',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbVesselByIdVesselHeader = <ThrowOnError extends boolean = false>(options: Options<PostApiEkbVesselByIdVesselHeaderData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiEkbVesselByIdVesselHeaderResponses, PostApiEkbVesselByIdVesselHeaderErrors, ThrowOnError>({
        url: '/api/ekb/vessel/{id}/vessel-header',
        ...options
    });
};

export const postApiEkbVesselByIdVesselItem = <ThrowOnError extends boolean = false>(options: Options<PostApiEkbVesselByIdVesselItemData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiEkbVesselByIdVesselItemResponses, PostApiEkbVesselByIdVesselItemErrors, ThrowOnError>({
        url: '/api/ekb/vessel/{id}/vessel-item',
        ...options
    });
};