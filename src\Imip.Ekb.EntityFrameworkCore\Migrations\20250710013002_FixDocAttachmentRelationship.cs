﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class FixDocAttachmentRelationship : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.DropForeignKey(
            //     name: "FK_M_Doc_attachment_T_MDOC_ZoneDetailDocEntry",
            //     table: "M_Doc_attachment");

            // migrationBuilder.DropIndex(
            //     name: "IX_M_Doc_attachment_ZoneDetailDocEntry",
            //     table: "M_Doc_attachment");

            // migrationBuilder.DropColumn(
            //     name: "ZoneDetailDocEntry",
            //     table: "M_Doc_attachment");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.AddColumn<int>(
            //     name: "ZoneDetailDocEntry",
            //     table: "M_Doc_attachment",
            //     type: "int",
            //     nullable: true);

            // migrationBuilder.CreateIndex(
            //     name: "IX_M_Doc_attachment_ZoneDetailDocEntry",
            //     table: "M_Doc_attachment",
            //     column: "ZoneDetailDocEntry");

            // migrationBuilder.AddForeignKey(
            //     name: "FK_M_Doc_attachment_T_MDOC_ZoneDetailDocEntry",
            //     table: "M_Doc_attachment",
            //     column: "ZoneDetailDocEntry",
            //     principalTable: "T_MDOC",
            //     principalColumn: "DocEntry");
        }
    }
}
