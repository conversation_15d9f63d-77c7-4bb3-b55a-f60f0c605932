# Copilot Coding Agent Instructions for Imip.Ekb

## Architecture Overview
- **Layered DDD Monolith**: Solution follows Domain-Driven Design with clear separation: `Domain`, `Application`, `EntityFrameworkCore`, `Web`, and `DbMigrator` projects. See `README.md` for details.
- **ABP Framework**: Uses ABP modules for modularity, dependency injection, permission management, background jobs, and OpenIddict-based authentication.
- **Frontend**: `frontend/` is a Vite+React+TypeScript SPA, communicating with the backend via OpenAPI/Swagger-generated clients.
- **Blob Storage**: Custom SFTP blob provider in `src/Imip.Ekb.Domain/BlobStorage/` (see its README for config and usage).
- **Kubernetes**: `k8s/` contains deployment manifests for dev/prod, with GitLab CI/CD integration.

## Developer Workflows
- **Build**: Use `dotnet build Imip.Ekb.sln` (or VS Code tasks: `build`, `publish`, `watch`).
- **Migrations**: Run `Imip.Ekb.DbMigrator` to apply DB migrations and seed data.
- **Frontend**: In `frontend/`, use `pnpm install` and `pnpm dev` for local dev.
- **K8s Deploy**: See `k8s/README.md` for manual and CI/CD deployment steps.
- **Certificates**: For OpenIddict, generate `openiddict.pfx` as described in root `README.md`.

## Project-Specific Patterns & Conventions
- **ABP Module System**: Each major project is an ABP module. Register services and configuration in the module class (e.g., `EkbWebModule`).
- **HttpClient + Polly**: Use `AddHttpClient<T>().AddPolicyHandler(...)` for resilient HTTP calls (see `EkbWebModule`).
- **Custom Middleware**: API error handling, CORS, and token refresh are handled via custom middleware in `Imip.Ekb.Web/Middleware`.
- **Navigation/UI**: Menus and toolbars are contributed via `EkbMenuContributor` and `EkbToolbarContributor`.
- **AutoMapper**: Use ABP's AutoMapper integration; configure in module classes.
- **Blob Storage**: Use `IBlobContainer`/`IBlobContainerFactory` for file operations; see SFTP provider docs.
- **Testing**: Tests are in `test/` subfolders, using xUnit and Moq.

## Integration Points
- **External APIs**: Use typed `HttpClient` with Polly for retries/circuit-breakers.
- **Authentication**: OpenIddict with certificates; see `README.md` and K8s secrets.
- **CI/CD**: GitLab pipelines for build/deploy; see `.gitlab-ci.yml` and `k8s/README.md`.

## Key Files/Directories
- `src/Imip.Ekb.Web/EkbWebModule.cs`: Main web module config (services, middleware, policies)
- `src/Imip.Ekb.Domain/BlobStorage/`: SFTP blob provider
- `frontend/`: SPA client
- `k8s/`: Kubernetes manifests
- `test/`: Automated tests

## Special Conventions
- **Use ABP's dependency injection and module system for all service registration/configuration.**
- **Frontend code must use TypeScript and follow the rules in `.cursor/rules/frontend.mdc`.**
- **Backend code must follow .NET conventions in `.cursor/rules/abp-rule.mdc`.**
- **Do not hardcode secrets; use configuration and K8s secrets.**

---

For more, see the root `README.md`, `k8s/README.md`, and module-level docs. When in doubt, follow ABP and .NET best practices unless a local convention overrides.
