using Imip.Ekb.BoundedZone.ImportVessels.ZoneDetailInvoice;
using Imip.Ekb.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.Ekb.Repositories;
public class ZoneDetailInvoiceRepository : EfCoreRepository<EkbDbContext, ZoneDetailInvoice, Guid>, IZoneDetailInvoiceRepository
{
    public ZoneDetailInvoiceRepository(IDbContextProvider<EkbDbContext> dbContextProvider) : base(dbContextProvider) { }

    public virtual async Task<IQueryable<ZoneDetailInvoice>> GetQueryableWithIncludesAsync()
    {
        return (await GetQueryableAsync())
            .AsNoTracking()
            .Include(x => x.ZoneDetail!)
                .ThenInclude(z => z.ImportVessel!);
    }

    public virtual async Task<IQueryable<ZoneDetailInvoice>> GetQueryableListAsync()
    {
        return (await GetQueryableAsync())
            .AsNoTracking()
            .Include(x => x.ZoneDetail!);
    }
}