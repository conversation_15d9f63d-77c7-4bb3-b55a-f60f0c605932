﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.BoundedZone.ImportVessels.ZoneDetailInvoice.ZoneDetailInvoiceItem;
public class ZoneDetailInvoiceItem : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    public int? DocNum { get; set; }

    [StringLength(255)]
    public string? ItemCode { get; set; }

    public string? ItemName { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? Qty { get; set; }

    [StringLength(255)]
    public string? UoM { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? UnitPrice { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? LineTotal { get; set; }

    [StringLength(255)]
    public string? HsCode { get; set; }

    [StringLength(255)]
    public string? ContractNo { get; set; }

    [Column("Created_by")]
    [StringLength(255)]
    public string CreatedBy { get; set; } = null!;

    [Column("Updated_by")]
    [StringLength(255)]
    public string? UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(20)]
    public string? Flags { get; set; }

    [Column("isScan")]
    [StringLength(10)]
    public string IsScan { get; set; } = null!;

    [Column("isOriginal")]
    [StringLength(10)]
    public string IsOriginal { get; set; } = null!;

    [Column("isSend")]
    [StringLength(10)]
    public string IsSend { get; set; } = null!;

    [Column("isFeOri")]
    [StringLength(10)]
    public string IsFeOri { get; set; } = null!;

    [Column("isFeSend")]
    [StringLength(10)]
    public string IsFeSend { get; set; } = null!;

    [StringLength(100)]
    public string? MatchKey { get; set; }

    public int? Category { get; set; }

    [StringLength(255)]
    public string? CategoryDesc { get; set; }

    [StringLength(10)]
    public string HasSub { get; set; } = null!;

    [StringLength(255)]
    public string? SecretKey { get; set; }

    [StringLength(10)]
    public string Deleted { get; set; } = null!;

    [Column("ItemNameSAP")]
    [StringLength(255)]
    public string? ItemNameSap { get; set; }

    [StringLength(255)]
    public string? FrgnName { get; set; }

    [StringLength(250)]
    public string? Specification { get; set; }

    [StringLength(200)]
    public string? ParentKey { get; set; }

    [Column("BLKey")]
    public int? Blkey { get; set; }

    public int? InvKey { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? DeletedAt { get; set; }

    [StringLength(200)]
    public string? DeleteBy { get; set; }

    [StringLength(250)]
    public string? MaterialNumber { get; set; }

    [StringLength(100)]
    public string? OpenDate { get; set; }

    [StringLength(100)]
    public string? UpdateDate { get; set; }

    [Column("BM", TypeName = "numeric(20, 4)")]
    public decimal? Bm { get; set; }

    [Column("PPN", TypeName = "numeric(20, 4)")]
    public decimal? Ppn { get; set; }

    [Column("PPH", TypeName = "numeric(20, 4)")]
    public decimal? Pph { get; set; }

    [Column("BMAD", TypeName = "decimal(20, 4)")]
    public decimal? Bmad { get; set; }

    [Column("BMTP", TypeName = "decimal(20, 4)")]
    public decimal? Bmtp { get; set; }

    [StringLength(255)]
    public string? FormType { get; set; }

    [StringLength(255)]
    public string DocType { get; set; } = null!;

    [StringLength(255)]
    public string? UniCode { get; set; }

    [Column("SKBPPN")]
    [StringLength(10)]
    public string Skbppn { get; set; } = null!;

    public short TempImport { get; set; }

    public string? Remark { get; set; }

    public string? Damage { get; set; }

    public string? Improvement { get; set; }

    public long? TemporaryExportId { get; set; }
}
