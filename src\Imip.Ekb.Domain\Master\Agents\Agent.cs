﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.ExportVessels;
using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.BoundedZone.LocalVessels;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.Agents;

[Table("M_Agent")]
public class Agent : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    [StringLength(10)]
    public string Status { get; set; } = null!;

    [StringLength(255)]
    public string? Type { get; set; }

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(100)]
    public string? NpwpNo { get; set; }

    [Column("BDM_SAPCode")]
    [StringLength(255)]
    public string? BdmSapcode { get; set; }

    [StringLength(255)]
    public string? TaxCode { get; set; }

    public string? AddressNpwp { get; set; }

    public string? Address { get; set; }

    [Column("SAPCodeS4")]
    [StringLength(255)]
    public string? SapcodeS4 { get; set; }

    public ICollection<LocalVessel>? LocalVessels { get; set; }
    public ICollection<ExportVessel>? ExportVessels { get; set; }
    public ICollection<ImportVessel>? ImportVessels { get; set; }
    public ICollection<ZoneDetail>? VesselTransactions { get; set; }
}
