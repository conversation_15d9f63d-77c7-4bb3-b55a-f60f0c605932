﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.PriceListPortServices;

[Table("MPLPS")]
public class PriceListPortService : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    public DateOnly PeriodStart { get; set; }

    public DateOnly PeriodEnd { get; set; }

    [Column("PSKey")]
    public int Pskey { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal Price { get; set; }

    public int Currency { get; set; }

    public int CreatedBy { get; set; }

    public int UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    public int? TenantKey { get; set; }

    [StringLength(50)]
    public string Type { get; set; } = null!;

    [StringLength(255)]
    public string? ItemName { get; set; }

    [StringLength(255)]
    public string? BillingType { get; set; }
}
