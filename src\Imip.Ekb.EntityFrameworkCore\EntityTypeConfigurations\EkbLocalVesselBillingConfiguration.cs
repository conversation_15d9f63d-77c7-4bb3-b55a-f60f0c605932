using Imip.Ekb.Billing.LocalVesselBillings;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;
public class EkbLocalVesselBillingConfiguration : IEntityTypeConfiguration<LocalVesselBilling>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbLocalVesselBillingConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<LocalVesselBilling> b)
    {
        b.ToTable("BHLOCAL", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        b.HasOne(x => x.MasterJetty)
            .WithMany(z => z.LocalVesselBillings)
            .HasForeignKey(x => x.JettyId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);


        b.<PERSON>(x => x.Items)
            .WithOne(x => x.LocalVesselBilling)
            // .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);
    }
}
