using Imip.Ekb.BoundedZone.LocalVessels;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbLocalVesselConfiguration : IEntityTypeConfiguration<LocalVessel>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbLocalVesselConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<LocalVessel> b)
    {
        b.ToTable("L_master", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        // Configure integer properties that might be stored as bigint in database
        b.Property(x => x.VesselName)
            .HasColumnType("bigint");

        b.Property(x => x.CreatedBy)
            .HasColumnType("bigint");

        b.Property(x => x.UpdatedBy)
            .HasColumnType("bigint");

        b.Property(x => x.Jetty)
            .HasColumnType("bigint");
        b.Property(x => x.AgentId)
            .HasColumnType("bigint");
        b.Property(x => x.SurveyorId)
            .HasColumnType("bigint");
        b.Property(x => x.TradingId)
            .HasColumnType("bigint");

        b.HasOne(x => x.MasterJetty)
            .WithMany(z => z.LocalVessels)
            .HasForeignKey(x => x.JettyId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.Vessel)
            .WithMany(z => z.LocalVessels)
            .HasForeignKey(x => x.VesselId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.Barge)
           .WithMany(z => z.LocalBarges)
           .HasForeignKey(x => x.BargeId)
           .HasPrincipalKey(z => z.Id)
           .IsRequired(false)
           .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterAgent)
           .WithMany(x => x.LocalVessels)
           .HasForeignKey(x => x.MasterAgentId)
           .HasPrincipalKey(z => z.Id)
           .IsRequired(false)
           .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterTrading)
           .WithMany(x => x.LocalVessels)
           .HasForeignKey(x => x.MasterTradingId)
           .HasPrincipalKey(z => z.Id)
           .IsRequired(false)
           .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterSurveyor)
           .WithMany(x => x.LocalVessels)
           .HasForeignKey(x => x.MasterSurveyorId)
           .HasPrincipalKey(z => z.Id)
           .IsRequired(false)
           .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterPortOrigin)
          .WithMany(x => x.LocalVessels)
          .HasForeignKey(x => x.PortOriginId)
          .HasPrincipalKey(z => z.Id)
          .IsRequired(false)
          .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterDestinationPort)
          .WithMany(x => x.LocalVessels)
          .HasForeignKey(x => x.DestinationPortId)
          .HasPrincipalKey(z => z.Id)
          .IsRequired(false)
          .OnDelete(DeleteBehavior.NoAction);

        // Configure Items collection using HeaderId as foreign key (not creating columns on vessel side)
        b.HasMany(x => x.Items)
            .WithOne(x => x.LocalVessel)
            .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasIndex(z => z.DocType);
        b.HasIndex(z => z.TransType);
    }
}
