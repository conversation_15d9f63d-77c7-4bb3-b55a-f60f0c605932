﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.BoundedZone.ImportVessels.ZoneDetailInvoice;

[Table("T_MDOC_bl")]
public class ZoneDetailInvoice : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    public int? DocNum { get; set; }

    [Column("No_inv")]
    [StringLength(255)]
    public string? NoInv { get; set; }

    [Column("Date_inv")]
    public DateOnly? DateInv { get; set; }

    [StringLength(255)]
    public string? Vendor { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? Value { get; set; }

    [Column("Created_by")]
    [StringLength(255)]
    public string CreatedBy { get; set; } = null!;

    [Column("Updated_by")]
    [StringLength(255)]
    public string? UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(20)]
    public string? Flags { get; set; }

    public string? ItemName { get; set; }

    [Column(TypeName = "decimal(15, 4)")]
    public decimal Qty { get; set; }

    [StringLength(50)]
    public string DocType { get; set; } = null!;

    [StringLength(50)]
    public string TransType { get; set; } = null!;

    [Column("isScan")]
    [StringLength(10)]
    public string IsScan { get; set; } = null!;

    [Column("isOriginal")]
    [StringLength(10)]
    public string IsOriginal { get; set; } = null!;

    [Column("isSend")]
    [StringLength(10)]
    public string IsSend { get; set; } = null!;

    [Column("isFeOri")]
    [StringLength(10)]
    public string IsFeOri { get; set; } = null!;

    [Column("isFeSend")]
    [StringLength(10)]
    public string IsFeSend { get; set; } = null!;

    [StringLength(100)]
    public string? SecretKey { get; set; }

    [StringLength(20)]
    public string? Currency { get; set; }

    [StringLength(10)]
    public string? FormE { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? ItemQty { get; set; }

    [StringLength(20)]
    public string? UnitQty { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? GrossWeight { get; set; }

    [StringLength(20)]
    public string? UnitWeight { get; set; }

    [StringLength(100)]
    public string? MatchKey { get; set; }

    [StringLength(10)]
    public string Deleted { get; set; } = null!;

    [StringLength(200)]
    public string? ParentKey { get; set; }

    [Column("BLKey")]
    public int? Blkey { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? DeletedAt { get; set; }

    [StringLength(200)]
    public string? DeleteBy { get; set; }

    [StringLength(100)]
    public string? OpenDate { get; set; }

    [StringLength(100)]
    public string? UpdateDate { get; set; }

    [StringLength(255)]
    public string? PackingList { get; set; }

    [Column("FOB", TypeName = "decimal(20, 4)")]
    public decimal? Fob { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? CostOfRepair { get; set; }

    [StringLength(255)]
    public string? InvoiceImportNo { get; set; }

    public DateOnly? InvoiceImportDate { get; set; }

    public Guid? ZoneDetailId { get; set; }

    // Navigation property
    public virtual ZoneDetail? ZoneDetail { get; set; }
}
