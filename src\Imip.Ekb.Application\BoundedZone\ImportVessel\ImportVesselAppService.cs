using ImportVesselEntity = Imip.Ekb.BoundedZone.ImportVessels.ImportVessel;
using Imip.Ekb.BoundedZone.ImportVessels.Dtos;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp;
using Imip.Ekb.BoundedZone.ImportVessel;
using Imip.Ekb.Master.Dtos;
using Microsoft.Extensions.Configuration;
using Imip.Ekb.Master.PortOfLoadings.Dtos;
using Imip.Ekb.Master.DestinationPorts.Dtos;

namespace Imip.Ekb.Application.BoundedZone.ImportVessel;

[Authorize]
public class ImportVesselAppService :
    CrudAppService<
        ImportVesselEntity,
        ImportVesselDto,
        Guid,
        QueryParametersDto,
        CreateUpdateImportVesselDto>,
    IImportVesselAppService
{
    private readonly IImportVesselRepository _importVesselRepository;
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly ImportVesselMapper _importVesselMapper;
    private readonly VesselMapper _vesselMapper;
    private readonly IConfiguration _configuration;
    private readonly ZoneDetailMapper _zoneDetailMapper;
    private readonly ILogger<ImportVesselAppService> _logger;

    public ImportVesselAppService(
        IImportVesselRepository importVesselRepository,
        IZoneDetailRepository zoneDetailRepository,
        ImportVesselMapper importVesselMapper,
        VesselMapper vesselMapper,
        IConfiguration configuration,
        ZoneDetailMapper zoneDetailMapper,
        ILogger<ImportVesselAppService> logger)
        : base(importVesselRepository)
    {
        _configuration = configuration;
        _importVesselRepository = importVesselRepository;
        _zoneDetailRepository = zoneDetailRepository;
        _importVesselMapper = importVesselMapper;
        _vesselMapper = vesselMapper;
        _zoneDetailMapper = zoneDetailMapper;
        _logger = logger;
    }

    public override async Task<PagedResultDto<ImportVesselDto>> GetListAsync(QueryParametersDto input)
    {
        await CheckGetListPolicyAsync();

        var query = await _importVesselRepository.GetQueryableWithIncludesAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            query = query.OrderBy(input.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);

        var entities = await AsyncExecuter.ToListAsync(
            query.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(MapToGetListOutputDto).ToList();

        return new PagedResultDto<ImportVesselDto>(totalCount, dtos);
    }



    public async Task<int> GenerateNextDocNumAsync(DateTime postDate)
    {
        var prefix = postDate.ToString("yyMM");
        var queryable = await _importVesselRepository.GetQueryableAsync();
        var maxDocNum = queryable
            .Where(x => x.DocNum.ToString().StartsWith(prefix) && x.DocType == EkbConsts.ImportVesselType.Import)
            .OrderByDescending(x => x.DocNum)
            .Select(x => x.DocNum)
            .FirstOrDefault();

        int nextIncrement = 1;
        if (maxDocNum != 0)
        {
            var lastIncrementStr = maxDocNum.ToString().Length > 4 ? maxDocNum.ToString().Substring(4, 4) : "0000";
            if (int.TryParse(lastIncrementStr, out var lastIncrement))
            {
                nextIncrement = lastIncrement + 1;
            }
        }
        var docNum = int.Parse($"{prefix}{nextIncrement:D4}");
        return docNum;
    }

    public override async Task<ImportVesselDto> CreateAsync(CreateUpdateImportVesselDto input)
    {
        await CheckCreatePolicyAsync();

        // Generate DocNum
        var dt = input.PostingDate ?? DateOnly.FromDateTime(DateTime.Now);
        input.DocNum = await GenerateNextDocNumAsync(dt.ToDateTime(TimeOnly.MinValue));

        // Set DocType for Import vessel
        input.DocType = EkbConsts.ImportVesselType.Import;

        var entity = _importVesselMapper.CreateEntityWithId(input, Guid.NewGuid());

        // Set CreatedBy from authenticated user
        entity.CreatedBy = CurrentUser.UserName ?? "System";

        await _importVesselRepository.InsertAsync(entity, autoSave: true);

        // Set HeaderId for each item after entity is inserted (if needed)
        if (input.Items != null && input.Items.Count != 0)
        {
            foreach (var itemDto in input.Items)
            {
                // If HeaderId is Guid, assign entity.Id
                itemDto.HeaderId = entity.Id;
                itemDto.DocNum = entity.DocEntry;
                itemDto.DocType = EkbConsts.ImportVesselType.Import;

                var itemEntity = _zoneDetailMapper.CreateEntityWithId(itemDto, Guid.NewGuid());
                // Set CreatedBy from authenticated user on detail
                itemEntity.CreatedBy = CurrentUser.UserName ?? "System";
                // Ensure required fields are set
                itemEntity.IsUrgent = string.IsNullOrEmpty(itemEntity.IsUrgent) ? "N" : itemEntity.IsUrgent;
                itemEntity.IsScan = string.IsNullOrEmpty(itemEntity.IsScan) ? "N" : itemEntity.IsScan;
                itemEntity.IsOriginal = string.IsNullOrEmpty(itemEntity.IsOriginal) ? "N" : itemEntity.IsOriginal;
                itemEntity.IsSend = string.IsNullOrEmpty(itemEntity.IsSend) ? "N" : itemEntity.IsSend;
                itemEntity.IsFeOri = string.IsNullOrEmpty(itemEntity.IsFeOri) ? "N" : itemEntity.IsFeOri;
                itemEntity.IsFeSend = string.IsNullOrEmpty(itemEntity.IsFeSend) ? "N" : itemEntity.IsFeSend;
                itemEntity.IsChange = string.IsNullOrEmpty(itemEntity.IsChange) ? "N" : itemEntity.IsChange;
                itemEntity.Deleted = string.IsNullOrEmpty(itemEntity.Deleted) ? "N" : itemEntity.Deleted;
                itemEntity.DocType = string.IsNullOrEmpty(itemEntity.DocType) ? "Import" : itemEntity.DocType;
                await _zoneDetailRepository.InsertAsync(itemEntity, autoSave: false);
            }
        }

        // Save all changes in a single transaction
        await CurrentUnitOfWork.SaveChangesAsync();

        // Load the entity with includes for proper mapping
        var createdEntity = await _importVesselRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(createdEntity);
    }

    public override async Task<ImportVesselDto> UpdateAsync(Guid id, CreateUpdateImportVesselDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _importVesselRepository.GetAsync(id);
        _importVesselMapper.MapToEntity(input, entity);

        await _importVesselRepository.UpdateAsync(entity, autoSave: false);


        // Handle items update - update existing items and add new ones
        if (input.Items != null)
        {
            // Get existing items
            var existingItems = await _zoneDetailRepository.GetByHeaderIdAsync(id);

            // Process each item in the input
            for (int i = 0; i < input.Items.Count; i++)
            {
                var itemDto = input.Items[i];
                itemDto.HeaderId = id;

                if (i < existingItems.Count)
                {
                    // Update existing item
                    var existingItem = existingItems[i];
                    _zoneDetailMapper.MapToEntity(itemDto, existingItem);
                    await _zoneDetailRepository.UpdateAsync(existingItem, autoSave: false);
                }
                else
                {
                    // If HeaderId is Guid, assign entity.Id
                    itemDto.HeaderId = entity.Id;
                    itemDto.DocNum = entity.DocEntry;
                    itemDto.DocType = EkbConsts.ImportVesselType.Import;

                    var itemEntity = _zoneDetailMapper.CreateEntityWithId(itemDto, Guid.NewGuid());
                    // Set CreatedBy from authenticated user on detail
                    itemEntity.CreatedBy = CurrentUser.UserName ?? "System";
                    // Ensure required fields are set
                    itemEntity.IsUrgent = string.IsNullOrEmpty(itemEntity.IsUrgent) ? "N" : itemEntity.IsUrgent;
                    itemEntity.IsScan = string.IsNullOrEmpty(itemEntity.IsScan) ? "N" : itemEntity.IsScan;
                    itemEntity.IsOriginal = string.IsNullOrEmpty(itemEntity.IsOriginal) ? "N" : itemEntity.IsOriginal;
                    itemEntity.IsSend = string.IsNullOrEmpty(itemEntity.IsSend) ? "N" : itemEntity.IsSend;
                    itemEntity.IsFeOri = string.IsNullOrEmpty(itemEntity.IsFeOri) ? "N" : itemEntity.IsFeOri;
                    itemEntity.IsFeSend = string.IsNullOrEmpty(itemEntity.IsFeSend) ? "N" : itemEntity.IsFeSend;
                    itemEntity.IsChange = string.IsNullOrEmpty(itemEntity.IsChange) ? "N" : itemEntity.IsChange;
                    itemEntity.Deleted = string.IsNullOrEmpty(itemEntity.Deleted) ? "N" : itemEntity.Deleted;
                    itemEntity.DocType = string.IsNullOrEmpty(itemEntity.DocType) ? "Import" : itemEntity.DocType;
                    await _zoneDetailRepository.InsertAsync(itemEntity, autoSave: false);
                }
            }

            // Remove excess existing items if input has fewer items
            for (int i = input.Items.Count; i < existingItems.Count; i++)
            {
                await _zoneDetailRepository.DeleteAsync(existingItems[i], autoSave: false);
            }
        }

        // Save all changes in a single transaction
        await CurrentUnitOfWork.SaveChangesAsync();

        // Load the entity with includes for proper mapping
        var updatedEntity = await _importVesselRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(updatedEntity);
    }

    public override async Task<ImportVesselDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _importVesselRepository.GetQueryableWithItemsSplitAsync(id);

        if (entity == null)
        {
            throw new UserFriendlyException("LocalVessel not found");
        }

        // Map the items from the navigation property
        var items = entity.Items?.Select(zoneDetail =>
        {
            var itemDto = _vesselMapper.MapZoneDetailToItemDtoWithType(zoneDetail, "LocalVessel");

            // Use navigation property for Tenant
            if (zoneDetail.Tenant != null)
            {
                itemDto.Tenant = _vesselMapper.MapTenantToTenantShortDto(zoneDetail.Tenant);
            }

            // Map DocAttachments if available
            if (zoneDetail.DocAttachment != null && zoneDetail.DocAttachment.Any())
            {
                itemDto.Attachments = zoneDetail.DocAttachment.Select(att => _vesselMapper.MapDocAttachmentToDto(att)).ToList();
            }
            else
            {
                itemDto.Attachments = new List<DocAttachmentSortDto>();
            }

            return itemDto;
        }).ToList() ?? new List<VesselItemDto>();

        return _importVesselMapper.MapToDtoWithItems(entity, items);
    }

    private IQueryable<ImportVesselEntity> ApplyDynamicQuery(IQueryable<ImportVesselEntity> query, QueryParametersDto parameters)
    {
        // Apply filters if provided
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ImportVesselEntity>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ImportVesselEntity>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }

    protected override ImportVesselDto MapToGetOutputDto(ImportVesselEntity entity)
    {
        return _importVesselMapper.MapToDto(entity);
    }

    protected override ImportVesselDto MapToGetListOutputDto(ImportVesselEntity entity)
    {
        return _importVesselMapper.MapToDto(entity);
    }

    protected override ImportVesselEntity MapToEntity(CreateUpdateImportVesselDto createInput)
    {
        return _importVesselMapper.MapToEntity(createInput);
    }

    protected override void MapToEntity(CreateUpdateImportVesselDto updateInput, ImportVesselEntity entity)
    {
        _importVesselMapper.MapToEntity(updateInput, entity);
    }

    public async Task<ImportVesselWithItemsDto> GetWithItemsAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var vesselWithItems = await _importVesselRepository.GetQueryableWithItemsSplitAsync(id);

        if (vesselWithItems == null)
        {
            throw new UserFriendlyException("ImportVessel not found");
        }

        var dto = new ImportVesselWithItemsDto
        {
            Id = vesselWithItems.Id,
            ConcurrencyStamp = vesselWithItems.ConcurrencyStamp,
            DocEntry = vesselWithItems.DocEntry,
            DocNum = vesselWithItems.DocNum,
            PostingDate = vesselWithItems.PostingDate,
            Bp = vesselWithItems.Bp,
            VesselName = vesselWithItems.Vessel != null ? vesselWithItems.Vessel.Name : null,
            Shipment = vesselWithItems.Shipment,
            ShipmentNo = vesselWithItems.ShipmentNo,
            VesselArrival = vesselWithItems.VesselArrival,
            CreatedBy = vesselWithItems.CreatedBy,
            UpdatedBy = vesselWithItems.UpdatedBy,
            CreatedAt = vesselWithItems.CreatedAt,
            UpdatedAt = vesselWithItems.UpdatedAt,
            Color = vesselWithItems.Color,
            Flags = vesselWithItems.Flags,
            Remarks = vesselWithItems.Remarks,
            Status = vesselWithItems.Status,
            IsLocked = vesselWithItems.IsLocked,
            IsChange = vesselWithItems.IsChange,
            TransType = vesselWithItems.TransType,
            DocType = vesselWithItems.DocType,
            BcType = vesselWithItems.BcType,
            PortOrigin = vesselWithItems.PortOrigin,
            EmailToPpjk = vesselWithItems.EmailToPpjk,
            MatchKey = vesselWithItems.MatchKey,
            Voyage = vesselWithItems.Voyage,
            Deleted = vesselWithItems.Deleted,
            DocStatus = vesselWithItems.DocStatus,
            GrossWeight = vesselWithItems.GrossWeight,
            VesselFlag = vesselWithItems.VesselFlag,
            VesselDeparture = vesselWithItems.VesselDeparture,
            VesselStatus = vesselWithItems.VesselStatus,
            Jetty = vesselWithItems.Jetty,
            DestinationPort = vesselWithItems.DestinationPort,
            BerthingDate = vesselWithItems.BerthingDate,
            AnchorageDate = vesselWithItems.AnchorageDate,
            Type = vesselWithItems.Type,
            JettyUpdate = vesselWithItems.JettyUpdate,
            ReportDate = vesselWithItems.ReportDate,
            UnloadingDate = vesselWithItems.UnloadingDate,
            FinishUnloadingDate = vesselWithItems.FinishUnloadingDate,
            GrtWeight = vesselWithItems.GrtWeight,
            InvoiceStatus = vesselWithItems.InvoiceStatus,
            AgentId = vesselWithItems.AgentId,
            AgentName = vesselWithItems.AgentName,
            StatusBms = vesselWithItems.StatusBms,
            SurveyorId = vesselWithItems.SurveyorId,
            TradingId = vesselWithItems.TradingId,
            JettyId = vesselWithItems.JettyId,
            VesselId = vesselWithItems.VesselId,
            MasterAgentId = vesselWithItems.MasterAgentId,
            MasterTradingId = vesselWithItems.MasterTradingId,
            MasterSurveyorId = vesselWithItems.MasterSurveyorId,
            AsideDate = vesselWithItems.AsideDate,
            CastOfDate = vesselWithItems.CastOfDate,
            PortOriginId = vesselWithItems.PortOriginId,
            DestinationPortId = vesselWithItems.DestinationPortId,
            MasterJetty = vesselWithItems.MasterJetty == null ? null : new Master.Jetties.Dtos.JettyDto
            {
                Id = vesselWithItems.MasterJetty.Id,
                Name = vesselWithItems.MasterJetty.Name,
                Alias = vesselWithItems.MasterJetty.Alias,
                Max = vesselWithItems.MasterJetty.Max,
                Port = vesselWithItems.MasterJetty.Port,
                IsCustomArea = vesselWithItems.MasterJetty.IsCustomArea,
            },
            Vessel = vesselWithItems.Vessel == null ? null : new Master.Cargos.Dtos.CargoDto
            {
                Id = vesselWithItems.Vessel.Id,
                Name = vesselWithItems.Vessel.Name,
                Alias = vesselWithItems.Vessel.Alias,
                Flag = vesselWithItems.Vessel.Flag,
                GrossWeight = vesselWithItems.Vessel.GrossWeight,
                Type = vesselWithItems.Vessel.Type,
                LoaQty = vesselWithItems.Vessel.LoaQty,
            },
            MasterAgent = vesselWithItems.MasterAgent == null ? null : new Master.Agents.Dtos.AgentDto
            {
                Id = vesselWithItems.MasterAgent.Id,
                Name = vesselWithItems.MasterAgent.Name,
                Status = vesselWithItems.MasterAgent.Status,
                Type = vesselWithItems.MasterAgent.Type,
                NpwpNo = vesselWithItems.MasterAgent.NpwpNo,
                BdmSapcode = vesselWithItems.MasterAgent.BdmSapcode,
                TaxCode = vesselWithItems.MasterAgent.TaxCode,
                AddressNpwp = vesselWithItems.MasterAgent.AddressNpwp,
                Address = vesselWithItems.MasterAgent.Address,
                SapcodeS4 = vesselWithItems.MasterAgent.SapcodeS4,
            },
            MasterTrading = vesselWithItems.MasterTrading == null ? null : new Master.Tradings.Dtos.TradingDto
            {
                Id = vesselWithItems.MasterTrading.Id,
                Name = vesselWithItems.MasterTrading.Name,
                Address = vesselWithItems.MasterTrading.Address,
                Npwp = vesselWithItems.MasterTrading.Npwp,
            },
            MasterSurveyor = vesselWithItems.MasterSurveyor == null ? null : new Master.Surveyors.Dtos.SurveyorDto
            {
                Id = vesselWithItems.MasterSurveyor.Id,
                Name = vesselWithItems.MasterSurveyor.Name,
                Address = vesselWithItems.MasterSurveyor.Address,
                Npwp = vesselWithItems.MasterSurveyor.Npwp,
            },
            MasterPortOrigin = vesselWithItems.MasterPortOrigin == null ? null : new PortOfLoadingDto
            {
                Id = vesselWithItems.MasterPortOrigin.Id,
                Name = vesselWithItems.MasterPortOrigin.Name,
                DocType = vesselWithItems.MasterPortOrigin.DocType,
            },
            MasterDestinationPort = vesselWithItems.MasterDestinationPort == null ? null : new DestinationPortDto
            {
                Id = vesselWithItems.MasterDestinationPort.Id,
                Name = vesselWithItems.MasterDestinationPort.Name,
                DocType = vesselWithItems.MasterDestinationPort.DocType,
            },
            Items = vesselWithItems.Items?.Select(zoneDetail => new VesselItemDto
            {
                Id = zoneDetail.Id,
                ConcurrencyStamp = zoneDetail.ConcurrencyStamp,
                DocEntry = zoneDetail.DocEntry,
                DocNum = zoneDetail.DocNum ?? 0,
                TenantName = zoneDetail.Tenant != null ? zoneDetail.Tenant.Name : null,
                ItemName = zoneDetail.ItemName,
                ItemQty = zoneDetail.ItemQty,
                UnitQty = zoneDetail.UnitQty,
                Cargo = zoneDetail.Cargo,
                Shipment = zoneDetail.Shipment,
                Remarks = zoneDetail.Remarks,
                NoBl = zoneDetail.NoBl,
                DateBl = zoneDetail.DateBl,
                AjuNo = zoneDetail.AjuNo,
                RegNo = zoneDetail.RegNo,
                RegDate = zoneDetail.RegDate,
                SppbNo = zoneDetail.SppbNo,
                SppbDate = zoneDetail.SppbDate,
                SppdNo = zoneDetail.SppdNo,
                SppdDate = zoneDetail.SppdDate,
                GrossWeight = zoneDetail.GrossWeight,
                UnitWeight = zoneDetail.UnitWeight,
                HeaderId = zoneDetail.HeaderId,
                LetterNo = zoneDetail.LetterNo,
                DocType = zoneDetail.DocType,
                VesselType = "ImportVessel",
                ShippingInstructionNo = zoneDetail.ShippingInstructionNo,
                ShippingInstructionDate = zoneDetail.ShippingInstructionDate,
                RegType = zoneDetail.RegType,
                Status = zoneDetail.Status,
                LetterDate = zoneDetail.LetterDate,
                TenantId = zoneDetail.TenantId,
                BcTypeId = zoneDetail.BcTypeId,
                BusinessPartnerId = zoneDetail.BusinessPartnerId,
                AgentId = zoneDetail.AgentId,
                MasterExportClassificationId = zoneDetail.MasterExportClassificationId,
                Item = zoneDetail.Item,
                Tenant = zoneDetail.Tenant == null ? null : new TenantShortDto
                {
                    Id = zoneDetail.Tenant.Id,
                    Name = zoneDetail.Tenant.Name,
                    FullName = zoneDetail.Tenant.FullName,
                    DocEntry = zoneDetail.Tenant.DocEntry,
                    Npwp = zoneDetail.Tenant.Npwp,
                    Address = zoneDetail.Tenant.Address,
                    Nib = zoneDetail.Tenant.Nib,
                    Phone = zoneDetail.Tenant.Phone,
                    NoAndDateNotaris = zoneDetail.Tenant.NoAndDateNotaris,
                    DescNotaris = zoneDetail.Tenant.DescNotaris,
                },
                BcType = zoneDetail.BcType == null ? null : new BcTypeShortDto
                {
                    Id = zoneDetail.BcType.Id,
                    Type = zoneDetail.BcType.Type,
                    TransName = zoneDetail.BcType.TransName,
                },
                BusinessPartner = zoneDetail.BusinessPartner == null ? null : new BusinessPartnerShortDto
                {
                    Id = zoneDetail.BusinessPartner.Id,
                    Name = zoneDetail.BusinessPartner.Name,
                    Npwp = zoneDetail.BusinessPartner.Npwp,
                    Nitku = zoneDetail.BusinessPartner.Nitku,
                    DocEntry = zoneDetail.BusinessPartner.DocEntry
                },
                MasterAgent = zoneDetail.MasterAgent == null ? null : new AgentShortDto
                {
                    Id = zoneDetail.MasterAgent.Id,
                    Name = zoneDetail.MasterAgent.Name,
                    Type = zoneDetail.MasterAgent.Type,
                },
                Attachments = zoneDetail.DocAttachment != null ? zoneDetail.DocAttachment.Select(att => new DocAttachmentSortDto
                {
                    Id = att.Id,
                    DocType = att.DocType,
                    TransType = att.TransType,
                    Description = att.Description,
                    BlobName = att.BlobName,
                    ReferenceId = att.ReferenceId,
                    FileName = att.FileName,
                    TabName = att.TabName,
                    StreamUrl = att.FilePath,
                    TypePa = att.TypePa
                }).ToList() : new List<DocAttachmentSortDto>()
            }).ToList() ?? new List<VesselItemDto>()
        };

        return dto;
    }

    private string GetFileStreamUrl(string? path)
    {
        // Get the base URL from configuration
        var baseUrl = _configuration["App:ServerRootAddress"]?.TrimEnd('/') ?? "";

        // Return the URL
        return $"{baseUrl}/api/filestream/stream/ekb/{path}";
    }

    public async Task<PagedResultDto<ImportVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters)
    {
        await CheckGetListPolicyAsync();

        var entityQuery = await _importVesselRepository.GetQueryableWithIncludesAsync();
        entityQuery = ApplyDynamicQuery(entityQuery, parameters);

        // Define the projection selector
        System.Linq.Expressions.Expression<Func<ImportVesselEntity, ImportVesselProjectionDto>> selector = x => new ImportVesselProjectionDto
        {
            Id = x.Id,
            ConcurrencyStamp = x.ConcurrencyStamp,
            DocEntry = x.DocEntry,
            DocNum = x.DocNum,
            PostingDate = x.PostingDate,
            VesselName = x.Vessel != null ? x.Vessel.Name : null,
            Shipment = x.Shipment,
            VesselArrival = x.VesselArrival,
            VesselDeparture = x.VesselDeparture,
            PortOrigin = x.PortOrigin,
            DestinationPort = x.DestinationPort,
            Voyage = x.Voyage,
            GrossWeight = x.GrossWeight,
            DocStatus = x.DocStatus,
            Status = x.Status,
            Remarks = x.Remarks,
            DocType = x.DocType,
            TransType = x.TransType,
            BerthingDate = x.BerthingDate,
            AnchorageDate = x.AnchorageDate,
            ReportDate = x.ReportDate,
            UnloadingDate = x.UnloadingDate,
            FinishUnloadingDate = x.FinishUnloadingDate,
            GrtWeight = x.GrtWeight,
            InvoiceStatus = x.InvoiceStatus,
            StatusBms = x.StatusBms,
            JettyId = x.JettyId,
            VesselId = x.VesselId,
            MasterAgentId = x.MasterAgentId,
            MasterTradingId = x.MasterTradingId,
            MasterSurveyorId = x.MasterSurveyorId,
            AsideDate = x.AsideDate,
            CastOfDate = x.CastOfDate,
            PortOriginId = x.PortOriginId,
            DestinationPortId = x.DestinationPortId,
            MasterAgent = x.MasterAgent == null ? null : new AgentProjectionDto
            {
                Id = x.MasterAgent.Id,
                Name = x.MasterAgent.Name,
                Status = x.MasterAgent.Status,
                Type = x.MasterAgent.Type,
                NpwpNo = x.MasterAgent.NpwpNo,
                BdmSapcode = x.MasterAgent.BdmSapcode,
                TaxCode = x.MasterAgent.TaxCode,
                AddressNpwp = x.MasterAgent.AddressNpwp,
                Address = x.MasterAgent.Address,
                SapcodeS4 = x.MasterAgent.SapcodeS4,
            },
            MasterTrading = x.MasterTrading == null ? null : new TradingProjectionDto
            {
                Id = x.MasterTrading.Id,
                Name = x.MasterTrading.Name,
                Address = x.MasterTrading.Address,
                Npwp = x.MasterTrading.Npwp,
                IsActive = x.MasterTrading.IsActive,
            },
            MasterSurveyor = x.MasterSurveyor == null ? null : new SurveyorProjectionDto
            {
                Id = x.MasterSurveyor.Id,
                Name = x.MasterSurveyor.Name,
                Address = x.MasterSurveyor.Address,
                Npwp = x.MasterSurveyor.Npwp,
                IsActive = x.MasterSurveyor.IsActive,
            },
            MasterJetty = x.MasterJetty == null ? null : new JettyProjectionDto
            {
                Id = x.MasterJetty.Id,
                Name = x.MasterJetty.Name,
                Alias = x.MasterJetty.Alias,
                Max = x.MasterJetty.Max,
                Port = x.MasterJetty.Port,
                IsCustomArea = x.MasterJetty.IsCustomArea,
                Deleted = x.MasterJetty.Deleted,
                DocEntry = x.MasterJetty.DocEntry,
            },
            Vessel = x.Vessel == null ? null : new CargoProjectionDto
            {
                Id = x.Vessel.Id,
                Name = x.Vessel.Name,
                Alias = x.Vessel.Alias,
                Flag = x.Vessel.Flag,
                GrossWeight = x.Vessel.GrossWeight,
                Type = x.Vessel.Type,
                LoaQty = x.Vessel.LoaQty,
                Status = x.Vessel.Status,
                DocEntry = x.Vessel.DocEntry,
            },
            MasterPortOrigin = x.MasterPortOrigin == null ? null : new PortOfOriginProjectionDto
            {
                Id = x.MasterPortOrigin.Id,
                Name = x.MasterPortOrigin.Name,
                DocType = x.MasterPortOrigin.DocType,
            },
            MasterDestinationPort = x.MasterDestinationPort == null ? null : new DestinationPortProjectionDto
            {
                Id = x.MasterDestinationPort.Id,
                Name = x.MasterDestinationPort.Name,
                DocType = x.MasterDestinationPort.DocType,
            },
        };

        var projectedQuery = entityQuery.Select(selector);

        var totalCount = await AsyncExecuter.CountAsync(projectedQuery);
        var items = await AsyncExecuter.ToListAsync(
            projectedQuery.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        return new PagedResultDto<ImportVesselProjectionDto>
        {
            TotalCount = totalCount,
            Items = items
        };
    }
}