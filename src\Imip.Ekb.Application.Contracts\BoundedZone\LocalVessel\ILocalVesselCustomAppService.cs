using System;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.BoundedZone.LocalVessels;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.BoundedZone.LocalVessel;

public interface ILocalVesselCustomAppService
{
    Task<PagedResultDto<LocalVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters);
    Task<LocalVesselWithItemsDto> GetWithItemsAsync(Guid id);
}