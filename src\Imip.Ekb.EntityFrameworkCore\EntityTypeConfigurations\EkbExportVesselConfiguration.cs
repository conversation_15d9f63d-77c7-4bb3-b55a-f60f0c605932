using Imip.Ekb.BoundedZone.ExportVessels;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbExportVesselConfiguration : IEntityTypeConfiguration<ExportVessel>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbExportVesselConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<ExportVessel> b)
    {
        b.ToTable("THEXP", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        b.HasOne(x => x.MasterJetty)
            .WithMany(z => z.ExportVessels)
            .HasForeignKey(x => x.JettyId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.<PERSON>(x => x.Vessel)
            .WithMany(z => z.ExportVessels)
            .HasForeignKey(x => x.VesselId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterAgent)
           .WithMany(x => x.ExportVessels)
           .HasForeignKey(x => x.MasterAgentId)
           .HasPrincipalKey(z => z.Id)
           .IsRequired(false)
           .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterTrading)
           .WithMany(x => x.ExportVessels)
           .HasForeignKey(x => x.MasterTradingId)
           .HasPrincipalKey(z => z.Id)
           .IsRequired(false)
           .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterSurveyor)
           .WithMany(x => x.ExportVessels)
           .HasForeignKey(x => x.MasterSurveyorId)
           .HasPrincipalKey(z => z.Id)
           .IsRequired(false)
           .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterPortOrigin)
          .WithMany(x => x.ExportVessels)
          .HasForeignKey(x => x.PortOriginId)
          .HasPrincipalKey(z => z.Id)
          .IsRequired(false)
          .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterDestinationPort)
          .WithMany(x => x.ExportVessels)
          .HasForeignKey(x => x.DestinationPortId)
          .HasPrincipalKey(z => z.Id)
          .IsRequired(false)
          .OnDelete(DeleteBehavior.NoAction);

        // Configure Items collection using HeaderId as foreign key (not creating columns on vessel side)
        b.HasMany(x => x.Items)
            .WithOne(x => x.ExportVessel)
            .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);


        b.Property(x => x.AgentId)
            .HasColumnType("bigint");
        b.Property(x => x.SurveyorId)
            .HasColumnType("bigint");
        b.Property(x => x.TradingId)
            .HasColumnType("bigint");

        b.HasIndex(z => z.DocType);
    }
}
