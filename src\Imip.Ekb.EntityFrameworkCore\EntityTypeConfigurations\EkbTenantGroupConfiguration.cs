using Imip.Ekb.Master.Tenants;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;


namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbTenantGroupConfiguration : IEntityTypeConfiguration<TenantGroup>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbTenantGroupConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<TenantGroup> b)
    {
        b.ToTable("MasterGroup", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions
    }
}