using Imip.Ekb.BoundedZone.TradingVessels.TradingInvoice;
using Imip.Ekb.BoundedZone.Dtos;
using Riok.Mapperly.Abstractions;
using System;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class TradingInvoiceMapper : IMapperlyMapper
{
    [MapProperty(nameof(TradingInvoice.Id), nameof(TradingInvoiceDto.Id))]
    [MapProperty(nameof(TradingInvoice.DocEntry), nameof(TradingInvoiceDto.DocEntry))]
    public partial TradingInvoiceDto MapToDto(TradingInvoice entity);

    [MapperIgnoreTarget(nameof(TradingInvoice.Id))]
    [MapperIgnoreTarget(nameof(TradingInvoice.DocEntry))]
    public partial void MapToEntity(CreateUpdateTradingInvoiceDto dto, TradingInvoice entity);

    [MapperIgnoreTarget(nameof(TradingInvoice.Id))]
    [MapperIgnoreTarget(nameof(TradingInvoice.DocEntry))]
    public partial TradingInvoice MapToEntity(CreateUpdateTradingInvoiceDto dto);

    public partial System.Collections.Generic.List<TradingInvoiceDto> MapToDtoList(System.Collections.Generic.List<TradingInvoice> entities);
    public partial System.Collections.Generic.IEnumerable<TradingInvoiceDto> MapToDtoEnumerable(System.Collections.Generic.IEnumerable<TradingInvoice> entities);
}