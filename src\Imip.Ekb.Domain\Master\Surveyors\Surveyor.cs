﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.BoundedZone.ExportVessels;
using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.BoundedZone.LocalVessels;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.Surveyors;
[Table("master_surveyors")]
public class Surveyor : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public long DocEntry { get; set; }

    [Column("name")]
    [StringLength(255)]
    public string Name { get; set; } = null!;

    [Column("address")]
    public string? Address { get; set; }

    [Column("npwp")]
    [StringLength(255)]
    public string? Npwp { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [Column("is_active")]
    [StringLength(5)]
    public string IsActive { get; set; } = null!;

    [Column("created_by")]
    public long CreatedBy { get; set; }

    [Column("updated_by")]
    public long? UpdatedBy { get; set; }

    public ICollection<LocalVessel>? LocalVessels { get; set; }
    public ICollection<ExportVessel>? ExportVessels { get; set; }
    public ICollection<ImportVessel>? ImportVessels { get; set; }
}