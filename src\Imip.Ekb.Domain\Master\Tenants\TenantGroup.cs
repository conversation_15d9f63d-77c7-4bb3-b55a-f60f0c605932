using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.Tenants;

[Table("MasterGroup")]
public class TenantGroup : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public long DocEntry { get; set; }

    [StringLength(255)]
    public string GroupName { get; set; } = null!;

    public long CreatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(50)]
    public string Status { get; set; } = null!;
}
