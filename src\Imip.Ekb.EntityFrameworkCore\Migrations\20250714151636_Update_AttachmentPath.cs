﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class Update_AttachmentPath : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Import (ImportDetails)
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/IMPORT/' + 
                    ISNULL(CAST(iv.DocNum AS VARCHAR), 'unknown') + '/' +
                    ISNULL(
                        UPPER(REPLACE(REPLACE(REPLACE(ISNULL(zd.No_bl, zd.BL_No), ' ', '_'), '-', '_'), '/', '_')),
                        'nobl'
                    ) + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC zd ON att.ReferenceId = zd.Id
                INNER JOIN T_MDOC_Header iv ON zd.HeaderId = iv.Id
                WHERE att.DocType = 'Import'
                AND att.TransType = 'ImportDetails'
                ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/TR/' + 
                    ISNULL(CAST(iv.DocNo AS VARCHAR), 'unknown') + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC zd ON att.ReferenceId = zd.Id
                INNER JOIN R_Master iv ON zd.HeaderId = iv.Id
                WHERE att.DocType = 'Trading'
                AND att.TransType = 'Detail'
                ");

            // Import (ImportDetails Inv)
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/IMPORT/' + 
                    ISNULL(CAST(head.DocNum AS VARCHAR), 'unknown') + '/' +
                    ISNULL(
                        UPPER(REPLACE(REPLACE(REPLACE(ISNULL(zd.No_bl, zd.BL_No), ' ', '_'), '-', '_'), '/', '_')),
                        'nobl'
                    ) + '/' +
                    ISNULL(CAST(iv.No_inv AS VARCHAR), 'unknown')
                     + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC_Bl iv ON att.ReferenceId = iv.Id
                INNER JOIN T_MDOC zd ON iv.ZoneDetailId = zd.Id
                INNER JOIN T_MDOC_Header head ON zd.HeaderId = head.Id
                WHERE att.DocType = 'Import'
                AND att.TransType = 'Inv'
                ");

            // Local (detail)
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/LOCAL/' + ISNULL(CAST(lv.DocNum AS VARCHAR), 'unknown')
                     + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC zd ON att.ReferenceId = zd.Id
                INNER JOIN L_Master lv ON zd.HeaderId = lv.Id
                WHERE att.DocType = 'Local'
                AND att.TransType = 'detail'
                ");

            // BillingImport
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/BillingImport/' + CAST(ib.DocNum AS VARCHAR)
                     + '/' + name
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                LEFT JOIN BHIMP ib ON bi.HeaderId = ib.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingImport'
                ");

            // BillingExport
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/BillingExport/' + CAST(eb.DocNum AS VARCHAR)
                     + '/' + name
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                LEFT JOIN BHEXP eb ON bi.HeaderId = eb.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingExport'
                ");

            // BillingLocalIN
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/BillingLocalIN/' + CAST(lvb.DocNum AS VARCHAR)
                     + '/' + name
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                LEFT JOIN BHLOCAL lvb ON bi.HeaderId = lvb.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingLocalIN'
                ");

            // BillingLocalOUT
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/BillingLocalOUT/' + CAST(lvb.DocNum AS VARCHAR)
                     + '/' + name
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                LEFT JOIN BHLOCAL lvb ON bi.HeaderId = lvb.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingLocalOUT'
                ");

            // BillingDetail
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/BILLING/BillingDetail/' + ISNULL(CAST(bi.DocEntry AS VARCHAR), ISNULL(CAST(att.DocEntry AS VARCHAR), 'unknown'))
                     + '/' + name
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingDetail'
                ");

            // BillingOther
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/BILLING/BillingOther/' + 
                    CASE
                        WHEN bi.Type = 'Import' THEN (select CAST(DocNum as VARCHAR) from BHIMP where Id = bi.HeaderId)
                        WHEN bi.Type = 'Export' THEN (select CAST(DocNum as VARCHAR) from BHEXP where Id = bi.HeaderId)
                        ELSE (select CAST(DocNum as VARCHAR) from BHLOCAL where Id = bi.HeaderId)
                    END
                     + '/' + name
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingOther'
                ");

            // Fallback for any not set
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                    SET Path = 'docs/MONITORING'  + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC zd ON att.ReferenceId = zd.Id
                WHERE att.DocType = 'Import'
                AND att.TransType = 'ImportDetails'
                AND zd.DocNum IS NULL
                ");


            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT t.Id FROM doc_check_errors t WHERE M_Doc_attachment.M_doc_key = t.DocEntry)
                WHERE DocType = 'Common' AND TransType = 'ErrorCheck';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                    SET Path = 'docs/ErrorCheck'  + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN doc_check_errors zd ON att.ReferenceId = zd.Id
                WHERE att.DocType = 'Common'
                AND att.TransType = 'ErrorCheck'
                ");


            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT t.Id FROM notuls t WHERE M_Doc_attachment.M_doc_key = t.DocEntry)
                WHERE DocType = 'Import' AND TransType = 'Notul';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                    SET Path = 'docs/NOTUL'  + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN notuls zd ON att.ReferenceId = zd.Id
                WHERE att.DocType = 'Import'
                AND att.TransType = 'Notul'
                ");


            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT t.Id FROM M_Tenant t WHERE M_Doc_attachment.M_doc_key = t.DocEntry)
                WHERE DocType = 'MasterTenant';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                    SET Path = 'docs/TENANT'  + '/' + '/' + zd.Name + '/' + att.TransType  + '/'  + att.name
                FROM M_Doc_attachment att
                INNER JOIN M_Tenant zd ON att.ReferenceId = zd.Id
                WHERE att.DocType = 'MasterTenant'
                ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 'docs/DEFAULT'  + '/' + name
                FROM M_Doc_attachment
                WHERE Path IS NULL OR Path = ''
                ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
