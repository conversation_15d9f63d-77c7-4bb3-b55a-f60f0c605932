using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.Ekb.Repositories;
public class LocalVesselBillingRepository : EfCoreRepository<EkbDbContext, LocalVesselBilling, Guid>, ILocalVesselBillingRepository
{
    public LocalVesselBillingRepository(IDbContextProvider<EkbDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

    public virtual async Task<IQueryable<LocalVesselBilling>> GetQueryableWithIncludesAsync()
    {
        return (await GetQueryableAsync())
            .AsNoTracking()
            .Include(x => x.MasterJetty)
            .Where(x => !x.IsDeleted);
    }

    public virtual async Task<int> CountAsync()
    {
        var query = await GetQueryableAsync();
        return await query.Where(x => !x.IsDeleted).CountAsync();
    }
}