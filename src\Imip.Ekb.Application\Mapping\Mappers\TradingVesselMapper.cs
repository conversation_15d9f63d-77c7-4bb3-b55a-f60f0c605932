using Imip.Ekb.BoundedZone.TradingVessels;
using Imip.Ekb.BoundedZone.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class TradingVesselMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(TradingVessel.Id), nameof(TradingVesselDto.Id))]
    [MapProperty(nameof(TradingVessel.DocEntry), nameof(TradingVesselDto.DocEntry))]
    [MapProperty(nameof(TradingVessel.DocNum), nameof(TradingVesselDto.DocNum))]
    [MapProperty(nameof(TradingVessel.Tenant), nameof(TradingVesselDto.Tenant))]
    [MapProperty(nameof(TradingVessel.Bp), nameof(TradingVesselDto.Bp))]
    [MapProperty(nameof(TradingVessel.PostDate), nameof(TradingVesselDto.PostDate))]
    [MapProperty(nameof(TradingVessel.Contract), nameof(TradingVesselDto.Contract))]
    [MapProperty(nameof(TradingVessel.Bc), nameof(TradingVesselDto.Bc))]
    [MapProperty(nameof(TradingVessel.CreatedBy), nameof(TradingVesselDto.CreatedBy))]
    [MapProperty(nameof(TradingVessel.UpdatedBy), nameof(TradingVesselDto.UpdatedBy))]
    [MapProperty(nameof(TradingVessel.CreatedAt), nameof(TradingVesselDto.CreatedAt))]
    [MapProperty(nameof(TradingVessel.UpdatedAt), nameof(TradingVesselDto.UpdatedAt))]
    [MapProperty(nameof(TradingVessel.Deleted), nameof(TradingVesselDto.Deleted))]
    [MapProperty(nameof(TradingVessel.Status), nameof(TradingVesselDto.Status))]
    [MapProperty(nameof(TradingVessel.Remark), nameof(TradingVesselDto.Remark))]
    [MapProperty(nameof(TradingVessel.ContractDate), nameof(TradingVesselDto.ContractDate))]
    [MapProperty(nameof(TradingVessel.SubconType), nameof(TradingVesselDto.SubconType))]
    [MapProperty(nameof(TradingVessel.DocStatus), nameof(TradingVesselDto.DocStatus))]
    public partial TradingVesselDto MapToDto(TradingVessel entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(TradingVessel.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(TradingVessel.DocEntry))] // Don't change existing DocEntry
    [MapperIgnoreTarget(nameof(TradingVessel.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(TradingVessel.CreatedAt))]
    [MapperIgnoreTarget(nameof(TradingVessel.CreationTime))]
    [MapperIgnoreTarget(nameof(TradingVessel.CreatorId))]
    [MapperIgnoreTarget(nameof(TradingVessel.LastModificationTime))]
    [MapperIgnoreTarget(nameof(TradingVessel.LastModifierId))]
    [MapperIgnoreTarget(nameof(TradingVessel.IsDeleted))]
    [MapperIgnoreTarget(nameof(TradingVessel.DeleterId))]
    [MapperIgnoreTarget(nameof(TradingVessel.DeletionTime))]
    [MapperIgnoreTarget(nameof(TradingVessel.ExtraProperties))]
    [MapperIgnoreTarget(nameof(TradingVessel.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(TradingVessel.Items))]
    public partial void MapToEntity(CreateUpdateTradingVesselDto dto, TradingVessel entity);

    // DTO to Entity mapping for creation
    [MapperIgnoreTarget(nameof(TradingVessel.Id))] // Will be set by EF Core
    [MapperIgnoreTarget(nameof(TradingVessel.DocEntry))] // Will be set by EF Core
    [MapperIgnoreTarget(nameof(TradingVessel.CreatedBy))] // Will be set by ABP
    [MapperIgnoreTarget(nameof(TradingVessel.CreatedAt))]
    [MapperIgnoreTarget(nameof(TradingVessel.CreationTime))]
    [MapperIgnoreTarget(nameof(TradingVessel.CreatorId))]
    [MapperIgnoreTarget(nameof(TradingVessel.LastModificationTime))]
    [MapperIgnoreTarget(nameof(TradingVessel.LastModifierId))]
    [MapperIgnoreTarget(nameof(TradingVessel.IsDeleted))]
    [MapperIgnoreTarget(nameof(TradingVessel.DeleterId))]
    [MapperIgnoreTarget(nameof(TradingVessel.DeletionTime))]
    [MapperIgnoreTarget(nameof(TradingVessel.ExtraProperties))]
    [MapperIgnoreTarget(nameof(TradingVessel.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(TradingVessel.Items))]
    public partial TradingVessel MapToEntity(CreateUpdateTradingVesselDto dto);

    // Map list of entities to DTOs
    public partial List<TradingVesselDto> MapToDtoList(List<TradingVessel> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<TradingVesselDto> MapToDtoEnumerable(IEnumerable<TradingVessel> entities);

    // Map with items included
    public TradingVesselWithItemsDto MapToDtoWithItems(TradingVessel entity, List<VesselItemDto> items)
    {
        var dto = MapToDto(entity);
        var withItemsDto = new TradingVesselWithItemsDto
        {
            Id = dto.Id,
            DocEntry = dto.DocEntry,
            DocNum = dto.DocNum,
            Tenant = dto.Tenant,
            Bp = dto.Bp,
            PostDate = dto.PostDate,
            Contract = dto.Contract,
            Bc = dto.Bc,
            CreatedBy = dto.CreatedBy,
            UpdatedBy = dto.UpdatedBy,
            CreatedAt = dto.CreatedAt,
            UpdatedAt = dto.UpdatedAt,
            Deleted = dto.Deleted,
            Status = dto.Status,
            Remark = dto.Remark,
            ContractDate = dto.ContractDate,
            SubconType = dto.SubconType,
            DocStatus = dto.DocStatus,
            Items = items
        };

        return withItemsDto;
    }
}