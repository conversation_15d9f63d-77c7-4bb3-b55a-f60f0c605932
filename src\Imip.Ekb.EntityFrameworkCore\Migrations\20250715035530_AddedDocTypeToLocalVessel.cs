﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class AddedDocTypeToLocalVessel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<long>(
                name: "DocEntry",
                table: "master_tradings",
                type: "bigint",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int")
                .Annotation("SqlServer:Identity", "1, 1")
                .OldAnnotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddColumn<string>(
                name: "DocType",
                table: "L_master",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_THEXP_DocType",
                table: "THEXP",
                column: "DocType");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_Header_DocType",
                table: "T_MDOC_Header",
                column: "DocType");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_Header_TransType",
                table: "T_MDOC_Header",
                column: "TransType");

            migrationBuilder.CreateIndex(
                name: "IX_L_master_DocType",
                table: "L_master",
                column: "DocType");

            migrationBuilder.CreateIndex(
                name: "IX_L_master_TransType",
                table: "L_master",
                column: "TransType");

            migrationBuilder.Sql(@"
                UPDATE L_master
                SET DocType = (SELECT t.TransType FROM L_master t WHERE L_master.DocEntry = t.DocEntry);
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_THEXP_DocType",
                table: "THEXP");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_Header_DocType",
                table: "T_MDOC_Header");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_Header_TransType",
                table: "T_MDOC_Header");

            migrationBuilder.DropIndex(
                name: "IX_L_master_DocType",
                table: "L_master");

            migrationBuilder.DropIndex(
                name: "IX_L_master_TransType",
                table: "L_master");

            migrationBuilder.DropColumn(
                name: "DocType",
                table: "L_master");

            migrationBuilder.AlterColumn<int>(
                name: "DocEntry",
                table: "master_tradings",
                type: "int",
                nullable: false,
                oldClrType: typeof(long),
                oldType: "bigint")
                .Annotation("SqlServer:Identity", "1, 1")
                .OldAnnotation("SqlServer:Identity", "1, 1");
        }
    }
}
