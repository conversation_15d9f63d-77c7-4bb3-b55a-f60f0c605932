using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Imip.Ekb.Models;

namespace Imip.Ekb.Application.Contracts.Attachments;

public interface IDocAttachmentAppService :
    ICrudAppService<
        DocAttachmentDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateDocAttachmentDto,
        CreateUpdateDocAttachmentDto>
{
    /// <summary>
    /// Uploads a file
    /// </summary>
    Task<FileUploadResultDto> UploadFileAsync(FileUploadDto input, string fileName, string contentType, byte[] fileBytes);

    /// <summary>
    /// Uploads a file using a single DTO
    /// </summary>
    Task<FileUploadResultDto> UploadWithFileContentAsync(FileUploadInputDto input);

    /// <summary>
    /// Downloads a file by ID
    /// </summary>
    Task<FileDto> DownloadAsync(Guid id);

    /// <summary>
    /// Downloads a file by SFTP path
    /// </summary>
    Task<FileDto> DownloadByPathAsync(string filePath);

    /// <summary>
    /// Gets a list of attachments by reference ID
    /// </summary>
    Task<List<FileUploadResultDto>> GetByReferenceAsync(Guid referenceId);

    /// <summary>
    /// Deletes a file by ID
    /// </summary>
    Task<bool> DeleteAsync(Guid id);

    /// <summary>
    /// Get a filtered list of DocAttachments with dynamic query support
    /// </summary>
    Task<PagedResultDto<DocAttachmentDto>> FilterListAsync(QueryParametersDto parameters);
}