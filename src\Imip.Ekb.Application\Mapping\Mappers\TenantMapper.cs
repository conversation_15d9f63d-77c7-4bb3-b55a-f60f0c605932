﻿using Imip.Ekb.Master.Tenants;
using Imip.Ekb.Master.Tenants.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class TenantMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(MasterTenant.Id), nameof(MasterTenantDto.Id))]
    public partial MasterTenantDto MapToDto(MasterTenant entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(MasterTenant.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(MasterTenant.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(MasterTenant.CreatedAt))]
    public partial void MapToEntity(TenantCreateUpdateDto dto, MasterTenant entity);

    // Custom mapping methods for complex scenarios
    public MasterTenant CreateEntityWithId(TenantCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (MasterTenant)Activator.CreateInstance(typeof(MasterTenant), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<MasterTenantDto> MapToDtoList(List<MasterTenant> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<MasterTenantDto> MapToDtoEnumerable(IEnumerable<MasterTenant> entities);
}
