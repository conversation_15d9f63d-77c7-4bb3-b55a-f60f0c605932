using Imip.Ekb.Billing.ExportVesselBillings;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbExportVesselBillingConfiguration : IEntityTypeConfiguration<ExportVesselBilling>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbExportVesselBillingConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<ExportVesselBilling> b)
    {
        b.ToTable("BHEXP", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        b.HasOne(x => x.MasterJetty)
            .WithMany(z => z.ExportVesselBillings)
            .HasForeignKey(x => x.JettyId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);


        b.HasMany(x => x.Items)
            .WithOne(x => x.ExportVesselBilling)
            // .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);
    }
}