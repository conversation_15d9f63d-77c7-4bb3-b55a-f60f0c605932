using Imip.Ekb.BoundedZone.ImportVessels;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbImportVesselConfiguration : IEntityTypeConfiguration<ImportVessel>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbImportVesselConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<ImportVessel> b)
    {
        b.ToTable("T_MDOC_Header", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        b.HasOne(x => x.MasterJetty)
            .WithMany(z => z.ImportVessels)
            .HasForeignKey(x => x.JettyId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.<PERSON>(x => x.Vessel)
            .WithMany(z => z.ImportVessels)
            .HasForeignKey(x => x.VesselId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterAgent)
           .WithMany(x => x.ImportVessels)
           .HasForeignKey(x => x.MasterAgentId)
           .HasPrincipalKey(z => z.Id)
           .IsRequired(false)
           .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterTrading)
           .WithMany(x => x.ImportVessels)
           .HasForeignKey(x => x.MasterTradingId)
           .HasPrincipalKey(z => z.Id)
           .IsRequired(false)
           .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterSurveyor)
           .WithMany(x => x.ImportVessels)
           .HasForeignKey(x => x.MasterSurveyorId)
           .HasPrincipalKey(z => z.Id)
           .IsRequired(false)
           .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterPortOrigin)
           .WithMany(x => x.ImportVessels)
           .HasForeignKey(x => x.PortOriginId)
           .HasPrincipalKey(z => z.Id)
           .IsRequired(false)
           .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterDestinationPort)
          .WithMany(x => x.ImportVessels)
          .HasForeignKey(x => x.DestinationPortId)
          .HasPrincipalKey(z => z.Id)
          .IsRequired(false)
          .OnDelete(DeleteBehavior.NoAction);

        // Configure Items collection using HeaderId as foreign key (not creating columns on vessel side)
        b.HasMany(x => x.Items)
            .WithOne(x => x.ImportVessel)
            .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.Property(x => x.AgentId)
            .HasColumnType("bigint");
        b.Property(x => x.SurveyorId)
            .HasColumnType("bigint");
        b.Property(x => x.TradingId)
            .HasColumnType("bigint");

        b.HasIndex(z => z.DocType);
        b.HasIndex(z => z.TransType);
    }
}
