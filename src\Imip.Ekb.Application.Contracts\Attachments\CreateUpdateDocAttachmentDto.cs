using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Application.Contracts.Attachments;

public class CreateUpdateDocAttachmentDto : EntityDto<Guid>
{
    [Required]
    [StringLength(255)]
    public string FileName { get; set; } = null!;

    [Required]
    [StringLength(255)]
    public string ContentType { get; set; } = null!;

    [Required]
    [StringLength(255)]
    public string CreatedBy { get; set; } = null!;

    public int? DocumentReferenceId { get; set; }

    /// <summary>
    /// Optional reference ID that this file is associated with
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// The blob name used to store the file in the blob storage
    /// </summary>
    public string? BlobName { get; set; }

    /// <summary>
    /// Optional description of the file
    /// </summary>
    public string? Description { get; set; }

    [Required]
    [StringLength(50)]
    public string DocType { get; set; } = null!;

    [Required]
    [StringLength(50)]
    public string TransType { get; set; } = null!;

    [StringLength(100)]
    public string? SecretKey { get; set; }

    [StringLength(20)]
    public string? TypePa { get; set; }

    [StringLength(255)]
    public string? FilePath { get; set; }

    [StringLength(255)]
    public string? NameNoExt { get; set; }

    [StringLength(255)]
    public string? Size { get; set; }

    [StringLength(255)]
    public string? Extension { get; set; }
}