using Imip.Ekb.Master.Tenants;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbMasterTenantConfiguration : IEntityTypeConfiguration<MasterTenant>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbMasterTenantConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<MasterTenant> b)
    {
        b.ToTable("M_Tenant", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        b.HasIndex(x => x.Name);
        b.<PERSON>ndex(x => x.TenantGroupId);

        b.<PERSON>(x => x.TenantGroup)
            .WithMany()
            .HasForeignKey(x => x.TenantGroupId)
            .HasPrincipalKey(z => z.Id)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        b.Property(x => x.MasterGroupId)
            .HasColumnType("bigint");
    }
}