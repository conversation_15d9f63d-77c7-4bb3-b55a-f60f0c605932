using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.Application.Contracts.Attachments;
using Imip.Ekb.Models;
using Imip.Ekb.Web.Models.Attachments;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.IO;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.Ekb.Web.Controllers;

/// <summary>
/// CRUD controller for DocAttachment entity with dynamic query filtering
/// </summary>
[Route("api/ekb/doc-attachments")]
[RemoteService]
[Authorize]
public class DocAttachmentController : AbpControllerBase
{
    private readonly IDocAttachmentAppService _docAttachmentAppService;
    private readonly ILogger<DocAttachmentController> _logger;
    private static readonly RecyclableMemoryStreamManager _recyclableMemoryStreamManager = new();

    public DocAttachmentController(
        IDocAttachmentAppService docAttachmentAppService,
        ILogger<DocAttachmentController> logger)
    {
        _docAttachmentAppService = docAttachmentAppService;
        _logger = logger;
    }

    /// <summary>
    /// Get a paged list of DocAttachments with dynamic filtering and sorting
    /// </summary>
    [HttpPost("list")]
    public virtual async Task<PagedResultDto<DocAttachmentDto>> GetPagedListAsync(QueryParametersDto input)
    {
        try
        {
            return await _docAttachmentAppService.FilterListAsync(input);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged list of DocAttachments");
            throw;
        }
    }

    /// <summary>
    /// Get a DocAttachment by ID
    /// </summary>
    [HttpGet("{id}")]
    public virtual async Task<DocAttachmentDto> GetAsync(Guid id)
    {
        try
        {
            return await _docAttachmentAppService.GetAsync(id);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "DocAttachment not found with ID: {Id}", id);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DocAttachment with ID: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// Create a new DocAttachment
    /// </summary>
    [HttpPost]
    public virtual async Task<DocAttachmentDto> CreateAsync(CreateUpdateDocAttachmentDto input)
    {
        try
        {
            return await _docAttachmentAppService.CreateAsync(input);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "Error creating DocAttachment");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating DocAttachment");
            throw;
        }
    }

    /// <summary>
    /// Update an existing DocAttachment
    /// </summary>
    [HttpPut("{id}")]
    public virtual async Task<DocAttachmentDto> UpdateAsync(Guid id, CreateUpdateDocAttachmentDto input)
    {
        try
        {
            return await _docAttachmentAppService.UpdateAsync(id, input);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "Error updating DocAttachment with ID: {Id}", id);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating DocAttachment with ID: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// Delete a DocAttachment by ID
    /// </summary>
    [HttpDelete("{id}")]
    public virtual async Task DeleteAsync(Guid id)
    {
        try
        {
            await _docAttachmentAppService.DeleteAsync(id);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "Error deleting DocAttachment with ID: {Id}", id);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting DocAttachment with ID: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// Upload a file with metadata
    /// </summary>
    [HttpPost("upload")]
    public virtual async Task<FileUploadResultDto> UploadFileAsync([FromForm] FileUploadFormDto input, IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                throw new UserFriendlyException("No file provided");
            }

            // Map to FileUploadDto (contracts) and extract file bytes
            var dto = new FileUploadDto
            {
                Description = input.Description,
                ReferenceId = input.ReferenceId,
                ReferenceType = input.ReferenceType,
                DocType = input.DocType,
                TransType = input.TransType,
                TypePa = input.TypePa,
                TabName = input.TabName,
                DocumentReferenceId = input.DocumentReferenceId
            };

            using var memoryStream = _recyclableMemoryStreamManager.GetStream();
            await file.CopyToAsync(memoryStream);
            var fileBytes = memoryStream.ToArray();
            memoryStream.Dispose();

            return await _docAttachmentAppService.UploadFileAsync(dto, file.FileName, file.ContentType, fileBytes);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "Error uploading file: {FileName}", file?.FileName);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file: {FileName}", file?.FileName);
            throw;
        }
    }

    /// <summary>
    /// Upload a file using a single DTO with file content
    /// </summary>
    [HttpPost("upload-with-content")]
    public virtual async Task<FileUploadResultDto> UploadWithFileContentAsync(FileUploadInputDto input)
    {
        try
        {
            return await _docAttachmentAppService.UploadWithFileContentAsync(input);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "Error uploading file with content: {FileName}", input.FileName);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file with content: {FileName}", input.FileName);
            throw;
        }
    }

    /// <summary>
    /// Download a file by ID
    /// </summary>
    [HttpGet("download/{id}")]
    public virtual async Task<IActionResult> DownloadAsync(Guid id)
    {
        try
        {
            var fileDto = await _docAttachmentAppService.DownloadAsync(id);
            return File(fileDto.Content, fileDto.ContentType, fileDto.FileName);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "File download failed for ID: {Id}", id);
            return NotFound(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading file with ID: {Id}", id);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get a list of attachments by reference ID
    /// </summary>
    [HttpGet("by-reference/{referenceId}")]
    public virtual async Task<List<FileUploadResultDto>> GetByReferenceAsync(Guid referenceId)
    {
        try
        {
            return await _docAttachmentAppService.GetByReferenceAsync(referenceId);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "Error getting attachments by reference ID: {ReferenceId}", referenceId);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting attachments by reference ID: {ReferenceId}", referenceId);
            throw;
        }
    }

    /// <summary>
    /// Delete a file by ID (returns boolean result)
    /// </summary>
    [HttpDelete("file/{id}")]
    public virtual async Task<bool> DeleteFileAsync(Guid id)
    {
        try
        {
            return await _docAttachmentAppService.DeleteAsync(id);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "Error deleting file with ID: {Id}", id);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file with ID: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// Get file information by ID
    /// </summary>
    [HttpGet("info/{id}")]
    public virtual async Task<DocAttachmentDto> GetFileInfoAsync(Guid id)
    {
        try
        {
            return await _docAttachmentAppService.GetAsync(id);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "File info retrieval failed for ID: {Id}", id);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file info for ID: {Id}", id);
            throw;
        }
    }
}
