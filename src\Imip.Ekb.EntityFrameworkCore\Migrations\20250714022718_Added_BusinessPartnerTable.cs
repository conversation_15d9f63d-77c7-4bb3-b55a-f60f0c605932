﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class Added_BusinessPartnerTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "BusinessPartnerId",
                table: "T_MDOC",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddUniqueConstraint(
                name: "AK_M_BP_Id",
                table: "M_BP",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_BusinessPartnerId",
                table: "T_MDOC",
                column: "BusinessPartnerId");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_M_BP_BusinessPartnerId",
                table: "T_MDOC",
                column: "BusinessPartnerId",
                principalTable: "M_BP",
                principalColumn: "Id");

            migrationBuilder.Sql(@"
                UPDATE T_MDOC
                SET BusinessPartnerId = (SELECT top 1 M_BP.Id FROM M_BP WHERE T_MDOC.BP = M_BP.Name);
            ");

            migrationBuilder.Sql(@"
                UPDATE T_MDOC
                SET BusinessPartnerId = (SELECT top 1 M_BP.Id FROM M_BP WHERE T_MDOC.BPNum = M_BP.DocEntry)
                where BusinessPartnerId is null;
            ");

            migrationBuilder.Sql(@"
                UPDATE T_MDOC
                SET BusinessPartnerId = (SELECT top 1 M_BP.Id FROM M_BP WHERE TRY_CAST(T_MDOC.BP AS INT) = M_BP.DocEntry)
                where BusinessPartnerId is null;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_M_BP_BusinessPartnerId",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_BusinessPartnerId",
                table: "T_MDOC");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_M_BP_Id",
                table: "M_BP");

            migrationBuilder.DropColumn(
                name: "BusinessPartnerId",
                table: "T_MDOC");
        }
    }
}
