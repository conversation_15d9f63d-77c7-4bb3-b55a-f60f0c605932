﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class Added_VesselRelationship : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "VesselId",
                table: "THEXP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "VesselId",
                table: "T_MDOC_Header",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "BargeId",
                table: "L_master",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "VesselId",
                table: "L_master",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddUniqueConstraint(
                name: "AK_M_CARGO_Id",
                table: "M_CARGO",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_THEXP_VesselId",
                table: "THEXP",
                column: "VesselId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_Header_VesselId",
                table: "T_MDOC_Header",
                column: "VesselId");

            migrationBuilder.CreateIndex(
                name: "IX_L_master_BargeId",
                table: "L_master",
                column: "BargeId");

            migrationBuilder.CreateIndex(
                name: "IX_L_master_VesselId",
                table: "L_master",
                column: "VesselId");

            migrationBuilder.AddForeignKey(
                name: "FK_L_master_M_CARGO_BargeId",
                table: "L_master",
                column: "BargeId",
                principalTable: "M_CARGO",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_L_master_M_CARGO_VesselId",
                table: "L_master",
                column: "VesselId",
                principalTable: "M_CARGO",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_Header_M_CARGO_VesselId",
                table: "T_MDOC_Header",
                column: "VesselId",
                principalTable: "M_CARGO",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_THEXP_M_CARGO_VesselId",
                table: "THEXP",
                column: "VesselId",
                principalTable: "M_CARGO",
                principalColumn: "Id");


            migrationBuilder.Sql(@"
                UPDATE T_MDOC_Header
                SET VesselId = (SELECT top 1 tbc.Id FROM M_CARGO tbc WHERE T_MDOC_Header.Cargo = tbc.Name and Status='Y');
            ");

            migrationBuilder.Sql(@"
                UPDATE L_master
                SET VesselId = (SELECT tbc.Id FROM M_CARGO tbc WHERE L_master.VesselName = tbc.DocEntry and Status='Y');
            ");
            migrationBuilder.Sql(@"
                UPDATE L_master
                SET BargeId = (SELECT tbc.Id FROM M_CARGO tbc WHERE L_master.tongkang = tbc.DocEntry and Status='Y');
            ");

            migrationBuilder.Sql(@"
                UPDATE THEXP
                SET VesselId = (SELECT tbc.Id FROM M_CARGO tbc WHERE THEXP.VesselName = tbc.DocEntry and Status='Y');
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_L_master_M_CARGO_BargeId",
                table: "L_master");

            migrationBuilder.DropForeignKey(
                name: "FK_L_master_M_CARGO_VesselId",
                table: "L_master");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_Header_M_CARGO_VesselId",
                table: "T_MDOC_Header");

            migrationBuilder.DropForeignKey(
                name: "FK_THEXP_M_CARGO_VesselId",
                table: "THEXP");

            migrationBuilder.DropIndex(
                name: "IX_THEXP_VesselId",
                table: "THEXP");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_Header_VesselId",
                table: "T_MDOC_Header");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_M_CARGO_Id",
                table: "M_CARGO");

            migrationBuilder.DropIndex(
                name: "IX_L_master_BargeId",
                table: "L_master");

            migrationBuilder.DropIndex(
                name: "IX_L_master_VesselId",
                table: "L_master");

            migrationBuilder.DropColumn(
                name: "VesselId",
                table: "THEXP");

            migrationBuilder.DropColumn(
                name: "VesselId",
                table: "T_MDOC_Header");

            migrationBuilder.DropColumn(
                name: "BargeId",
                table: "L_master");

            migrationBuilder.DropColumn(
                name: "VesselId",
                table: "L_master");
        }
    }
}
