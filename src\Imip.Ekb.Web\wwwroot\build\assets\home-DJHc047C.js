import{j as c,Q as Uo,f as Wo,a as T,b as Cn,u as Ho,c as re,d as Mn,h as mr,r as Pn,R as X}from"./vendor-B032F4SZ.js";import{c as Go,a as Tn,u as kn,P as gr,b as ne,d as St,e as Te,f as Mt,B as Yo,r as Xo,R as Qo,V as Rn,g as In,h as Jo,i as Zo,S as st,j as ea,C as ta,k as ra,l as na,m as oa,n as aa,T as sa,D as ia,o as ca,O as la,p as An,q as ua,s as da,A as fa,t as pa,v as ha,w as va,x as ma,y as ga,z as ba,F as ya,E as xa,G as wa,H as _a,I as ja,L as Sa,J as Ea,K as Oa,M as Ca,N as Ma,Q as Pa,U as Ta,W as ka,X as Ra,Y as Ia,Z as Aa,_ as Na,$ as Da,a0 as La,a1 as za,a2 as Ba,a3 as $a,a4 as Ka,a5 as Fa,a6 as Va}from"./radix-Jg4_14Md.js";import{c as Nn,q as Dn,Y as Et,$ as qa}from"./App-BOdbo8DN.js";const Ua=new Wo({defaultOptions:{queries:{staleTime:10*1e3,refetchOnWindowFocus:!0}}});function Wa({children:e}){return c.jsx(Uo,{client:Ua,children:e})}const{useContext:Ha}=Cn,Ga={theme:"system",setTheme:()=>null,resolvedTheme:void 0},Ln=T.createContext(Ga);function Ya(){const e=Ha(Ln);if(e===void 0)throw new Error("useTheme must be used within a ThemeProvider");return e}const{useEffect:Tr,useState:kr}=Cn;function Xa({children:e,defaultTheme:t="system",storageKey:r="vite-ui-theme",...n}){const[o,s]=kr(()=>localStorage.getItem(r)||t),[a,l]=kr(()=>window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light");Tr(()=>{const u=window.document.documentElement;if(u.classList.remove("light","dark"),o==="system"){u.classList.add(a);return}u.classList.add(o)},[o,a]),Tr(()=>{const u=window.matchMedia("(prefers-color-scheme: dark)"),f=()=>{const d=window.document.documentElement;d.classList.remove("light","dark");const g=u.matches?"dark":"light";l(g),o==="system"&&d.classList.add(g)};return u.addEventListener("change",f),()=>u.removeEventListener("change",f)},[o]);const i={theme:o,setTheme:u=>{localStorage.setItem(r,u),s(u)},resolvedTheme:o==="system"?a:o};return c.jsx(Ln.Provider,{...n,value:i,children:e})}var zn="ToastProvider",[br,Qa,Ja]=Go("Toast"),[Bn,gd]=Tn("Toast",[Ja]),[Za,Pt]=Bn(zn),$n=e=>{const{__scopeToast:t,label:r="Notification",duration:n=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:a}=e,[l,i]=T.useState(null),[u,f]=T.useState(0),d=T.useRef(!1),g=T.useRef(!1);return r.trim(),c.jsx(br.Provider,{scope:t,children:c.jsx(Za,{scope:t,label:r,duration:n,swipeDirection:o,swipeThreshold:s,toastCount:u,viewport:l,onViewportChange:i,onToastAdd:T.useCallback(()=>f(m=>m+1),[]),onToastRemove:T.useCallback(()=>f(m=>m-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:g,children:a})})};$n.displayName=zn;var Kn="ToastViewport",es=["F8"],er="toast.viewportPause",tr="toast.viewportResume",Fn=T.forwardRef((e,t)=>{const{__scopeToast:r,hotkey:n=es,label:o="Notifications ({hotkey})",...s}=e,a=Pt(Kn,r),l=Qa(r),i=T.useRef(null),u=T.useRef(null),f=T.useRef(null),d=T.useRef(null),g=Mt(t,d,a.onViewportChange),m=n.join("+").replace(/Key/g,"").replace(/Digit/g,""),p=a.toastCount>0;T.useEffect(()=>{const v=w=>{n.length!==0&&n.every(h=>w[h]||w.code===h)&&d.current?.focus()};return document.addEventListener("keydown",v),()=>document.removeEventListener("keydown",v)},[n]),T.useEffect(()=>{const v=i.current,w=d.current;if(p&&v&&w){const y=()=>{if(!a.isClosePausedRef.current){const P=new CustomEvent(er);w.dispatchEvent(P),a.isClosePausedRef.current=!0}},h=()=>{if(a.isClosePausedRef.current){const P=new CustomEvent(tr);w.dispatchEvent(P),a.isClosePausedRef.current=!1}},j=P=>{!v.contains(P.relatedTarget)&&h()},C=()=>{v.contains(document.activeElement)||h()};return v.addEventListener("focusin",y),v.addEventListener("focusout",j),v.addEventListener("pointermove",y),v.addEventListener("pointerleave",C),window.addEventListener("blur",y),window.addEventListener("focus",h),()=>{v.removeEventListener("focusin",y),v.removeEventListener("focusout",j),v.removeEventListener("pointermove",y),v.removeEventListener("pointerleave",C),window.removeEventListener("blur",y),window.removeEventListener("focus",h)}}},[p,a.isClosePausedRef]);const b=T.useCallback(({tabbingDirection:v})=>{const y=l().map(h=>{const j=h.ref.current,C=[j,...vs(j)];return v==="forwards"?C:C.reverse()});return(v==="forwards"?y.reverse():y).flat()},[l]);return T.useEffect(()=>{const v=d.current;if(v){const w=y=>{const h=y.altKey||y.ctrlKey||y.metaKey;if(y.key==="Tab"&&!h){const C=document.activeElement,P=y.shiftKey;if(y.target===v&&P){u.current?.focus();return}const M=b({tabbingDirection:P?"backwards":"forwards"}),x=M.findIndex(_=>_===C);Lt(M.slice(x+1))?y.preventDefault():P?u.current?.focus():f.current?.focus()}};return v.addEventListener("keydown",w),()=>v.removeEventListener("keydown",w)}},[l,b]),c.jsxs(Yo,{ref:i,role:"region","aria-label":o.replace("{hotkey}",m),tabIndex:-1,style:{pointerEvents:p?void 0:"none"},children:[p&&c.jsx(rr,{ref:u,onFocusFromOutsideViewport:()=>{const v=b({tabbingDirection:"forwards"});Lt(v)}}),c.jsx(br.Slot,{scope:r,children:c.jsx(Te.ol,{tabIndex:-1,...s,ref:g})}),p&&c.jsx(rr,{ref:f,onFocusFromOutsideViewport:()=>{const v=b({tabbingDirection:"backwards"});Lt(v)}})]})});Fn.displayName=Kn;var Vn="ToastFocusProxy",rr=T.forwardRef((e,t)=>{const{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,s=Pt(Vn,r);return c.jsx(Rn,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:a=>{const l=a.relatedTarget;!s.viewport?.contains(l)&&n()}})});rr.displayName=Vn;var it="Toast",ts="toast.swipeStart",rs="toast.swipeMove",ns="toast.swipeCancel",os="toast.swipeEnd",qn=T.forwardRef((e,t)=>{const{forceMount:r,open:n,defaultOpen:o,onOpenChange:s,...a}=e,[l,i]=kn({prop:n,defaultProp:o??!0,onChange:s,caller:it});return c.jsx(gr,{present:r||l,children:c.jsx(is,{open:l,...a,ref:t,onClose:()=>i(!1),onPause:St(e.onPause),onResume:St(e.onResume),onSwipeStart:ne(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:ne(e.onSwipeMove,u=>{const{x:f,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:ne(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:ne(e.onSwipeEnd,u=>{const{x:f,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),i(!1)})})})});qn.displayName=it;var[as,ss]=Bn(it,{onClose(){}}),is=T.forwardRef((e,t)=>{const{__scopeToast:r,type:n="foreground",duration:o,open:s,onClose:a,onEscapeKeyDown:l,onPause:i,onResume:u,onSwipeStart:f,onSwipeMove:d,onSwipeCancel:g,onSwipeEnd:m,...p}=e,b=Pt(it,r),[v,w]=T.useState(null),y=Mt(t,S=>w(S)),h=T.useRef(null),j=T.useRef(null),C=o||b.duration,P=T.useRef(0),O=T.useRef(C),E=T.useRef(0),{onToastAdd:M,onToastRemove:x}=b,_=St(()=>{v?.contains(document.activeElement)&&b.viewport?.focus(),a()}),I=T.useCallback(S=>{!S||S===1/0||(window.clearTimeout(E.current),P.current=new Date().getTime(),E.current=window.setTimeout(_,S))},[_]);T.useEffect(()=>{const S=b.viewport;if(S){const N=()=>{I(O.current),u?.()},A=()=>{const D=new Date().getTime()-P.current;O.current=O.current-D,window.clearTimeout(E.current),i?.()};return S.addEventListener(er,A),S.addEventListener(tr,N),()=>{S.removeEventListener(er,A),S.removeEventListener(tr,N)}}},[b.viewport,C,i,u,I]),T.useEffect(()=>{s&&!b.isClosePausedRef.current&&I(C)},[s,C,b.isClosePausedRef,I]),T.useEffect(()=>(M(),()=>x()),[M,x]);const k=T.useMemo(()=>v?Yn(v):null,[v]);return b.viewport?c.jsxs(c.Fragment,{children:[k&&c.jsx(cs,{__scopeToast:r,role:"status","aria-live":n==="foreground"?"assertive":"polite","aria-atomic":!0,children:k}),c.jsx(as,{scope:r,onClose:_,children:Xo.createPortal(c.jsx(br.ItemSlot,{scope:r,children:c.jsx(Qo,{asChild:!0,onEscapeKeyDown:ne(l,()=>{b.isFocusedToastEscapeKeyDownRef.current||_(),b.isFocusedToastEscapeKeyDownRef.current=!1}),children:c.jsx(Te.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":b.swipeDirection,...p,ref:y,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:ne(e.onKeyDown,S=>{S.key==="Escape"&&(l?.(S.nativeEvent),S.nativeEvent.defaultPrevented||(b.isFocusedToastEscapeKeyDownRef.current=!0,_()))}),onPointerDown:ne(e.onPointerDown,S=>{S.button===0&&(h.current={x:S.clientX,y:S.clientY})}),onPointerMove:ne(e.onPointerMove,S=>{if(!h.current)return;const N=S.clientX-h.current.x,A=S.clientY-h.current.y,D=!!j.current,R=["left","right"].includes(b.swipeDirection),V=["left","up"].includes(b.swipeDirection)?Math.min:Math.max,W=R?V(0,N):0,Q=R?0:V(0,A),Z=S.pointerType==="touch"?10:2,q={x:W,y:Q},U={originalEvent:S,delta:q};D?(j.current=q,ft(rs,d,U,{discrete:!1})):Rr(q,b.swipeDirection,Z)?(j.current=q,ft(ts,f,U,{discrete:!1}),S.target.setPointerCapture(S.pointerId)):(Math.abs(N)>Z||Math.abs(A)>Z)&&(h.current=null)}),onPointerUp:ne(e.onPointerUp,S=>{const N=j.current,A=S.target;if(A.hasPointerCapture(S.pointerId)&&A.releasePointerCapture(S.pointerId),j.current=null,h.current=null,N){const D=S.currentTarget,R={originalEvent:S,delta:N};Rr(N,b.swipeDirection,b.swipeThreshold)?ft(os,m,R,{discrete:!0}):ft(ns,g,R,{discrete:!0}),D.addEventListener("click",V=>V.preventDefault(),{once:!0})}})})})}),b.viewport)})]}):null}),cs=e=>{const{__scopeToast:t,children:r,...n}=e,o=Pt(it,t),[s,a]=T.useState(!1),[l,i]=T.useState(!1);return ps(()=>a(!0)),T.useEffect(()=>{const u=window.setTimeout(()=>i(!0),1e3);return()=>window.clearTimeout(u)},[]),l?null:c.jsx(In,{asChild:!0,children:c.jsx(Rn,{...n,children:s&&c.jsxs(c.Fragment,{children:[o.label," ",r]})})})},ls="ToastTitle",Un=T.forwardRef((e,t)=>{const{__scopeToast:r,...n}=e;return c.jsx(Te.div,{...n,ref:t})});Un.displayName=ls;var us="ToastDescription",Wn=T.forwardRef((e,t)=>{const{__scopeToast:r,...n}=e;return c.jsx(Te.div,{...n,ref:t})});Wn.displayName=us;var ds="ToastAction",fs=T.forwardRef((e,t)=>{const{altText:r,...n}=e;return r.trim()?c.jsx(Gn,{altText:r,asChild:!0,children:c.jsx(yr,{...n,ref:t})}):null});fs.displayName=ds;var Hn="ToastClose",yr=T.forwardRef((e,t)=>{const{__scopeToast:r,...n}=e,o=ss(Hn,r);return c.jsx(Gn,{asChild:!0,children:c.jsx(Te.button,{type:"button",...n,ref:t,onClick:ne(e.onClick,o.onClose)})})});yr.displayName=Hn;var Gn=T.forwardRef((e,t)=>{const{__scopeToast:r,altText:n,...o}=e;return c.jsx(Te.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function Yn(e){const t=[];return Array.from(e.childNodes).forEach(n=>{if(n.nodeType===n.TEXT_NODE&&n.textContent&&t.push(n.textContent),hs(n)){const o=n.ariaHidden||n.hidden||n.style.display==="none",s=n.dataset.radixToastAnnounceExclude==="";if(!o)if(s){const a=n.dataset.radixToastAnnounceAlt;a&&t.push(a)}else t.push(...Yn(n))}}),t}function ft(e,t,r,{discrete:n}){const o=r.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?Zo(o,s):o.dispatchEvent(s)}var Rr=(e,t,r=0)=>{const n=Math.abs(e.x),o=Math.abs(e.y),s=n>o;return t==="left"||t==="right"?s&&n>r:!s&&o>r};function ps(e=()=>{}){const t=St(e);Jo(()=>{let r=0,n=0;return r=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(r),window.cancelAnimationFrame(n)}},[t])}function hs(e){return e.nodeType===e.ELEMENT_NODE}function vs(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const o=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||o?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function Lt(e){const t=document.activeElement;return e.some(r=>r===t?!0:(r.focus(),document.activeElement!==t))}var ms=$n,gs=Fn,bs=qn,ys=Un,xs=Wn,ws=yr;function Xn(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(r=Xn(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function Qn(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=Xn(e))&&(n&&(n+=" "),n+=t);return n}const Ir=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Ar=Qn,xr=(e,t)=>r=>{var n;if(t?.variants==null)return Ar(e,r?.class,r?.className);const{variants:o,defaultVariants:s}=t,a=Object.keys(o).map(u=>{const f=r?.[u],d=s?.[u];if(f===null)return null;const g=Ir(f)||Ir(d);return o[u][g]}),l=r&&Object.entries(r).reduce((u,f)=>{let[d,g]=f;return g===void 0||(u[d]=g),u},{}),i=t==null||(n=t.compoundVariants)===null||n===void 0?void 0:n.reduce((u,f)=>{let{class:d,className:g,...m}=f;return Object.entries(m).every(p=>{let[b,v]=p;return Array.isArray(v)?v.includes({...s,...l}[b]):{...s,...l}[b]===v})?[...u,d,g]:u},[]);return Ar(e,a,i,r?.class,r?.className)};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),js=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,r,n)=>n?n.toUpperCase():r.toLowerCase()),Nr=e=>{const t=js(e);return t.charAt(0).toUpperCase()+t.slice(1)},Jn=(...e)=>e.filter((t,r,n)=>!!t&&t.trim()!==""&&n.indexOf(t)===r).join(" ").trim(),Ss=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Es={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Os=T.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:o="",children:s,iconNode:a,...l},i)=>T.createElement("svg",{ref:i,...Es,width:t,height:t,stroke:e,strokeWidth:n?Number(r)*24/Number(t):r,className:Jn("lucide",o),...!s&&!Ss(l)&&{"aria-hidden":"true"},...l},[...a.map(([u,f])=>T.createElement(u,f)),...Array.isArray(s)?s:[s]]));/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ve=(e,t)=>{const r=T.forwardRef(({className:n,...o},s)=>T.createElement(Os,{ref:s,iconNode:t,className:Jn(`lucide-${_s(Nr(e))}`,`lucide-${e}`,n),...o}));return r.displayName=Nr(e),r};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cs=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Ms=Ve("check",Cs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ps=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Zn=Ve("chevron-down",Ps);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ts=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],ks=Ve("chevron-right",Ts);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rs=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Is=Ve("chevron-up",Rs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const As=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],Ns=Ve("panel-left",As);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ds=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]],Ls=Ve("shield-alert",Ds);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zs=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],wr=Ve("x",zs),_r="-",Bs=e=>{const t=Ks(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:a=>{const l=a.split(_r);return l[0]===""&&l.length!==1&&l.shift(),eo(l,t)||$s(a)},getConflictingClassGroupIds:(a,l)=>{const i=r[a]||[];return l&&n[a]?[...i,...n[a]]:i}}},eo=(e,t)=>{if(e.length===0)return t.classGroupId;const r=e[0],n=t.nextPart.get(r),o=n?eo(e.slice(1),n):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(_r);return t.validators.find(({validator:a})=>a(s))?.classGroupId},Dr=/^\[(.+)\]$/,$s=e=>{if(Dr.test(e)){const t=Dr.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},Ks=e=>{const{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(const o in r)nr(r[o],n,o,t);return n},nr=(e,t,r,n)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:Lr(t,o);s.classGroupId=r;return}if(typeof o=="function"){if(Fs(o)){nr(o(n),t,r,n);return}t.validators.push({validator:o,classGroupId:r});return}Object.entries(o).forEach(([s,a])=>{nr(a,Lr(t,s),r,n)})})},Lr=(e,t)=>{let r=e;return t.split(_r).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},Fs=e=>e.isThemeGetter,Vs=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;const o=(s,a)=>{r.set(s,a),t++,t>e&&(t=0,n=r,r=new Map)};return{get(s){let a=r.get(s);if(a!==void 0)return a;if((a=n.get(s))!==void 0)return o(s,a),a},set(s,a){r.has(s)?r.set(s,a):o(s,a)}}},or="!",ar=":",qs=ar.length,Us=e=>{const{prefix:t,experimentalParseClassName:r}=e;let n=o=>{const s=[];let a=0,l=0,i=0,u;for(let p=0;p<o.length;p++){let b=o[p];if(a===0&&l===0){if(b===ar){s.push(o.slice(i,p)),i=p+qs;continue}if(b==="/"){u=p;continue}}b==="["?a++:b==="]"?a--:b==="("?l++:b===")"&&l--}const f=s.length===0?o:o.substring(i),d=Ws(f),g=d!==f,m=u&&u>i?u-i:void 0;return{modifiers:s,hasImportantModifier:g,baseClassName:d,maybePostfixModifierPosition:m}};if(t){const o=t+ar,s=n;n=a=>a.startsWith(o)?s(a.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:a,maybePostfixModifierPosition:void 0}}if(r){const o=n;n=s=>r({className:s,parseClassName:o})}return n},Ws=e=>e.endsWith(or)?e.substring(0,e.length-1):e.startsWith(or)?e.substring(1):e,Hs=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const o=[];let s=[];return n.forEach(a=>{a[0]==="["||t[a]?(o.push(...s.sort(),a),s=[]):s.push(a)}),o.push(...s.sort()),o}},Gs=e=>({cache:Vs(e.cacheSize),parseClassName:Us(e),sortModifiers:Hs(e),...Bs(e)}),Ys=/\s+/,Xs=(e,t)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:s}=t,a=[],l=e.trim().split(Ys);let i="";for(let u=l.length-1;u>=0;u-=1){const f=l[u],{isExternal:d,modifiers:g,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:b}=r(f);if(d){i=f+(i.length>0?" "+i:i);continue}let v=!!b,w=n(v?p.substring(0,b):p);if(!w){if(!v){i=f+(i.length>0?" "+i:i);continue}if(w=n(p),!w){i=f+(i.length>0?" "+i:i);continue}v=!1}const y=s(g).join(":"),h=m?y+or:y,j=h+w;if(a.includes(j))continue;a.push(j);const C=o(w,v);for(let P=0;P<C.length;++P){const O=C[P];a.push(h+O)}i=f+(i.length>0?" "+i:i)}return i};function Qs(){let e=0,t,r,n="";for(;e<arguments.length;)(t=arguments[e++])&&(r=to(t))&&(n&&(n+=" "),n+=r);return n}const to=e=>{if(typeof e=="string")return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=to(e[n]))&&(r&&(r+=" "),r+=t);return r};function Js(e,...t){let r,n,o,s=a;function a(i){const u=t.reduce((f,d)=>d(f),e());return r=Gs(u),n=r.cache.get,o=r.cache.set,s=l,l(i)}function l(i){const u=n(i);if(u)return u;const f=Xs(i,r);return o(i,f),f}return function(){return s(Qs.apply(null,arguments))}}const J=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},ro=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,no=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Zs=/^\d+\/\d+$/,ei=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ti=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ri=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ni=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,oi=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,He=e=>Zs.test(e),K=e=>!!e&&!Number.isNaN(Number(e)),Me=e=>!!e&&Number.isInteger(Number(e)),zt=e=>e.endsWith("%")&&K(e.slice(0,-1)),_e=e=>ei.test(e),ai=()=>!0,si=e=>ti.test(e)&&!ri.test(e),oo=()=>!1,ii=e=>ni.test(e),ci=e=>oi.test(e),li=e=>!L(e)&&!z(e),ui=e=>Ye(e,io,oo),L=e=>ro.test(e),De=e=>Ye(e,co,si),Bt=e=>Ye(e,vi,K),zr=e=>Ye(e,ao,oo),di=e=>Ye(e,so,ci),pt=e=>Ye(e,lo,ii),z=e=>no.test(e),et=e=>Xe(e,co),fi=e=>Xe(e,mi),Br=e=>Xe(e,ao),pi=e=>Xe(e,io),hi=e=>Xe(e,so),ht=e=>Xe(e,lo,!0),Ye=(e,t,r)=>{const n=ro.exec(e);return n?n[1]?t(n[1]):r(n[2]):!1},Xe=(e,t,r=!1)=>{const n=no.exec(e);return n?n[1]?t(n[1]):r:!1},ao=e=>e==="position"||e==="percentage",so=e=>e==="image"||e==="url",io=e=>e==="length"||e==="size"||e==="bg-size",co=e=>e==="length",vi=e=>e==="number",mi=e=>e==="family-name",lo=e=>e==="shadow",gi=()=>{const e=J("color"),t=J("font"),r=J("text"),n=J("font-weight"),o=J("tracking"),s=J("leading"),a=J("breakpoint"),l=J("container"),i=J("spacing"),u=J("radius"),f=J("shadow"),d=J("inset-shadow"),g=J("text-shadow"),m=J("drop-shadow"),p=J("blur"),b=J("perspective"),v=J("aspect"),w=J("ease"),y=J("animate"),h=()=>["auto","avoid","all","avoid-page","page","left","right","column"],j=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...j(),z,L],P=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto","contain","none"],E=()=>[z,L,i],M=()=>[He,"full","auto",...E()],x=()=>[Me,"none","subgrid",z,L],_=()=>["auto",{span:["full",Me,z,L]},Me,z,L],I=()=>[Me,"auto",z,L],k=()=>["auto","min","max","fr",z,L],S=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],N=()=>["start","end","center","stretch","center-safe","end-safe"],A=()=>["auto",...E()],D=()=>[He,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...E()],R=()=>[e,z,L],V=()=>[...j(),Br,zr,{position:[z,L]}],W=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Q=()=>["auto","cover","contain",pi,ui,{size:[z,L]}],Z=()=>[zt,et,De],q=()=>["","none","full",u,z,L],U=()=>["",K,et,De],ae=()=>["solid","dashed","dotted","double"],Ze=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],H=()=>[K,zt,Br,zr],Ue=()=>["","none",p,z,L],Ie=()=>["none",K,z,L],Ae=()=>["none",K,z,L],We=()=>[K,z,L],Oe=()=>[He,"full",...E()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[_e],breakpoint:[_e],color:[ai],container:[_e],"drop-shadow":[_e],ease:["in","out","in-out"],font:[li],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[_e],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[_e],shadow:[_e],spacing:["px",K],text:[_e],"text-shadow":[_e],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",He,L,z,v]}],container:["container"],columns:[{columns:[K,L,z,l]}],"break-after":[{"break-after":h()}],"break-before":[{"break-before":h()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:M()}],"inset-x":[{"inset-x":M()}],"inset-y":[{"inset-y":M()}],start:[{start:M()}],end:[{end:M()}],top:[{top:M()}],right:[{right:M()}],bottom:[{bottom:M()}],left:[{left:M()}],visibility:["visible","invisible","collapse"],z:[{z:[Me,"auto",z,L]}],basis:[{basis:[He,"full","auto",l,...E()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[K,He,"auto","initial","none",L]}],grow:[{grow:["",K,z,L]}],shrink:[{shrink:["",K,z,L]}],order:[{order:[Me,"first","last","none",z,L]}],"grid-cols":[{"grid-cols":x()}],"col-start-end":[{col:_()}],"col-start":[{"col-start":I()}],"col-end":[{"col-end":I()}],"grid-rows":[{"grid-rows":x()}],"row-start-end":[{row:_()}],"row-start":[{"row-start":I()}],"row-end":[{"row-end":I()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":k()}],"auto-rows":[{"auto-rows":k()}],gap:[{gap:E()}],"gap-x":[{"gap-x":E()}],"gap-y":[{"gap-y":E()}],"justify-content":[{justify:[...S(),"normal"]}],"justify-items":[{"justify-items":[...N(),"normal"]}],"justify-self":[{"justify-self":["auto",...N()]}],"align-content":[{content:["normal",...S()]}],"align-items":[{items:[...N(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...N(),{baseline:["","last"]}]}],"place-content":[{"place-content":S()}],"place-items":[{"place-items":[...N(),"baseline"]}],"place-self":[{"place-self":["auto",...N()]}],p:[{p:E()}],px:[{px:E()}],py:[{py:E()}],ps:[{ps:E()}],pe:[{pe:E()}],pt:[{pt:E()}],pr:[{pr:E()}],pb:[{pb:E()}],pl:[{pl:E()}],m:[{m:A()}],mx:[{mx:A()}],my:[{my:A()}],ms:[{ms:A()}],me:[{me:A()}],mt:[{mt:A()}],mr:[{mr:A()}],mb:[{mb:A()}],ml:[{ml:A()}],"space-x":[{"space-x":E()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":E()}],"space-y-reverse":["space-y-reverse"],size:[{size:D()}],w:[{w:[l,"screen",...D()]}],"min-w":[{"min-w":[l,"screen","none",...D()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[a]},...D()]}],h:[{h:["screen","lh",...D()]}],"min-h":[{"min-h":["screen","lh","none",...D()]}],"max-h":[{"max-h":["screen","lh",...D()]}],"font-size":[{text:["base",r,et,De]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,z,Bt]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",zt,L]}],"font-family":[{font:[fi,L,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,z,L]}],"line-clamp":[{"line-clamp":[K,"none",z,Bt]}],leading:[{leading:[s,...E()]}],"list-image":[{"list-image":["none",z,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",z,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:R()}],"text-color":[{text:R()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ae(),"wavy"]}],"text-decoration-thickness":[{decoration:[K,"from-font","auto",z,De]}],"text-decoration-color":[{decoration:R()}],"underline-offset":[{"underline-offset":[K,"auto",z,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:E()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",z,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",z,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:V()}],"bg-repeat":[{bg:W()}],"bg-size":[{bg:Q()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Me,z,L],radial:["",z,L],conic:[Me,z,L]},hi,di]}],"bg-color":[{bg:R()}],"gradient-from-pos":[{from:Z()}],"gradient-via-pos":[{via:Z()}],"gradient-to-pos":[{to:Z()}],"gradient-from":[{from:R()}],"gradient-via":[{via:R()}],"gradient-to":[{to:R()}],rounded:[{rounded:q()}],"rounded-s":[{"rounded-s":q()}],"rounded-e":[{"rounded-e":q()}],"rounded-t":[{"rounded-t":q()}],"rounded-r":[{"rounded-r":q()}],"rounded-b":[{"rounded-b":q()}],"rounded-l":[{"rounded-l":q()}],"rounded-ss":[{"rounded-ss":q()}],"rounded-se":[{"rounded-se":q()}],"rounded-ee":[{"rounded-ee":q()}],"rounded-es":[{"rounded-es":q()}],"rounded-tl":[{"rounded-tl":q()}],"rounded-tr":[{"rounded-tr":q()}],"rounded-br":[{"rounded-br":q()}],"rounded-bl":[{"rounded-bl":q()}],"border-w":[{border:U()}],"border-w-x":[{"border-x":U()}],"border-w-y":[{"border-y":U()}],"border-w-s":[{"border-s":U()}],"border-w-e":[{"border-e":U()}],"border-w-t":[{"border-t":U()}],"border-w-r":[{"border-r":U()}],"border-w-b":[{"border-b":U()}],"border-w-l":[{"border-l":U()}],"divide-x":[{"divide-x":U()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":U()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ae(),"hidden","none"]}],"divide-style":[{divide:[...ae(),"hidden","none"]}],"border-color":[{border:R()}],"border-color-x":[{"border-x":R()}],"border-color-y":[{"border-y":R()}],"border-color-s":[{"border-s":R()}],"border-color-e":[{"border-e":R()}],"border-color-t":[{"border-t":R()}],"border-color-r":[{"border-r":R()}],"border-color-b":[{"border-b":R()}],"border-color-l":[{"border-l":R()}],"divide-color":[{divide:R()}],"outline-style":[{outline:[...ae(),"none","hidden"]}],"outline-offset":[{"outline-offset":[K,z,L]}],"outline-w":[{outline:["",K,et,De]}],"outline-color":[{outline:R()}],shadow:[{shadow:["","none",f,ht,pt]}],"shadow-color":[{shadow:R()}],"inset-shadow":[{"inset-shadow":["none",d,ht,pt]}],"inset-shadow-color":[{"inset-shadow":R()}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:R()}],"ring-offset-w":[{"ring-offset":[K,De]}],"ring-offset-color":[{"ring-offset":R()}],"inset-ring-w":[{"inset-ring":U()}],"inset-ring-color":[{"inset-ring":R()}],"text-shadow":[{"text-shadow":["none",g,ht,pt]}],"text-shadow-color":[{"text-shadow":R()}],opacity:[{opacity:[K,z,L]}],"mix-blend":[{"mix-blend":[...Ze(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Ze()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[K]}],"mask-image-linear-from-pos":[{"mask-linear-from":H()}],"mask-image-linear-to-pos":[{"mask-linear-to":H()}],"mask-image-linear-from-color":[{"mask-linear-from":R()}],"mask-image-linear-to-color":[{"mask-linear-to":R()}],"mask-image-t-from-pos":[{"mask-t-from":H()}],"mask-image-t-to-pos":[{"mask-t-to":H()}],"mask-image-t-from-color":[{"mask-t-from":R()}],"mask-image-t-to-color":[{"mask-t-to":R()}],"mask-image-r-from-pos":[{"mask-r-from":H()}],"mask-image-r-to-pos":[{"mask-r-to":H()}],"mask-image-r-from-color":[{"mask-r-from":R()}],"mask-image-r-to-color":[{"mask-r-to":R()}],"mask-image-b-from-pos":[{"mask-b-from":H()}],"mask-image-b-to-pos":[{"mask-b-to":H()}],"mask-image-b-from-color":[{"mask-b-from":R()}],"mask-image-b-to-color":[{"mask-b-to":R()}],"mask-image-l-from-pos":[{"mask-l-from":H()}],"mask-image-l-to-pos":[{"mask-l-to":H()}],"mask-image-l-from-color":[{"mask-l-from":R()}],"mask-image-l-to-color":[{"mask-l-to":R()}],"mask-image-x-from-pos":[{"mask-x-from":H()}],"mask-image-x-to-pos":[{"mask-x-to":H()}],"mask-image-x-from-color":[{"mask-x-from":R()}],"mask-image-x-to-color":[{"mask-x-to":R()}],"mask-image-y-from-pos":[{"mask-y-from":H()}],"mask-image-y-to-pos":[{"mask-y-to":H()}],"mask-image-y-from-color":[{"mask-y-from":R()}],"mask-image-y-to-color":[{"mask-y-to":R()}],"mask-image-radial":[{"mask-radial":[z,L]}],"mask-image-radial-from-pos":[{"mask-radial-from":H()}],"mask-image-radial-to-pos":[{"mask-radial-to":H()}],"mask-image-radial-from-color":[{"mask-radial-from":R()}],"mask-image-radial-to-color":[{"mask-radial-to":R()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":j()}],"mask-image-conic-pos":[{"mask-conic":[K]}],"mask-image-conic-from-pos":[{"mask-conic-from":H()}],"mask-image-conic-to-pos":[{"mask-conic-to":H()}],"mask-image-conic-from-color":[{"mask-conic-from":R()}],"mask-image-conic-to-color":[{"mask-conic-to":R()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:V()}],"mask-repeat":[{mask:W()}],"mask-size":[{mask:Q()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",z,L]}],filter:[{filter:["","none",z,L]}],blur:[{blur:Ue()}],brightness:[{brightness:[K,z,L]}],contrast:[{contrast:[K,z,L]}],"drop-shadow":[{"drop-shadow":["","none",m,ht,pt]}],"drop-shadow-color":[{"drop-shadow":R()}],grayscale:[{grayscale:["",K,z,L]}],"hue-rotate":[{"hue-rotate":[K,z,L]}],invert:[{invert:["",K,z,L]}],saturate:[{saturate:[K,z,L]}],sepia:[{sepia:["",K,z,L]}],"backdrop-filter":[{"backdrop-filter":["","none",z,L]}],"backdrop-blur":[{"backdrop-blur":Ue()}],"backdrop-brightness":[{"backdrop-brightness":[K,z,L]}],"backdrop-contrast":[{"backdrop-contrast":[K,z,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",K,z,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[K,z,L]}],"backdrop-invert":[{"backdrop-invert":["",K,z,L]}],"backdrop-opacity":[{"backdrop-opacity":[K,z,L]}],"backdrop-saturate":[{"backdrop-saturate":[K,z,L]}],"backdrop-sepia":[{"backdrop-sepia":["",K,z,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":E()}],"border-spacing-x":[{"border-spacing-x":E()}],"border-spacing-y":[{"border-spacing-y":E()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",z,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[K,"initial",z,L]}],ease:[{ease:["linear","initial",w,z,L]}],delay:[{delay:[K,z,L]}],animate:[{animate:["none",y,z,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,z,L]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:Ie()}],"rotate-x":[{"rotate-x":Ie()}],"rotate-y":[{"rotate-y":Ie()}],"rotate-z":[{"rotate-z":Ie()}],scale:[{scale:Ae()}],"scale-x":[{"scale-x":Ae()}],"scale-y":[{"scale-y":Ae()}],"scale-z":[{"scale-z":Ae()}],"scale-3d":["scale-3d"],skew:[{skew:We()}],"skew-x":[{"skew-x":We()}],"skew-y":[{"skew-y":We()}],transform:[{transform:[z,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Oe()}],"translate-x":[{"translate-x":Oe()}],"translate-y":[{"translate-y":Oe()}],"translate-z":[{"translate-z":Oe()}],"translate-none":["translate-none"],accent:[{accent:R()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:R()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",z,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":E()}],"scroll-mx":[{"scroll-mx":E()}],"scroll-my":[{"scroll-my":E()}],"scroll-ms":[{"scroll-ms":E()}],"scroll-me":[{"scroll-me":E()}],"scroll-mt":[{"scroll-mt":E()}],"scroll-mr":[{"scroll-mr":E()}],"scroll-mb":[{"scroll-mb":E()}],"scroll-ml":[{"scroll-ml":E()}],"scroll-p":[{"scroll-p":E()}],"scroll-px":[{"scroll-px":E()}],"scroll-py":[{"scroll-py":E()}],"scroll-ps":[{"scroll-ps":E()}],"scroll-pe":[{"scroll-pe":E()}],"scroll-pt":[{"scroll-pt":E()}],"scroll-pr":[{"scroll-pr":E()}],"scroll-pb":[{"scroll-pb":E()}],"scroll-pl":[{"scroll-pl":E()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",z,L]}],fill:[{fill:["none",...R()]}],"stroke-w":[{stroke:[K,et,De,Bt]}],stroke:[{stroke:["none",...R()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},bi=Js(gi);function B(...e){return bi(Qn(e))}const yi=ms;function xi({className:e,...t}){return c.jsx(gs,{className:B("fixed top-0 right-0 z-50 flex max-h-screen w-full flex-col-reverse p-4 sm:top-auto sm:bottom-0 sm:flex-col md:max-w-[400px]",e),...t})}const wi=xr("group pointer-events-auto relative flex w-full items-center justify-between overflow-hidden rounded-md border p-4 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:data-[swipe-direction=left]:slide-out-to-left-full data-[state=closed]:data-[swipe-direction=right]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"border-t-4 border-t-destructive border bg-background text-destructive",success:"border-t-4 border-t-green-500 border bg-background text-green-900 dark:text-green-100",error:"border-t-4 border-t-red-500 border bg-background text-red-900 dark:text-red-100",info:"border-t-4 border-t-blue-500 border bg-background text-blue-900 dark:text-blue-100",warning:"border-t-4 border-t-yellow-500 border bg-background text-yellow-900 dark:text-yellow-100"}},defaultVariants:{variant:"default"}});function _i({className:e,variant:t,...r}){return c.jsx(bs,{className:B(wi({variant:t}),e),...r})}function ji({className:e,asChild:t=!1,...r}){return c.jsx(ws,{className:B(!t&&"group focus-visible:border-ring focus-visible:ring-ring/50 absolute top-3 right-3 flex size-7 items-center justify-center rounded transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:pointer-events-none",e),"toast-close":"",asChild:t,...r,children:t?r.children:c.jsx(wr,{size:16,className:"opacity-60 transition-opacity group-hover:opacity-100","aria-hidden":"true"})})}function Si({className:e,...t}){return c.jsx(ys,{className:B("text-sm font-medium",e),...t})}function Ei({className:e,...t}){return c.jsx(xs,{className:B("text-muted-foreground text-sm",e),...t})}const Oi=1,Ci=1e6;let $t=0;function Mi(){return $t=($t+1)%Number.MAX_SAFE_INTEGER,$t.toString()}const Kt=new Map,$r=e=>{if(Kt.has(e))return;const t=setTimeout(()=>{Kt.delete(e),nt({type:"REMOVE_TOAST",toastId:e})},Ci);Kt.set(e,t)},Pi=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Oi)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(r=>r.id===t.toast.id?{...r,...t.toast}:r)};case"DISMISS_TOAST":{const{toastId:r}=t;return r?$r(r):e.toasts.forEach(n=>{$r(n.id)}),{...e,toasts:e.toasts.map(n=>n.id===r||r===void 0?{...n,open:!1}:n)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(r=>r.id!==t.toastId)}}},yt=[];let xt={toasts:[]};function nt(e){xt=Pi(xt,e),yt.forEach(t=>{t(xt)})}function Ti({...e}){const t=Mi(),r=o=>nt({type:"UPDATE_TOAST",toast:{...o,id:t}}),n=()=>nt({type:"DISMISS_TOAST",toastId:t});return nt({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||n()}}}),{id:t,dismiss:n,update:r}}function ki(){const[e,t]=T.useState(xt);return T.useEffect(()=>(yt.push(t),()=>{const r=yt.indexOf(t);r>-1&&yt.splice(r,1)}),[e]),{...e,toast:Ti,dismiss:r=>nt({type:"DISMISS_TOAST",toastId:r})}}function uo(){const{toasts:e}=ki();return c.jsxs(yi,{children:[e.map(function({id:t,title:r,description:n,action:o,...s}){return c.jsx(_i,{...s,children:c.jsxs("div",{className:"flex w-full justify-between gap-2",children:[c.jsxs("div",{className:"flex flex-col gap-3",children:[c.jsxs("div",{className:"space-y-1",children:[r&&c.jsx(Si,{children:r}),n&&c.jsx(Ei,{children:n})]}),c.jsx("div",{children:o})]}),c.jsx("div",{children:c.jsx(ji,{})})]})},t)}),c.jsx(xi,{className:"z-[9999]"})]})}/*! js-cookie v3.0.5 | MIT */function vt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)e[n]=r[n]}return e}var Ri={read:function(e){return e[0]==='"'&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}};function sr(e,t){function r(o,s,a){if(!(typeof document>"u")){a=vt({},t,a),typeof a.expires=="number"&&(a.expires=new Date(Date.now()+a.expires*864e5)),a.expires&&(a.expires=a.expires.toUTCString()),o=encodeURIComponent(o).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var l="";for(var i in a)a[i]&&(l+="; "+i,a[i]!==!0&&(l+="="+a[i].split(";")[0]));return document.cookie=o+"="+e.write(s,o)+l}}function n(o){if(!(typeof document>"u"||arguments.length&&!o)){for(var s=document.cookie?document.cookie.split("; "):[],a={},l=0;l<s.length;l++){var i=s[l].split("="),u=i.slice(1).join("=");try{var f=decodeURIComponent(i[0]);if(a[f]=e.read(u,f),o===f)break}catch{}}return o?a[o]:a}}return Object.create({set:r,get:n,remove:function(o,s){r(o,"",vt({},s,{expires:-1}))},withAttributes:function(o){return sr(this.converter,vt({},this.attributes,o))},withConverter:function(o){return sr(vt({},this.converter,o),this.attributes)}},{attributes:{value:Object.freeze(t)},converter:{value:Object.freeze(e)}})}var ot=sr(Ri,{path:"/"});const wt="active_theme",Ii="default-scaled";function Ai(e){if(!(typeof window>"u"))try{ot.remove(wt,{path:"/"}),ot.set(wt,e,{path:"/",expires:365,sameSite:"lax"});const t=ot.get(wt)}catch{}}const fo=T.createContext(void 0);function Ni({children:e,initialTheme:t}){const[r,n]=T.useState(()=>{if(typeof window<"u"){const o=ot.get(wt);if(o)return o}return t||Ii});return T.useEffect(()=>{Ai(r);const o=document.body;Array.from(o.classList).filter(a=>a.startsWith("theme-")).forEach(a=>o.classList.remove(a)),o.classList.add(`theme-${r}`),r.endsWith("-scaled")&&o.classList.add("theme-scaled")},[r]),c.jsx(fo.Provider,{value:{activeTheme:r,setActiveTheme:n},children:e})}function po(){const e=T.useContext(fo);if(e===void 0)throw new Error("useThemeConfig must be used within an ActiveThemeProvider");return e}const Di=e=>(e?.client??Nn).get({url:"/api/abp/application-configuration",...e}),Li=e=>(e?.client??Nn).get({url:"/api/Auth/logout",...e}),zi={GetAppConfig:"GetAppConfig"},ho=()=>Ho({queryKey:[zi.GetAppConfig],queryFn:async()=>{const{data:e}=await Di();return e},staleTime:60*60*1e3}),Bi=()=>{const{data:e}=ho();return{can:T.useCallback(r=>!!(e?.auth?.grantedPolicies&&e.auth.grantedPolicies[r]),[e?.auth?.grantedPolicies])}},$i=xr("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",primary:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 focus-visible:ring-primary/20 dark:focus-visible:ring-primary/40",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white shadow-xs hover:bg-green-700 focus-visible:ring-green-600/20 dark:focus-visible:ring-green-400/40 dark:bg-green-600/80",warning:"bg-yellow-600 text-white shadow-xs hover:bg-yellow-700 focus-visible:ring-yellow-600/20 dark:focus-visible:ring-yellow-400/40 dark:bg-yellow-600/80",info:"bg-blue-600 text-white shadow-xs hover:bg-blue-700 focus-visible:ring-blue-600/20 dark:focus-visible:ring-blue-400/40 dark:bg-blue-600/80",error:"bg-red-600 text-white shadow-xs hover:bg-red-700 focus-visible:ring-red-600/20 dark:focus-visible:ring-red-400/40 dark:bg-red-600/80"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Tt({className:e,variant:t,size:r,asChild:n=!1,...o}){const s=n?st:"button";return c.jsx(s,{"data-slot":"button",className:B($i({variant:t,size:r,className:e})),...o})}function Ki({message:e="You don't have permission to access this page.",showBackButton:t=!0}){return c.jsx("div",{className:"flex flex-col items-center justify-center min-h-[50vh] p-6",children:c.jsxs("div",{className:"flex flex-col items-center text-center space-y-4 max-w-md",children:[c.jsx(Ls,{className:"h-12 w-12 text-gray-400"}),c.jsx("h2",{className:"text-2xl font-semibold tracking-tight",children:"Access Restricted"}),c.jsx("p",{className:"text-gray-500",children:e}),t&&c.jsx(Tt,{onClick:()=>window.history.back(),variant:"outline",className:"mt-4",children:"Go Back"})]})})}function Fi({policy:e,children:t,fallback:r,message:n="You don't have permission to access this page."}){const{can:o}=Bi();return o(e)?c.jsx(c.Fragment,{children:t}):r?c.jsx(c.Fragment,{children:r}):c.jsx(Ki,{message:n})}const Vi=()=>{const{data:e}=ho();return e?.currentUser};function qi({...e}){return c.jsx(ea,{"data-slot":"collapsible",...e})}function Ui({...e}){return c.jsx(ta,{"data-slot":"collapsible-trigger",...e})}function Wi({...e}){return c.jsx(ra,{"data-slot":"collapsible-content",...e})}/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Hi={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=(e,t,r,n)=>{const o=T.forwardRef(({color:s="currentColor",size:a=24,stroke:l=2,title:i,className:u,children:f,...d},g)=>T.createElement("svg",{ref:g,...Hi[e],width:a,height:a,className:["tabler-icon",`tabler-icon-${t}`,u].join(" "),strokeWidth:l,stroke:s,...d},[i&&T.createElement("title",{key:"svg-title"},i),...n.map(([m,p])=>T.createElement(m,p)),...Array.isArray(f)?f:[f]]));return o.displayName=`${r}`,o};/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Gi=oe("outline","app-window","IconAppWindow",[["path",{d:"M3 5m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z",key:"svg-0"}],["path",{d:"M6 8h.01",key:"svg-1"}],["path",{d:"M9 8h.01",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Yi=oe("outline","brightness","IconBrightness",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 3l0 18",key:"svg-1"}],["path",{d:"M12 9l4.65 -4.65",key:"svg-2"}],["path",{d:"M12 14.3l7.37 -7.37",key:"svg-3"}],["path",{d:"M12 19.6l8.85 -8.85",key:"svg-4"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Xi=oe("outline","chevron-right","IconChevronRight",[["path",{d:"M9 6l6 6l-6 6",key:"svg-0"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Qi=oe("outline","credit-card","IconCreditCard",[["path",{d:"M3 5m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z",key:"svg-0"}],["path",{d:"M3 10l18 0",key:"svg-1"}],["path",{d:"M7 15l.01 0",key:"svg-2"}],["path",{d:"M11 15l2 0",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ji=oe("outline","dashboard","IconDashboard",[["path",{d:"M12 13m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-0"}],["path",{d:"M13.45 11.55l2.05 -2.05",key:"svg-1"}],["path",{d:"M6.4 20a9 9 0 1 1 11.2 0z",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Zi=oe("outline","database","IconDatabase",[["path",{d:"M12 6m-8 0a8 3 0 1 0 16 0a8 3 0 1 0 -16 0",key:"svg-0"}],["path",{d:"M4 6v6a8 3 0 0 0 16 0v-6",key:"svg-1"}],["path",{d:"M4 12v6a8 3 0 0 0 16 0v-6",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var ec=oe("outline","dots-vertical","IconDotsVertical",[["path",{d:"M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-0"}],["path",{d:"M12 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-1"}],["path",{d:"M12 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var tc=oe("outline","inner-shadow-top","IconInnerShadowTop",[["path",{d:"M5.636 5.636a9 9 0 1 0 12.728 12.728a9 9 0 0 0 -12.728 -12.728z",key:"svg-0"}],["path",{d:"M16.243 7.757a6 6 0 0 0 -8.486 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var rc=oe("outline","key","IconKey",[["path",{d:"M16.555 3.843l3.602 3.602a2.877 2.877 0 0 1 0 4.069l-2.643 2.643a2.877 2.877 0 0 1 -4.069 0l-.301 -.301l-6.558 6.558a2 2 0 0 1 -1.239 .578l-.175 .008h-1.172a1 1 0 0 1 -.993 -.883l-.007 -.117v-1.172a2 2 0 0 1 .467 -1.284l.119 -.13l.414 -.414h2v-2h2v-2l2.144 -2.144l-.301 -.301a2.877 2.877 0 0 1 0 -4.069l2.643 -2.643a2.877 2.877 0 0 1 4.069 0z",key:"svg-0"}],["path",{d:"M15 9h.01",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var nc=oe("outline","logout","IconLogout",[["path",{d:"M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M9 12h12l-3 -3",key:"svg-1"}],["path",{d:"M18 15l3 -3",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var oc=oe("outline","notification","IconNotification",[["path",{d:"M10 6h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3",key:"svg-0"}],["path",{d:"M17 7m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var ac=oe("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var sc=oe("outline","settings","IconSettings",[["path",{d:"M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z",key:"svg-0"}],["path",{d:"M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var ic=oe("outline","user-circle","IconUserCircle",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 10m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}],["path",{d:"M6.168 18.849a4 4 0 0 1 3.832 -2.849h4a4 4 0 0 1 3.834 2.855",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var cc=oe("outline","user","IconUser",[["path",{d:"M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0",key:"svg-0"}],["path",{d:"M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var lc=oe("outline","users-group","IconUsersGroup",[["path",{d:"M10 13a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-0"}],["path",{d:"M8 21v-1a2 2 0 0 1 2 -2h4a2 2 0 0 1 2 2v1",key:"svg-1"}],["path",{d:"M15 5a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-2"}],["path",{d:"M17 10h2a2 2 0 0 1 2 2v1",key:"svg-3"}],["path",{d:"M5 5a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-4"}],["path",{d:"M3 13v-1a2 2 0 0 1 2 -2h2",key:"svg-5"}]]);const uc={name:"Identity Provider"},dc=[{title:"Dashboard",url:"/admin",isActive:!1,icon:Ji},{title:"Claim Types",url:"/admin/claims",isActive:!1,icon:rc,permission:"IdentityServer.ClaimTypes"},{title:"Users",url:"/admin/users",isActive:!1,icon:cc,permission:"AbpIdentity.Users"},{title:"Roles",url:"/admin/users/roles",isActive:!1,icon:lc,permission:"AbpIdentity.Roles"},{title:"Clients",url:"#",isActive:!1,icon:Gi,permission:"IdentityServer.OpenIddictApplications",items:[{title:"Clients",url:"/admin/clients",permission:"IdentityServer.OpenIddictApplications"},{title:"Resources",url:"/admin/clients/resources",permission:"IdentityServer.OpenIddictResources"},{title:"Scopes",url:"/admin/clients/scopes",permission:"IdentityServer.OpenIddictScopes"}]},{title:"Tenants",url:"/admin/tenants",isActive:!1,icon:Zi,permission:"AbpTenantManagement.Tenants"},{title:"Settings",url:"/admin/settings",isActive:!1,icon:sc,permission:"SettingManagement.Emailing"}],Ft=768;function fc(){const[e,t]=T.useState(void 0);return T.useEffect(()=>{const r=window.matchMedia(`(max-width: ${Ft-1}px)`),n=()=>{t(window.innerWidth<Ft)};return r.addEventListener("change",n),t(window.innerWidth<Ft),()=>r.removeEventListener("change",n)},[]),!!e}var pc="Separator",Kr="horizontal",hc=["horizontal","vertical"],vo=T.forwardRef((e,t)=>{const{decorative:r,orientation:n=Kr,...o}=e,s=vc(n)?n:Kr,l=r?{role:"none"}:{"aria-orientation":s==="vertical"?s:void 0,role:"separator"};return c.jsx(Te.div,{"data-orientation":s,...l,...o,ref:t})});vo.displayName=pc;function vc(e){return hc.includes(e)}var mc=vo;function gc({className:e,orientation:t="horizontal",decorative:r=!0,...n}){return c.jsx(mc,{"data-slot":"separator",decorative:r,orientation:t,className:B("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...n})}function bc({...e}){return c.jsx(na,{"data-slot":"sheet",...e})}function yc({...e}){return c.jsx(ca,{"data-slot":"sheet-portal",...e})}function xc({className:e,...t}){return c.jsx(la,{"data-slot":"sheet-overlay",className:B("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function wc({className:e,children:t,side:r="right",...n}){return c.jsxs(yc,{children:[c.jsx(xc,{}),c.jsxs(oa,{"data-slot":"sheet-content",className:B("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",r==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",r==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",r==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",r==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...n,children:[t,c.jsxs(aa,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[c.jsx(wr,{className:"size-4"}),c.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function _c({className:e,...t}){return c.jsx("div",{"data-slot":"sheet-header",className:B("flex flex-col gap-1.5 p-4",e),...t})}function jc({className:e,...t}){return c.jsx(sa,{"data-slot":"sheet-title",className:B("text-foreground font-semibold",e),...t})}function Sc({className:e,...t}){return c.jsx(ia,{"data-slot":"sheet-description",className:B("text-muted-foreground text-sm",e),...t})}var[kt,bd]=Tn("Tooltip",[An]),Rt=An(),mo="TooltipProvider",Ec=700,ir="tooltip.open",[Oc,jr]=kt(mo),go=e=>{const{__scopeTooltip:t,delayDuration:r=Ec,skipDelayDuration:n=300,disableHoverableContent:o=!1,children:s}=e,a=T.useRef(!0),l=T.useRef(!1),i=T.useRef(0);return T.useEffect(()=>{const u=i.current;return()=>window.clearTimeout(u)},[]),c.jsx(Oc,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:T.useCallback(()=>{window.clearTimeout(i.current),a.current=!1},[]),onClose:T.useCallback(()=>{window.clearTimeout(i.current),i.current=window.setTimeout(()=>a.current=!0,n)},[n]),isPointerInTransitRef:l,onPointerInTransitChange:T.useCallback(u=>{l.current=u},[]),disableHoverableContent:o,children:s})};go.displayName=mo;var at="Tooltip",[Cc,ct]=kt(at),bo=e=>{const{__scopeTooltip:t,children:r,open:n,defaultOpen:o,onOpenChange:s,disableHoverableContent:a,delayDuration:l}=e,i=jr(at,e.__scopeTooltip),u=Rt(t),[f,d]=T.useState(null),g=ua(),m=T.useRef(0),p=a??i.disableHoverableContent,b=l??i.delayDuration,v=T.useRef(!1),[w,y]=kn({prop:n,defaultProp:o??!1,onChange:O=>{O?(i.onOpen(),document.dispatchEvent(new CustomEvent(ir))):i.onClose(),s?.(O)},caller:at}),h=T.useMemo(()=>w?v.current?"delayed-open":"instant-open":"closed",[w]),j=T.useCallback(()=>{window.clearTimeout(m.current),m.current=0,v.current=!1,y(!0)},[y]),C=T.useCallback(()=>{window.clearTimeout(m.current),m.current=0,y(!1)},[y]),P=T.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{v.current=!0,y(!0),m.current=0},b)},[b,y]);return T.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),c.jsx(da,{...u,children:c.jsx(Cc,{scope:t,contentId:g,open:w,stateAttribute:h,trigger:f,onTriggerChange:d,onTriggerEnter:T.useCallback(()=>{i.isOpenDelayedRef.current?P():j()},[i.isOpenDelayedRef,P,j]),onTriggerLeave:T.useCallback(()=>{p?C():(window.clearTimeout(m.current),m.current=0)},[C,p]),onOpen:j,onClose:C,disableHoverableContent:p,children:r})})};bo.displayName=at;var cr="TooltipTrigger",yo=T.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,o=ct(cr,r),s=jr(cr,r),a=Rt(r),l=T.useRef(null),i=Mt(t,l,o.onTriggerChange),u=T.useRef(!1),f=T.useRef(!1),d=T.useCallback(()=>u.current=!1,[]);return T.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),c.jsx(fa,{asChild:!0,...a,children:c.jsx(Te.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...n,ref:i,onPointerMove:ne(e.onPointerMove,g=>{g.pointerType!=="touch"&&!f.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),f.current=!0)}),onPointerLeave:ne(e.onPointerLeave,()=>{o.onTriggerLeave(),f.current=!1}),onPointerDown:ne(e.onPointerDown,()=>{o.open&&o.onClose(),u.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:ne(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:ne(e.onBlur,o.onClose),onClick:ne(e.onClick,o.onClose)})})});yo.displayName=cr;var Sr="TooltipPortal",[Mc,Pc]=kt(Sr,{forceMount:void 0}),xo=e=>{const{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,s=ct(Sr,t);return c.jsx(Mc,{scope:t,forceMount:r,children:c.jsx(gr,{present:r||s.open,children:c.jsx(In,{asChild:!0,container:o,children:n})})})};xo.displayName=Sr;var Ge="TooltipContent",wo=T.forwardRef((e,t)=>{const r=Pc(Ge,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...s}=e,a=ct(Ge,e.__scopeTooltip);return c.jsx(gr,{present:n||a.open,children:a.disableHoverableContent?c.jsx(_o,{side:o,...s,ref:t}):c.jsx(Tc,{side:o,...s,ref:t})})}),Tc=T.forwardRef((e,t)=>{const r=ct(Ge,e.__scopeTooltip),n=jr(Ge,e.__scopeTooltip),o=T.useRef(null),s=Mt(t,o),[a,l]=T.useState(null),{trigger:i,onClose:u}=r,f=o.current,{onPointerInTransitChange:d}=n,g=T.useCallback(()=>{l(null),d(!1)},[d]),m=T.useCallback((p,b)=>{const v=p.currentTarget,w={x:p.clientX,y:p.clientY},y=Ac(w,v.getBoundingClientRect()),h=Nc(w,y),j=Dc(b.getBoundingClientRect()),C=zc([...h,...j]);l(C),d(!0)},[d]);return T.useEffect(()=>()=>g(),[g]),T.useEffect(()=>{if(i&&f){const p=v=>m(v,f),b=v=>m(v,i);return i.addEventListener("pointerleave",p),f.addEventListener("pointerleave",b),()=>{i.removeEventListener("pointerleave",p),f.removeEventListener("pointerleave",b)}}},[i,f,m,g]),T.useEffect(()=>{if(a){const p=b=>{const v=b.target,w={x:b.clientX,y:b.clientY},y=i?.contains(v)||f?.contains(v),h=!Lc(w,a);y?g():h&&(g(),u())};return document.addEventListener("pointermove",p),()=>document.removeEventListener("pointermove",p)}},[i,f,a,u,g]),c.jsx(_o,{...e,ref:s})}),[kc,Rc]=kt(at,{isInside:!1}),Ic=ma("TooltipContent"),_o=T.forwardRef((e,t)=>{const{__scopeTooltip:r,children:n,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:a,...l}=e,i=ct(Ge,r),u=Rt(r),{onClose:f}=i;return T.useEffect(()=>(document.addEventListener(ir,f),()=>document.removeEventListener(ir,f)),[f]),T.useEffect(()=>{if(i.trigger){const d=g=>{g.target?.contains(i.trigger)&&f()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[i.trigger,f]),c.jsx(ha,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:a,onFocusOutside:d=>d.preventDefault(),onDismiss:f,children:c.jsxs(va,{"data-state":i.stateAttribute,...u,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[c.jsx(Ic,{children:n}),c.jsx(kc,{scope:r,isInside:!0,children:c.jsx(ga,{id:i.contentId,role:"tooltip",children:o||n})})]})})});wo.displayName=Ge;var jo="TooltipArrow",So=T.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,o=Rt(r);return Rc(jo,r).isInside?null:c.jsx(pa,{...o,...n,ref:t})});So.displayName=jo;function Ac(e,t){const r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(r,n,o,s)){case s:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function Nc(e,t,r=5){const n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r});break}return n}function Dc(e){const{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}function Lc(e,t){const{x:r,y:n}=e;let o=!1;for(let s=0,a=t.length-1;s<t.length;a=s++){const l=t[s],i=t[a],u=l.x,f=l.y,d=i.x,g=i.y;f>n!=g>n&&r<(d-u)*(n-f)/(g-f)+u&&(o=!o)}return o}function zc(e){const t=e.slice();return t.sort((r,n)=>r.x<n.x?-1:r.x>n.x?1:r.y<n.y?-1:r.y>n.y?1:0),Bc(t)}function Bc(e){if(e.length<=1)return e.slice();const t=[];for(let n=0;n<e.length;n++){const o=e[n];for(;t.length>=2;){const s=t[t.length-1],a=t[t.length-2];if((s.x-a.x)*(o.y-a.y)>=(s.y-a.y)*(o.x-a.x))t.pop();else break}t.push(o)}t.pop();const r=[];for(let n=e.length-1;n>=0;n--){const o=e[n];for(;r.length>=2;){const s=r[r.length-1],a=r[r.length-2];if((s.x-a.x)*(o.y-a.y)>=(s.y-a.y)*(o.x-a.x))r.pop();else break}r.push(o)}return r.pop(),t.length===1&&r.length===1&&t[0].x===r[0].x&&t[0].y===r[0].y?t:t.concat(r)}var $c=go,Kc=bo,Fc=yo,Vc=xo,qc=wo,Uc=So;function Eo({delayDuration:e=0,...t}){return c.jsx($c,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function Wc({...e}){return c.jsx(Eo,{children:c.jsx(Kc,{"data-slot":"tooltip",...e})})}function Hc({...e}){return c.jsx(Fc,{"data-slot":"tooltip-trigger",...e})}function Gc({className:e,sideOffset:t=0,children:r,...n}){return c.jsx(Vc,{children:c.jsxs(qc,{"data-slot":"tooltip-content",sideOffset:t,className:B("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...n,children:[r,c.jsx(Uc,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const Yc="sidebar_state",Xc=60*60*24*7,Qc="16rem",Jc="18rem",Zc="3rem",el="b",Oo=T.createContext(null);function lt(){const e=T.useContext(Oo);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function tl({defaultOpen:e=!0,open:t,onOpenChange:r,className:n,style:o,children:s,...a}){const l=fc(),[i,u]=T.useState(!1),[f,d]=T.useState(e),g=t??f,m=T.useCallback(w=>{const y=typeof w=="function"?w(g):w;r?r(y):d(y),document.cookie=`${Yc}=${y}; path=/; max-age=${Xc}`},[r,g]),p=T.useCallback(()=>l?u(w=>!w):m(w=>!w),[l,m,u]);T.useEffect(()=>{const w=y=>{y.key===el&&(y.metaKey||y.ctrlKey)&&(y.preventDefault(),p())};return window.addEventListener("keydown",w),()=>window.removeEventListener("keydown",w)},[p]);const b=g?"expanded":"collapsed",v=T.useMemo(()=>({state:b,open:g,setOpen:m,isMobile:l,openMobile:i,setOpenMobile:u,toggleSidebar:p}),[b,g,m,l,i,u,p]);return c.jsx(Oo.Provider,{value:v,children:c.jsx(Eo,{delayDuration:0,children:c.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":Qc,"--sidebar-width-icon":Zc,...o},className:B("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",n),...a,children:s})})})}function rl({side:e="left",variant:t="sidebar",collapsible:r="offcanvas",className:n,children:o,...s}){const{isMobile:a,state:l,openMobile:i,setOpenMobile:u}=lt();return r==="none"?c.jsx("div",{"data-slot":"sidebar",className:B("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",n),...s,children:o}):a?c.jsx(bc,{open:i,onOpenChange:u,...s,children:c.jsxs(wc,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":Jc},side:e,children:[c.jsxs(_c,{className:"sr-only",children:[c.jsx(jc,{children:"Sidebar"}),c.jsx(Sc,{children:"Displays the mobile sidebar."})]}),c.jsx("div",{className:"flex h-full w-full flex-col",children:o})]})}):c.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":l,"data-collapsible":l==="collapsed"?r:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[c.jsx("div",{"data-slot":"sidebar-gap",className:B("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),c.jsx("div",{"data-slot":"sidebar-container",className:B("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...s,children:c.jsx("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:o})})]})}function nl({className:e,onClick:t,...r}){const{toggleSidebar:n}=lt();return c.jsxs(Tt,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:B("size-7",e),onClick:o=>{t?.(o),n()},...r,children:[c.jsx(Ns,{}),c.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function ol({className:e,...t}){const{toggleSidebar:r}=lt();return c.jsx("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:B("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})}function al({className:e,...t}){return c.jsx("main",{"data-slot":"sidebar-inset",className:B("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...t})}function sl({className:e,...t}){return c.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:B("flex flex-col gap-2 p-2",e),...t})}function il({className:e,...t}){return c.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:B("flex flex-col gap-2 p-2",e),...t})}function cl({className:e,...t}){return c.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:B("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function ll({className:e,...t}){return c.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:B("relative flex w-full min-w-0 flex-col p-2",e),...t})}function ul({className:e,asChild:t=!1,...r}){const n=t?st:"div";return c.jsx(n,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:B("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...r})}function lr({className:e,...t}){return c.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:B("flex w-full min-w-0 flex-col gap-1",e),...t})}function _t({className:e,...t}){return c.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:B("group/menu-item relative",e),...t})}const dl=xr("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function jt({asChild:e=!1,isActive:t=!1,variant:r="default",size:n="default",tooltip:o,className:s,...a}){const l=e?st:"button",{isMobile:i,state:u}=lt(),f=c.jsx(l,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":n,"data-active":t,className:B(dl({variant:r,size:n}),s),...a});return o?(typeof o=="string"&&(o={children:o}),c.jsxs(Wc,{children:[c.jsx(Hc,{asChild:!0,children:f}),c.jsx(Gc,{side:"right",align:"center",hidden:u!=="collapsed"||i,...o})]})):f}function fl({className:e,...t}){return c.jsx("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:B("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t})}function pl({className:e,...t}){return c.jsx("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:B("group/menu-sub-item relative",e),...t})}function hl({asChild:e=!1,size:t="md",isActive:r=!1,className:n,...o}){const s=e?st:"a";return c.jsx(s,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":t,"data-active":r,className:B("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",t==="sm"&&"text-xs",t==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",n),...o})}function vl(){const[e,t]=T.useState(!1);return T.useEffect(()=>{const r=window.matchMedia("(max-width: 768px)");t(r.matches);const n=o=>{t(o.matches)};return r.addEventListener("change",n),()=>r.removeEventListener("change",n)},[]),{isOpen:e}}function Fr({className:e,...t}){return c.jsx(ba,{"data-slot":"avatar",className:B("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function Vr({className:e,...t}){return c.jsx(ya,{"data-slot":"avatar-fallback",className:B("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}function ml({...e}){return c.jsx(xa,{"data-slot":"dropdown-menu",...e})}function gl({...e}){return c.jsx(wa,{"data-slot":"dropdown-menu-trigger",...e})}function bl({className:e,sideOffset:t=4,...r}){return c.jsx(_a,{children:c.jsx(ja,{"data-slot":"dropdown-menu-content",sideOffset:t,className:B("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function yl({...e}){return c.jsx(Oa,{"data-slot":"dropdown-menu-group",...e})}function mt({className:e,inset:t,variant:r="default",...n}){return c.jsx(Ca,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:B("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function xl({className:e,inset:t,...r}){return c.jsx(Sa,{"data-slot":"dropdown-menu-label","data-inset":t,className:B("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r})}function qr({className:e,...t}){return c.jsx(Ea,{"data-slot":"dropdown-menu-separator",className:B("bg-border -mx-1 my-1 h-px",e),...t})}function wl({user:e}){const{isMobile:t}=lt(),r=o=>o?o.slice(0,2).toUpperCase():"",n=async()=>{try{await Li(),window.location.reload()}catch{}};return c.jsx(lr,{children:c.jsx(_t,{children:c.jsxs(ml,{children:[c.jsx(gl,{asChild:!0,children:c.jsxs(jt,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[c.jsx(Fr,{className:"h-8 w-8 rounded-lg grayscale",children:c.jsx(Vr,{className:"rounded-lg",children:r(e?.name??"")})}),c.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[c.jsx("span",{className:"truncate font-medium",children:e.name}),c.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]}),c.jsx(ec,{className:"ml-auto size-4"})]})}),c.jsxs(bl,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:t?"bottom":"right",align:"end",sideOffset:4,children:[c.jsx(xl,{className:"p-0 font-normal",children:c.jsxs("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[c.jsx(Fr,{className:"h-8 w-8 rounded-lg",children:c.jsx(Vr,{className:"rounded-lg",children:r(e?.name??"")})}),c.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[c.jsx("span",{className:"truncate font-medium",children:e.name}),c.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]})]})}),c.jsx(qr,{}),c.jsxs(yl,{children:[c.jsxs(mt,{children:[c.jsx(ic,{}),"Account"]}),c.jsxs(mt,{children:[c.jsx(Qi,{}),"Billing"]}),c.jsxs(mt,{children:[c.jsx(oc,{}),"Notifications"]})]}),c.jsx(qr,{}),c.jsxs(mt,{onClick:n,children:[c.jsx(nc,{}),"Log out"]})]})]})})})}function _l(){const e=Vi(),{url:t}=Dn(),r=t,{isOpen:n}=vl();return T.useEffect(()=>{},[n]),c.jsxs(rl,{collapsible:"icon",children:[c.jsx(sl,{children:c.jsx(lr,{children:c.jsx(_t,{children:c.jsx(jt,{asChild:!0,className:"data-[slot=sidebar-menu-button]:!p-1.5",children:c.jsxs("a",{href:"#",children:[c.jsx(tc,{className:"!size-5"}),c.jsx("span",{className:"text-base font-semibold",children:uc.name})]})})})})}),c.jsx(cl,{className:"overflow-x-hidden",children:c.jsxs(ll,{children:[c.jsx(ul,{children:"Overview"}),c.jsx(lr,{children:dc.map(o=>{const s=o.items?.some(a=>r.startsWith(a.url));return o?.items&&o?.items?.length>0?c.jsx(qi,{asChild:!0,defaultOpen:s||o.isActive,className:"group/collapsible",children:c.jsxs(_t,{children:[c.jsx(Ui,{asChild:!0,children:c.jsxs(jt,{tooltip:o.title,isActive:r===o.url,children:[o.icon&&c.jsx(o.icon,{}),c.jsx("span",{children:o.title}),c.jsx(Xi,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),c.jsx(Wi,{children:c.jsx(fl,{children:o.items?.map(a=>c.jsx(pl,{children:c.jsx(hl,{asChild:!0,isActive:r===a.url,children:c.jsx(Et,{href:a.url,children:c.jsx("span",{children:a.title})})})},a.title))})})]})},o.title):c.jsx(_t,{children:c.jsx(jt,{asChild:!0,tooltip:o.title,isActive:r===o.url,children:c.jsxs(Et,{href:o.url,children:[c.jsx(o.icon,{}),c.jsx("span",{children:o.title})]})})},o.title)})})]})}),c.jsx(il,{children:e&&c.jsx(wl,{user:e})}),c.jsx(ol,{})]})}function jl({...e}){return c.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function Sl({className:e,...t}){return c.jsx("ol",{"data-slot":"breadcrumb-list",className:B("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function Ur({className:e,...t}){return c.jsx("li",{"data-slot":"breadcrumb-item",className:B("inline-flex items-center gap-1.5",e),...t})}function Wr({asChild:e,className:t,...r}){const n=e?st:"a";return c.jsx(n,{"data-slot":"breadcrumb-link",className:B("hover:text-foreground transition-colors",t),...r})}function El({className:e,...t}){return c.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:B("text-foreground font-normal",e),...t})}function Hr({children:e,className:t,...r}){return c.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:B("[&>svg]:size-3.5",t),...r,children:e??c.jsx(ks,{})})}function Ol(){const{url:e}=Dn();if(e==="/")return null;const t=e.split("/").filter(Boolean),r=n=>n.split("-").map(o=>o.charAt(0).toUpperCase()+o.slice(1)).join(" ");return c.jsx(jl,{children:c.jsxs(Sl,{children:[c.jsx(Ur,{children:c.jsx(Wr,{asChild:!0,children:c.jsx(Et,{href:"/",children:"Home"})})}),c.jsx(Hr,{}),t.map((n,o)=>{const s=`/${t.slice(0,o+1).join("/")}`,a=o===t.length-1;return c.jsxs(T.Fragment,{children:[c.jsx(Ur,{children:a?c.jsx(El,{children:r(n)}):c.jsx(Wr,{asChild:!0,children:c.jsx(Et,{href:s,children:r(n)})})}),!a&&c.jsx(Hr,{})]},s)})]})})}var Le={},F={},Gr;function qe(){if(Gr)return F;Gr=1;var e=F&&F.__assign||function(){return e=Object.assign||function(y){for(var h,j=1,C=arguments.length;j<C;j++){h=arguments[j];for(var P in h)Object.prototype.hasOwnProperty.call(h,P)&&(y[P]=h[P])}return y},e.apply(this,arguments)},t=F&&F.__createBinding||(Object.create?function(y,h,j,C){C===void 0&&(C=j),Object.defineProperty(y,C,{enumerable:!0,get:function(){return h[j]}})}:function(y,h,j,C){C===void 0&&(C=j),y[C]=h[j]}),r=F&&F.__setModuleDefault||(Object.create?function(y,h){Object.defineProperty(y,"default",{enumerable:!0,value:h})}:function(y,h){y.default=h}),n=F&&F.__importStar||function(y){if(y&&y.__esModule)return y;var h={};if(y!=null)for(var j in y)j!=="default"&&Object.prototype.hasOwnProperty.call(y,j)&&t(h,y,j);return r(h,y),h},o=F&&F.__spreadArray||function(y,h,j){if(j||arguments.length===2)for(var C=0,P=h.length,O;C<P;C++)(O||!(C in h))&&(O||(O=Array.prototype.slice.call(h,0,C)),O[C]=h[C]);return y.concat(O||Array.prototype.slice.call(h))};Object.defineProperty(F,"__esModule",{value:!0}),F.Priority=F.isModKey=F.shouldRejectKeystrokes=F.useThrottledValue=F.getScrollbarWidth=F.useIsomorphicLayout=F.noop=F.createAction=F.randomId=F.usePointerMovedSinceMount=F.useOuterClick=F.swallowEvent=void 0;var s=n(re());function a(y){y.stopPropagation(),y.preventDefault()}F.swallowEvent=a;function l(y,h){var j=s.useRef(h);j.current=h,s.useEffect(function(){function C(P){var O,E;!((O=y.current)===null||O===void 0)&&O.contains(P.target)||P.target===((E=y.current)===null||E===void 0?void 0:E.getRootNode().host)||(P.preventDefault(),P.stopPropagation(),j.current())}return window.addEventListener("pointerdown",C,!0),function(){return window.removeEventListener("pointerdown",C,!0)}},[y])}F.useOuterClick=l;function i(){var y=s.useState(!1),h=y[0],j=y[1];return s.useEffect(function(){function C(){j(!0)}if(!h)return window.addEventListener("pointermove",C),function(){return window.removeEventListener("pointermove",C)}},[h]),h}F.usePointerMovedSinceMount=i;function u(){return Math.random().toString(36).substring(2,9)}F.randomId=u;function f(y){return e({id:u()},y)}F.createAction=f;function d(){}F.noop=d,F.useIsomorphicLayout=typeof window>"u"?d:s.useLayoutEffect;function g(){var y=document.createElement("div");y.style.visibility="hidden",y.style.overflow="scroll",document.body.appendChild(y);var h=document.createElement("div");y.appendChild(h);var j=y.offsetWidth-h.offsetWidth;return y.parentNode.removeChild(y),j}F.getScrollbarWidth=g;function m(y,h){h===void 0&&(h=100);var j=s.useState(y),C=j[0],P=j[1],O=s.useRef(Date.now());return s.useEffect(function(){if(h!==0){var E=setTimeout(function(){P(y),O.current=Date.now()},O.current-(Date.now()-h));return function(){clearTimeout(E)}}},[h,y]),h===0?y:C}F.useThrottledValue=m;function p(y){var h,j,C,P=y===void 0?{ignoreWhenFocused:[]}:y,O=P.ignoreWhenFocused,E=o(["input","textarea"],O,!0).map(function(_){return _.toLowerCase()}),M=document.activeElement,x=M&&(E.indexOf(M.tagName.toLowerCase())!==-1||((h=M.attributes.getNamedItem("role"))===null||h===void 0?void 0:h.value)==="textbox"||((j=M.attributes.getNamedItem("contenteditable"))===null||j===void 0?void 0:j.value)==="true"||((C=M.attributes.getNamedItem("contenteditable"))===null||C===void 0?void 0:C.value)==="plaintext-only");return x}F.shouldRejectKeystrokes=p;var b=typeof window>"u",v=!b&&window.navigator.platform==="MacIntel";function w(y){return v?y.metaKey:y.ctrlKey}return F.isModKey=w,F.Priority={HIGH:1,NORMAL:0,LOW:-1},F}var ve={},ce={},je={},se={},rt={exports:{}},Cl=rt.exports,Yr;function Ml(){return Yr||(Yr=1,function(e,t){(function(r,n){n(t)})(Cl,function(r){var n=typeof WeakSet=="function",o=Object.keys;function s(x,_){return x===_||x!==x&&_!==_}function a(x){return x.constructor===Object||x.constructor==null}function l(x){return!!x&&typeof x.then=="function"}function i(x){return!!(x&&x.$$typeof)}function u(){var x=[];return{add:function(_){x.push(_)},has:function(_){return x.indexOf(_)!==-1}}}var f=function(x){return x?function(){return new WeakSet}:u}(n);function d(x){return function(I){var k=x||I;return function(N,A,D){D===void 0&&(D=f());var R=!!N&&typeof N=="object",V=!!A&&typeof A=="object";if(R||V){var W=R&&D.has(N),Q=V&&D.has(A);if(W||Q)return W&&Q;R&&D.add(N),V&&D.add(A)}return k(N,A,D)}}}function g(x,_,I,k){var S=x.length;if(_.length!==S)return!1;for(;S-- >0;)if(!I(x[S],_[S],k))return!1;return!0}function m(x,_,I,k){var S=x.size===_.size;if(S&&x.size){var N={};x.forEach(function(A,D){if(S){var R=!1,V=0;_.forEach(function(W,Q){!R&&!N[V]&&(R=I(D,Q,k)&&I(A,W,k),R&&(N[V]=!0)),V++}),S=R}})}return S}var p="_owner",b=Function.prototype.bind.call(Function.prototype.call,Object.prototype.hasOwnProperty);function v(x,_,I,k){var S=o(x),N=S.length;if(o(_).length!==N)return!1;if(N)for(var A=void 0;N-- >0;){if(A=S[N],A===p){var D=i(x),R=i(_);if((D||R)&&D!==R)return!1}if(!b(_,A)||!I(x[A],_[A],k))return!1}return!0}function w(x,_){return x.source===_.source&&x.global===_.global&&x.ignoreCase===_.ignoreCase&&x.multiline===_.multiline&&x.unicode===_.unicode&&x.sticky===_.sticky&&x.lastIndex===_.lastIndex}function y(x,_,I,k){var S=x.size===_.size;if(S&&x.size){var N={};x.forEach(function(A){if(S){var D=!1,R=0;_.forEach(function(V){!D&&!N[R]&&(D=I(A,V,k),D&&(N[R]=!0)),R++}),S=D}})}return S}var h=typeof Map=="function",j=typeof Set=="function";function C(x){var _=typeof x=="function"?x(I):I;function I(k,S,N){if(k===S)return!0;if(k&&S&&typeof k=="object"&&typeof S=="object"){if(a(k)&&a(S))return v(k,S,_,N);var A=Array.isArray(k),D=Array.isArray(S);return A||D?A===D&&g(k,S,_,N):(A=k instanceof Date,D=S instanceof Date,A||D?A===D&&s(k.getTime(),S.getTime()):(A=k instanceof RegExp,D=S instanceof RegExp,A||D?A===D&&w(k,S):l(k)||l(S)?k===S:h&&(A=k instanceof Map,D=S instanceof Map,A||D)?A===D&&m(k,S,_,N):j&&(A=k instanceof Set,D=S instanceof Set,A||D)?A===D&&y(k,S,_,N):v(k,S,_,N)))}return k!==k&&S!==S}return I}var P=C(),O=C(function(){return s}),E=C(d()),M=C(d(s));r.circularDeepEqual=E,r.circularShallowEqual=M,r.createCustomEqual=C,r.deepEqual=P,r.sameValueZeroEqual=s,r.shallowEqual=O,Object.defineProperty(r,"__esModule",{value:!0})})}(rt,rt.exports)),rt.exports}var Vt,Xr;function Er(){if(Xr)return Vt;Xr=1;var e="Invariant failed";function t(r,n){if(!r)throw new Error(e)}return Vt=t,Vt}var Se={},ze={},tt={},Qr;function Pl(){if(Qr)return tt;Qr=1,Object.defineProperty(tt,"__esModule",{value:!0}),tt.Command=void 0;var e=function(){function t(r,n){var o=this;n===void 0&&(n={}),this.perform=function(){var s=r.perform();if(typeof s=="function"){var a=n.history;a&&(o.historyItem&&a.remove(o.historyItem),o.historyItem=a.add({perform:r.perform,negate:s}),o.history={undo:function(){return a.undo(o.historyItem)},redo:function(){return a.redo(o.historyItem)}})}}}return t}();return tt.Command=e,tt}var Jr;function Co(){if(Jr)return ze;Jr=1;var e=ze&&ze.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(ze,"__esModule",{value:!0}),ze.ActionImpl=void 0;var t=e(Er()),r=Pl(),n=qe(),o=function(a){var l=a.keywords,i=l===void 0?"":l,u=a.section,f=u===void 0?"":u;return(i+" "+(typeof f=="string"?f:f.name)).trim()},s=function(){function a(l,i){var u=this,f;this.priority=n.Priority.NORMAL,this.ancestors=[],this.children=[],Object.assign(this,l),this.id=l.id,this.name=l.name,this.keywords=o(l);var d=l.perform;if(this.command=d&&new r.Command({perform:function(){return d(u)}},{history:i.history}),this.perform=(f=this.command)===null||f===void 0?void 0:f.perform,l.parent){var g=i.store[l.parent];(0,t.default)(g,"attempted to create an action whos parent: "+l.parent+" does not exist in the store."),g.addChild(this)}}return a.prototype.addChild=function(l){l.ancestors.unshift(this);for(var i=this.parentActionImpl;i;)l.ancestors.unshift(i),i=i.parentActionImpl;this.children.push(l)},a.prototype.removeChild=function(l){var i=this,u=this.children.indexOf(l);u!==-1&&this.children.splice(u,1),l.children&&l.children.forEach(function(f){i.removeChild(f)})},Object.defineProperty(a.prototype,"parentActionImpl",{get:function(){return this.ancestors[this.ancestors.length-1]},enumerable:!1,configurable:!0}),a.create=function(l,i){return new a(l,i)},a}();return ze.ActionImpl=s,ze}var Zr;function Mo(){if(Zr)return Se;Zr=1;var e=Se&&Se.__assign||function(){return e=Object.assign||function(s){for(var a,l=1,i=arguments.length;l<i;l++){a=arguments[l];for(var u in a)Object.prototype.hasOwnProperty.call(a,u)&&(s[u]=a[u])}return s},e.apply(this,arguments)},t=Se&&Se.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(Se,"__esModule",{value:!0}),Se.ActionInterface=void 0;var r=t(Er()),n=Co(),o=function(){function s(a,l){a===void 0&&(a=[]),l===void 0&&(l={}),this.actions={},this.options=l,this.add(a)}return s.prototype.add=function(a){for(var l=0;l<a.length;l++){var i=a[l];i.parent&&(0,r.default)(this.actions[i.parent],'Attempted to create action "'+i.name+'" without registering its parent "'+i.parent+'" first.'),this.actions[i.id]=n.ActionImpl.create(i,{history:this.options.historyManager,store:this.actions})}return e({},this.actions)},s.prototype.remove=function(a){var l=this;return a.forEach(function(i){var u=l.actions[i.id];if(u){for(var f=u.children;f.length;){var d=f.pop();if(!d)return;delete l.actions[d.id],d.parentActionImpl&&d.parentActionImpl.removeChild(d),d.children&&f.push.apply(f,d.children)}u.parentActionImpl&&u.parentActionImpl.removeChild(u),delete l.actions[i.id]}}),e({},this.actions)},s}();return Se.ActionInterface=o,Se}var Be={},en;function Tl(){if(en)return Be;en=1,Object.defineProperty(Be,"__esModule",{value:!0}),Be.history=Be.HistoryItemImpl=void 0;var e=qe(),t=function(){function o(s){this.perform=s.perform,this.negate=s.negate}return o.create=function(s){return new o(s)},o}();Be.HistoryItemImpl=t;var r=function(){function o(){return this.undoStack=[],this.redoStack=[],o.instance||(o.instance=this,this.init()),o.instance}return o.prototype.init=function(){var s=this;typeof window>"u"||window.addEventListener("keydown",function(a){var l;if(!(!s.redoStack.length&&!s.undoStack.length||(0,e.shouldRejectKeystrokes)())){var i=(l=a.key)===null||l===void 0?void 0:l.toLowerCase();a.metaKey&&i==="z"&&a.shiftKey?s.redo():a.metaKey&&i==="z"&&s.undo()}})},o.prototype.add=function(s){var a=t.create(s);return this.undoStack.push(a),a},o.prototype.remove=function(s){var a=this.undoStack.findIndex(function(i){return i===s});if(a!==-1){this.undoStack.splice(a,1);return}var l=this.redoStack.findIndex(function(i){return i===s});l!==-1&&this.redoStack.splice(l,1)},o.prototype.undo=function(s){if(!s){var a=this.undoStack.pop();return a?(a?.negate(),this.redoStack.push(a),a):void 0}var l=this.undoStack.findIndex(function(i){return i===s});if(l!==-1)return this.undoStack.splice(l,1),s.negate(),this.redoStack.push(s),s},o.prototype.redo=function(s){if(!s){var a=this.redoStack.pop();return a?(a?.perform(),this.undoStack.push(a),a):void 0}var l=this.redoStack.findIndex(function(i){return i===s});if(l!==-1)return this.redoStack.splice(l,1),s.perform(),this.undoStack.push(s),s},o.prototype.reset=function(){this.undoStack.splice(0),this.redoStack.splice(0)},o}(),n=new r;return Be.history=n,Object.freeze(n),Be}var qt={},tn;function Qe(){return tn||(tn=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.VisualState=void 0,function(t){t.animatingIn="animating-in",t.showing="showing",t.animatingOut="animating-out",t.hidden="hidden"}(e.VisualState||(e.VisualState={}))}(qt)),qt}var rn;function kl(){if(rn)return se;rn=1;var e=se&&se.__assign||function(){return e=Object.assign||function(p){for(var b,v=1,w=arguments.length;v<w;v++){b=arguments[v];for(var y in b)Object.prototype.hasOwnProperty.call(b,y)&&(p[y]=b[y])}return p},e.apply(this,arguments)},t=se&&se.__createBinding||(Object.create?function(p,b,v,w){w===void 0&&(w=v),Object.defineProperty(p,w,{enumerable:!0,get:function(){return b[v]}})}:function(p,b,v,w){w===void 0&&(w=v),p[w]=b[v]}),r=se&&se.__setModuleDefault||(Object.create?function(p,b){Object.defineProperty(p,"default",{enumerable:!0,value:b})}:function(p,b){p.default=b}),n=se&&se.__importStar||function(p){if(p&&p.__esModule)return p;var b={};if(p!=null)for(var v in p)v!=="default"&&Object.prototype.hasOwnProperty.call(p,v)&&t(b,p,v);return r(b,p),b},o=se&&se.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(se,"__esModule",{value:!0}),se.useStore=void 0;var s=Ml(),a=n(re()),l=o(Er()),i=Mo(),u=Tl(),f=Qe();function d(p){var b=a.useRef(e({animations:{enterMs:200,exitMs:100}},p.options)),v=a.useMemo(function(){return new i.ActionInterface(p.actions||[],{historyManager:b.current.enableHistory?u.history:void 0})},[]),w=a.useState({searchQuery:"",currentRootActionId:null,visualState:f.VisualState.hidden,actions:e({},v.actions),activeIndex:0,disabled:!1}),y=w[0],h=w[1],j=a.useRef(y);j.current=y;var C=a.useCallback(function(){return j.current},[]),P=a.useMemo(function(){return new g(C)},[C]);a.useEffect(function(){j.current=y,P.notify()},[y,P]);var O=a.useCallback(function(M){return h(function(x){return e(e({},x),{actions:v.add(M)})}),function(){h(function(_){return e(e({},_),{actions:v.remove(M)})})}},[v]),E=a.useRef(null);return a.useMemo(function(){var M={setCurrentRootAction:function(x){h(function(_){return e(e({},_),{currentRootActionId:x})})},setVisualState:function(x){h(function(_){return e(e({},_),{visualState:typeof x=="function"?x(_.visualState):x})})},setSearch:function(x){return h(function(_){return e(e({},_),{searchQuery:x})})},registerActions:O,toggle:function(){return h(function(x){return e(e({},x),{visualState:[f.VisualState.animatingOut,f.VisualState.hidden].includes(x.visualState)?f.VisualState.animatingIn:f.VisualState.animatingOut})})},setActiveIndex:function(x){return h(function(_){return e(e({},_),{activeIndex:typeof x=="number"?x:x(_.activeIndex)})})},inputRefSetter:function(x){E.current=x},getInput:function(){return(0,l.default)(E.current,"Input ref is undefined, make sure you attach `query.inputRefSetter` to your search input."),E.current},disable:function(x){h(function(_){return e(e({},_),{disabled:x})})}};return{getState:C,query:M,options:b.current,subscribe:function(x,_){return P.subscribe(x,_)}}},[C,P,O])}se.useStore=d;var g=function(){function p(b){this.subscribers=[],this.getState=b}return p.prototype.subscribe=function(b,v){var w=this,y=new m(function(){return b(w.getState())},v);return this.subscribers.push(y),this.unsubscribe.bind(this,y)},p.prototype.unsubscribe=function(b){if(this.subscribers.length){var v=this.subscribers.indexOf(b);if(v>-1)return this.subscribers.splice(v,1)}},p.prototype.notify=function(){this.subscribers.forEach(function(b){return b.collect()})},p}(),m=function(){function p(b,v){this.collector=b,this.onChange=v}return p.prototype.collect=function(){try{var b=this.collector();(0,s.deepEqual)(b,this.collected)||(this.collected=b,this.onChange&&this.onChange(this.collected))}catch{}},p}();return se}var le={},gt={},nn;function Rl(){if(nn)return gt;nn=1,Object.defineProperty(gt,"__esModule",{value:!0});var e=["Shift","Meta","Alt","Control"],t=1e3,r="keydown",n=typeof navigator=="object"&&/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"Meta":"Control";function o(i,u){return typeof i.getModifierState=="function"?i.getModifierState(u):!1}function s(i){return i.trim().split(" ").map(function(u){var f=u.split(/\b\+/),d=f.pop();return f=f.map(function(g){return g==="$mod"?n:g}),[f,d]})}function a(i,u){return/^[^A-Za-z0-9]$/.test(i.key)&&u[1]===i.key?!0:!(u[1].toUpperCase()!==i.key.toUpperCase()&&u[1]!==i.code||u[0].find(function(f){return!o(i,f)})||e.find(function(f){return!u[0].includes(f)&&u[1]!==f&&o(i,f)}))}function l(i,u,f){var d,g;f===void 0&&(f={});var m=(d=f.timeout)!==null&&d!==void 0?d:t,p=(g=f.event)!==null&&g!==void 0?g:r,b=Object.keys(u).map(function(h){return[s(h),u[h]]}),v=new Map,w=null,y=function(h){h instanceof KeyboardEvent&&(b.forEach(function(j){var C=j[0],P=j[1],O=v.get(C),E=O||C,M=E[0],x=a(h,M);x?E.length>1?v.set(C,E.slice(1)):(v.delete(C),P(h)):o(h,h.key)||v.delete(C)}),w&&clearTimeout(w),w=setTimeout(v.clear.bind(v),m))};return i.addEventListener(p,y),function(){i.removeEventListener(p,y)}}return gt.default=l,gt}var on;function Il(){if(on)return le;on=1;var e=le&&le.__createBinding||(Object.create?function(v,w,y,h){h===void 0&&(h=y),Object.defineProperty(v,h,{enumerable:!0,get:function(){return w[y]}})}:function(v,w,y,h){h===void 0&&(h=y),v[h]=w[y]}),t=le&&le.__setModuleDefault||(Object.create?function(v,w){Object.defineProperty(v,"default",{enumerable:!0,value:w})}:function(v,w){v.default=w}),r=le&&le.__importStar||function(v){if(v&&v.__esModule)return v;var w={};if(v!=null)for(var y in v)y!=="default"&&Object.prototype.hasOwnProperty.call(v,y)&&e(w,v,y);return t(w,v),w},n=le&&le.__importDefault||function(v){return v&&v.__esModule?v:{default:v}};Object.defineProperty(le,"__esModule",{value:!0}),le.InternalEvents=void 0;var o=r(re()),s=n(Rl()),a=Qe(),l=ke(),i=qe();function u(){return f(),d(),p(),b(),null}le.InternalEvents=u;function f(){var v,w,y=(0,l.useKBar)(function(x){return{visualState:x.visualState,showing:x.visualState!==a.VisualState.hidden,disabled:x.disabled}}),h=y.query,j=y.options,C=y.visualState,P=y.showing,O=y.disabled;o.useEffect(function(){var x,_=function(){h.setVisualState(function(S){return S===a.VisualState.hidden||S===a.VisualState.animatingOut?S:a.VisualState.animatingOut})};if(O){_();return}var I=j.toggleShortcut||"$mod+k",k=(0,s.default)(window,(x={},x[I]=function(S){var N,A,D,R;S.defaultPrevented||(S.preventDefault(),h.toggle(),P?(A=(N=j.callbacks)===null||N===void 0?void 0:N.onClose)===null||A===void 0||A.call(N):(R=(D=j.callbacks)===null||D===void 0?void 0:D.onOpen)===null||R===void 0||R.call(D))},x.Escape=function(S){var N,A;P&&(S.stopPropagation(),S.preventDefault(),(A=(N=j.callbacks)===null||N===void 0?void 0:N.onClose)===null||A===void 0||A.call(N)),_()},x));return function(){k()}},[j.callbacks,j.toggleShortcut,h,P,O]);var E=o.useRef(),M=o.useCallback(function(x){var _,I,k=0;x===a.VisualState.animatingIn&&(k=((_=j.animations)===null||_===void 0?void 0:_.enterMs)||0),x===a.VisualState.animatingOut&&(k=((I=j.animations)===null||I===void 0?void 0:I.exitMs)||0),clearTimeout(E.current),E.current=setTimeout(function(){var S=!1;h.setVisualState(function(){var N=x===a.VisualState.animatingIn?a.VisualState.showing:a.VisualState.hidden;return N===a.VisualState.hidden&&(S=!0),N}),S&&h.setCurrentRootAction(null)},k)},[(v=j.animations)===null||v===void 0?void 0:v.enterMs,(w=j.animations)===null||w===void 0?void 0:w.exitMs,h]);o.useEffect(function(){switch(C){case a.VisualState.animatingIn:case a.VisualState.animatingOut:M(C);break}},[M,C])}function d(){var v=(0,l.useKBar)(function(h){return{visualState:h.visualState}}),w=v.visualState,y=v.options;o.useEffect(function(){if(!y.disableDocumentLock)if(w===a.VisualState.animatingIn){if(document.body.style.overflow="hidden",!y.disableScrollbarManagement){var h=(0,i.getScrollbarWidth)(),j=getComputedStyle(document.body)["margin-right"];j&&(h+=Number(j.replace(/\D/g,""))),document.body.style.marginRight=h+"px"}}else w===a.VisualState.hidden&&(document.body.style.removeProperty("overflow"),y.disableScrollbarManagement||document.body.style.removeProperty("margin-right"))},[y.disableDocumentLock,y.disableScrollbarManagement,w])}var g=new WeakSet;function m(v){return function(w){g.has(w)||(v(w),g.add(w))}}function p(){var v=(0,l.useKBar)(function(P){return{actions:P.actions,open:P.visualState===a.VisualState.showing,disabled:P.disabled}}),w=v.actions,y=v.query,h=v.open,j=v.options,C=v.disabled;o.useEffect(function(){var P;if(!(h||C)){for(var O=Object.keys(w).map(function(D){return w[D]}),E=[],M=0,x=O;M<x.length;M++){var _=x[M];!((P=_.shortcut)===null||P===void 0)&&P.length&&E.push(_)}E=E.sort(function(D,R){return R.shortcut.join(" ").length-D.shortcut.join(" ").length});for(var I={},k=function(D){var R=D.shortcut.join(" ");I[R]=m(function(V){var W,Q,Z,q,U,ae;(0,i.shouldRejectKeystrokes)()||(V.preventDefault(),!((W=D.children)===null||W===void 0)&&W.length?(y.setCurrentRootAction(D.id),y.toggle(),(Z=(Q=j.callbacks)===null||Q===void 0?void 0:Q.onOpen)===null||Z===void 0||Z.call(Q)):((q=D.command)===null||q===void 0||q.perform(),(ae=(U=j.callbacks)===null||U===void 0?void 0:U.onSelectAction)===null||ae===void 0||ae.call(U,D)))})},S=0,N=E;S<N.length;S++){var _=N[S];k(_)}var A=(0,s.default)(window,I,{timeout:400});return function(){A()}}},[w,h,j.callbacks,y,C])}function b(){var v=o.useRef(!0),w=(0,l.useKBar)(function(C){return{isShowing:C.visualState===a.VisualState.showing||C.visualState===a.VisualState.animatingIn}}),y=w.isShowing,h=w.query,j=o.useRef(null);o.useEffect(function(){if(v.current){v.current=!1;return}if(y){j.current=document.activeElement;return}var C=document.activeElement;C?.tagName.toLowerCase()==="input"&&C.blur();var P=j.current;P&&P!==C&&P.focus()},[y]),o.useEffect(function(){function C(P){var O=h.getInput();P.target!==O&&O.focus()}if(y)return window.addEventListener("keydown",C),function(){window.removeEventListener("keydown",C)}},[y,h])}return le}var an;function Po(){return an||(an=1,function(e){var t=je&&je.__createBinding||(Object.create?function(i,u,f,d){d===void 0&&(d=f),Object.defineProperty(i,d,{enumerable:!0,get:function(){return u[f]}})}:function(i,u,f,d){d===void 0&&(d=f),i[d]=u[f]}),r=je&&je.__setModuleDefault||(Object.create?function(i,u){Object.defineProperty(i,"default",{enumerable:!0,value:u})}:function(i,u){i.default=u}),n=je&&je.__importStar||function(i){if(i&&i.__esModule)return i;var u={};if(i!=null)for(var f in i)f!=="default"&&Object.prototype.hasOwnProperty.call(i,f)&&t(u,i,f);return r(u,i),u};Object.defineProperty(e,"__esModule",{value:!0}),e.KBarProvider=e.KBarContext=void 0;var o=kl(),s=n(re()),a=Il();e.KBarContext=s.createContext({});var l=function(i){var u=(0,o.useStore)(i);return s.createElement(e.KBarContext.Provider,{value:u},s.createElement(a.InternalEvents,null),i.children)};e.KBarProvider=l}(je)),je}var sn;function ke(){if(sn)return ce;sn=1;var e=ce&&ce.__assign||function(){return e=Object.assign||function(l){for(var i,u=1,f=arguments.length;u<f;u++){i=arguments[u];for(var d in i)Object.prototype.hasOwnProperty.call(i,d)&&(l[d]=i[d])}return l},e.apply(this,arguments)},t=ce&&ce.__createBinding||(Object.create?function(l,i,u,f){f===void 0&&(f=u),Object.defineProperty(l,f,{enumerable:!0,get:function(){return i[u]}})}:function(l,i,u,f){f===void 0&&(f=u),l[f]=i[u]}),r=ce&&ce.__setModuleDefault||(Object.create?function(l,i){Object.defineProperty(l,"default",{enumerable:!0,value:i})}:function(l,i){l.default=i}),n=ce&&ce.__importStar||function(l){if(l&&l.__esModule)return l;var i={};if(l!=null)for(var u in l)u!=="default"&&Object.prototype.hasOwnProperty.call(l,u)&&t(i,l,u);return r(i,l),i};Object.defineProperty(ce,"__esModule",{value:!0}),ce.useKBar=void 0;var o=n(re()),s=Po();function a(l){var i=o.useContext(s.KBarContext),u=i.query,f=i.getState,d=i.subscribe,g=i.options,m=o.useRef(l?.(f())),p=o.useRef(l),b=o.useCallback(function(h){return e(e({},h),{query:u,options:g})},[u,g]),v=o.useState(b(m.current)),w=v[0],y=v[1];return o.useEffect(function(){var h;return p.current&&(h=d(function(j){return p.current(j)},function(j){return y(b(j))})),function(){h&&h()}},[b,d]),w}return ce.useKBar=a,ce}function Ee(e){return Array.isArray?Array.isArray(e):Ro(e)==="[object Array]"}function Al(e){if(typeof e=="string")return e;let t=e+"";return t=="0"&&1/e==-1/0?"-0":t}function Nl(e){return e==null?"":Al(e)}function xe(e){return typeof e=="string"}function To(e){return typeof e=="number"}function Dl(e){return e===!0||e===!1||Ll(e)&&Ro(e)=="[object Boolean]"}function ko(e){return typeof e=="object"}function Ll(e){return ko(e)&&e!==null}function pe(e){return e!=null}function Ut(e){return!e.trim().length}function Ro(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}const zl="Incorrect 'index' type",Bl=e=>`Invalid value for key ${e}`,$l=e=>`Pattern length exceeds max of ${e}.`,Kl=e=>`Missing ${e} property in key`,Fl=e=>`Property 'weight' in key '${e}' must be a positive integer`,cn=Object.prototype.hasOwnProperty;class Vl{constructor(t){this._keys=[],this._keyMap={};let r=0;t.forEach(n=>{let o=Io(n);r+=o.weight,this._keys.push(o),this._keyMap[o.id]=o,r+=o.weight}),this._keys.forEach(n=>{n.weight/=r})}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function Io(e){let t=null,r=null,n=null,o=1,s=null;if(xe(e)||Ee(e))n=e,t=ln(e),r=ur(e);else{if(!cn.call(e,"name"))throw new Error(Kl("name"));const a=e.name;if(n=a,cn.call(e,"weight")&&(o=e.weight,o<=0))throw new Error(Fl(a));t=ln(a),r=ur(a),s=e.getFn}return{path:t,id:r,weight:o,src:n,getFn:s}}function ln(e){return Ee(e)?e:e.split(".")}function ur(e){return Ee(e)?e.join("."):e}function ql(e,t){let r=[],n=!1;const o=(s,a,l)=>{if(pe(s))if(!a[l])r.push(s);else{let i=a[l];const u=s[i];if(!pe(u))return;if(l===a.length-1&&(xe(u)||To(u)||Dl(u)))r.push(Nl(u));else if(Ee(u)){n=!0;for(let f=0,d=u.length;f<d;f+=1)o(u[f],a,l+1)}else a.length&&o(u,a,l+1)}};return o(e,xe(t)?t.split("."):t,0),n?r:r[0]}const Ul={includeMatches:!1,findAllMatches:!1,minMatchCharLength:1},Wl={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(e,t)=>e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1},Hl={location:0,threshold:.6,distance:100},Gl={useExtendedSearch:!1,getFn:ql,ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};var $={...Wl,...Ul,...Hl,...Gl};const Yl=/[^ ]+/g;function Xl(e=1,t=3){const r=new Map,n=Math.pow(10,t);return{get(o){const s=o.match(Yl).length;if(r.has(s))return r.get(s);const a=1/Math.pow(s,.5*e),l=parseFloat(Math.round(a*n)/n);return r.set(s,l),l},clear(){r.clear()}}}class Or{constructor({getFn:t=$.getFn,fieldNormWeight:r=$.fieldNormWeight}={}){this.norm=Xl(r,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach((r,n)=>{this._keysMap[r.id]=n})}create(){this.isCreated||!this.docs.length||(this.isCreated=!0,xe(this.docs[0])?this.docs.forEach((t,r)=>{this._addString(t,r)}):this.docs.forEach((t,r)=>{this._addObject(t,r)}),this.norm.clear())}add(t){const r=this.size();xe(t)?this._addString(t,r):this._addObject(t,r)}removeAt(t){this.records.splice(t,1);for(let r=t,n=this.size();r<n;r+=1)this.records[r].i-=1}getValueForItemAtKeyId(t,r){return t[this._keysMap[r]]}size(){return this.records.length}_addString(t,r){if(!pe(t)||Ut(t))return;let n={v:t,i:r,n:this.norm.get(t)};this.records.push(n)}_addObject(t,r){let n={i:r,$:{}};this.keys.forEach((o,s)=>{let a=o.getFn?o.getFn(t):this.getFn(t,o.path);if(pe(a)){if(Ee(a)){let l=[];const i=[{nestedArrIndex:-1,value:a}];for(;i.length;){const{nestedArrIndex:u,value:f}=i.pop();if(pe(f))if(xe(f)&&!Ut(f)){let d={v:f,i:u,n:this.norm.get(f)};l.push(d)}else Ee(f)&&f.forEach((d,g)=>{i.push({nestedArrIndex:g,value:d})})}n.$[s]=l}else if(xe(a)&&!Ut(a)){let l={v:a,n:this.norm.get(a)};n.$[s]=l}}}),this.records.push(n)}toJSON(){return{keys:this.keys,records:this.records}}}function Ao(e,t,{getFn:r=$.getFn,fieldNormWeight:n=$.fieldNormWeight}={}){const o=new Or({getFn:r,fieldNormWeight:n});return o.setKeys(e.map(Io)),o.setSources(t),o.create(),o}function Ql(e,{getFn:t=$.getFn,fieldNormWeight:r=$.fieldNormWeight}={}){const{keys:n,records:o}=e,s=new Or({getFn:t,fieldNormWeight:r});return s.setKeys(n),s.setIndexRecords(o),s}function bt(e,{errors:t=0,currentLocation:r=0,expectedLocation:n=0,distance:o=$.distance,ignoreLocation:s=$.ignoreLocation}={}){const a=t/e.length;if(s)return a;const l=Math.abs(n-r);return o?a+l/o:l?1:a}function Jl(e=[],t=$.minMatchCharLength){let r=[],n=-1,o=-1,s=0;for(let a=e.length;s<a;s+=1){let l=e[s];l&&n===-1?n=s:!l&&n!==-1&&(o=s-1,o-n+1>=t&&r.push([n,o]),n=-1)}return e[s-1]&&s-n>=t&&r.push([n,s-1]),r}const Ke=32;function Zl(e,t,r,{location:n=$.location,distance:o=$.distance,threshold:s=$.threshold,findAllMatches:a=$.findAllMatches,minMatchCharLength:l=$.minMatchCharLength,includeMatches:i=$.includeMatches,ignoreLocation:u=$.ignoreLocation}={}){if(t.length>Ke)throw new Error($l(Ke));const f=t.length,d=e.length,g=Math.max(0,Math.min(n,d));let m=s,p=g;const b=l>1||i,v=b?Array(d):[];let w;for(;(w=e.indexOf(t,p))>-1;){let O=bt(t,{currentLocation:w,expectedLocation:g,distance:o,ignoreLocation:u});if(m=Math.min(O,m),p=w+f,b){let E=0;for(;E<f;)v[w+E]=1,E+=1}}p=-1;let y=[],h=1,j=f+d;const C=1<<f-1;for(let O=0;O<f;O+=1){let E=0,M=j;for(;E<M;)bt(t,{errors:O,currentLocation:g+M,expectedLocation:g,distance:o,ignoreLocation:u})<=m?E=M:j=M,M=Math.floor((j-E)/2+E);j=M;let x=Math.max(1,g-M+1),_=a?d:Math.min(g+M,d)+f,I=Array(_+2);I[_+1]=(1<<O)-1;for(let S=_;S>=x;S-=1){let N=S-1,A=r[e.charAt(N)];if(b&&(v[N]=+!!A),I[S]=(I[S+1]<<1|1)&A,O&&(I[S]|=(y[S+1]|y[S])<<1|1|y[S+1]),I[S]&C&&(h=bt(t,{errors:O,currentLocation:N,expectedLocation:g,distance:o,ignoreLocation:u}),h<=m)){if(m=h,p=N,p<=g)break;x=Math.max(1,2*g-p)}}if(bt(t,{errors:O+1,currentLocation:g,expectedLocation:g,distance:o,ignoreLocation:u})>m)break;y=I}const P={isMatch:p>=0,score:Math.max(.001,h)};if(b){const O=Jl(v,l);O.length?i&&(P.indices=O):P.isMatch=!1}return P}function eu(e){let t={};for(let r=0,n=e.length;r<n;r+=1){const o=e.charAt(r);t[o]=(t[o]||0)|1<<n-r-1}return t}class No{constructor(t,{location:r=$.location,threshold:n=$.threshold,distance:o=$.distance,includeMatches:s=$.includeMatches,findAllMatches:a=$.findAllMatches,minMatchCharLength:l=$.minMatchCharLength,isCaseSensitive:i=$.isCaseSensitive,ignoreLocation:u=$.ignoreLocation}={}){if(this.options={location:r,threshold:n,distance:o,includeMatches:s,findAllMatches:a,minMatchCharLength:l,isCaseSensitive:i,ignoreLocation:u},this.pattern=i?t:t.toLowerCase(),this.chunks=[],!this.pattern.length)return;const f=(g,m)=>{this.chunks.push({pattern:g,alphabet:eu(g),startIndex:m})},d=this.pattern.length;if(d>Ke){let g=0;const m=d%Ke,p=d-m;for(;g<p;)f(this.pattern.substr(g,Ke),g),g+=Ke;if(m){const b=d-Ke;f(this.pattern.substr(b),b)}}else f(this.pattern,0)}searchIn(t){const{isCaseSensitive:r,includeMatches:n}=this.options;if(r||(t=t.toLowerCase()),this.pattern===t){let p={isMatch:!0,score:0};return n&&(p.indices=[[0,t.length-1]]),p}const{location:o,distance:s,threshold:a,findAllMatches:l,minMatchCharLength:i,ignoreLocation:u}=this.options;let f=[],d=0,g=!1;this.chunks.forEach(({pattern:p,alphabet:b,startIndex:v})=>{const{isMatch:w,score:y,indices:h}=Zl(t,p,b,{location:o+v,distance:s,threshold:a,findAllMatches:l,minMatchCharLength:i,includeMatches:n,ignoreLocation:u});w&&(g=!0),d+=y,w&&h&&(f=[...f,...h])});let m={isMatch:g,score:g?d/this.chunks.length:1};return g&&n&&(m.indices=f),m}}class Re{constructor(t){this.pattern=t}static isMultiMatch(t){return un(t,this.multiRegex)}static isSingleMatch(t){return un(t,this.singleRegex)}search(){}}function un(e,t){const r=e.match(t);return r?r[1]:null}class tu extends Re{constructor(t){super(t)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(t){const r=t===this.pattern;return{isMatch:r,score:r?0:1,indices:[0,this.pattern.length-1]}}}class ru extends Re{constructor(t){super(t)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(t){const n=t.indexOf(this.pattern)===-1;return{isMatch:n,score:n?0:1,indices:[0,t.length-1]}}}class nu extends Re{constructor(t){super(t)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(t){const r=t.startsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[0,this.pattern.length-1]}}}class ou extends Re{constructor(t){super(t)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(t){const r=!t.startsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[0,t.length-1]}}}class au extends Re{constructor(t){super(t)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(t){const r=t.endsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[t.length-this.pattern.length,t.length-1]}}}class su extends Re{constructor(t){super(t)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(t){const r=!t.endsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[0,t.length-1]}}}class Do extends Re{constructor(t,{location:r=$.location,threshold:n=$.threshold,distance:o=$.distance,includeMatches:s=$.includeMatches,findAllMatches:a=$.findAllMatches,minMatchCharLength:l=$.minMatchCharLength,isCaseSensitive:i=$.isCaseSensitive,ignoreLocation:u=$.ignoreLocation}={}){super(t),this._bitapSearch=new No(t,{location:r,threshold:n,distance:o,includeMatches:s,findAllMatches:a,minMatchCharLength:l,isCaseSensitive:i,ignoreLocation:u})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class Lo extends Re{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let r=0,n;const o=[],s=this.pattern.length;for(;(n=t.indexOf(this.pattern,r))>-1;)r=n+s,o.push([n,r-1]);const a=!!o.length;return{isMatch:a,score:a?0:1,indices:o}}}const dr=[tu,Lo,nu,ou,su,au,ru,Do],dn=dr.length,iu=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,cu="|";function lu(e,t={}){return e.split(cu).map(r=>{let n=r.trim().split(iu).filter(s=>s&&!!s.trim()),o=[];for(let s=0,a=n.length;s<a;s+=1){const l=n[s];let i=!1,u=-1;for(;!i&&++u<dn;){const f=dr[u];let d=f.isMultiMatch(l);d&&(o.push(new f(d,t)),i=!0)}if(!i)for(u=-1;++u<dn;){const f=dr[u];let d=f.isSingleMatch(l);if(d){o.push(new f(d,t));break}}}return o})}const uu=new Set([Do.type,Lo.type]);class du{constructor(t,{isCaseSensitive:r=$.isCaseSensitive,includeMatches:n=$.includeMatches,minMatchCharLength:o=$.minMatchCharLength,ignoreLocation:s=$.ignoreLocation,findAllMatches:a=$.findAllMatches,location:l=$.location,threshold:i=$.threshold,distance:u=$.distance}={}){this.query=null,this.options={isCaseSensitive:r,includeMatches:n,minMatchCharLength:o,findAllMatches:a,ignoreLocation:s,location:l,threshold:i,distance:u},this.pattern=r?t:t.toLowerCase(),this.query=lu(this.pattern,this.options)}static condition(t,r){return r.useExtendedSearch}searchIn(t){const r=this.query;if(!r)return{isMatch:!1,score:1};const{includeMatches:n,isCaseSensitive:o}=this.options;t=o?t:t.toLowerCase();let s=0,a=[],l=0;for(let i=0,u=r.length;i<u;i+=1){const f=r[i];a.length=0,s=0;for(let d=0,g=f.length;d<g;d+=1){const m=f[d],{isMatch:p,indices:b,score:v}=m.search(t);if(p){if(s+=1,l+=v,n){const w=m.constructor.type;uu.has(w)?a=[...a,...b]:a.push(b)}}else{l=0,s=0,a.length=0;break}}if(s){let d={isMatch:!0,score:l/s};return n&&(d.indices=a),d}}return{isMatch:!1,score:1}}}const fr=[];function fu(...e){fr.push(...e)}function pr(e,t){for(let r=0,n=fr.length;r<n;r+=1){let o=fr[r];if(o.condition(e,t))return new o(e,t)}return new No(e,t)}const Ot={AND:"$and",OR:"$or"},hr={PATH:"$path",PATTERN:"$val"},vr=e=>!!(e[Ot.AND]||e[Ot.OR]),pu=e=>!!e[hr.PATH],hu=e=>!Ee(e)&&ko(e)&&!vr(e),fn=e=>({[Ot.AND]:Object.keys(e).map(t=>({[t]:e[t]}))});function zo(e,t,{auto:r=!0}={}){const n=o=>{let s=Object.keys(o);const a=pu(o);if(!a&&s.length>1&&!vr(o))return n(fn(o));if(hu(o)){const i=a?o[hr.PATH]:s[0],u=a?o[hr.PATTERN]:o[i];if(!xe(u))throw new Error(Bl(i));const f={keyId:ur(i),pattern:u};return r&&(f.searcher=pr(u,t)),f}let l={children:[],operator:s[0]};return s.forEach(i=>{const u=o[i];Ee(u)&&u.forEach(f=>{l.children.push(n(f))})}),l};return vr(e)||(e=fn(e)),n(e)}function vu(e,{ignoreFieldNorm:t=$.ignoreFieldNorm}){e.forEach(r=>{let n=1;r.matches.forEach(({key:o,norm:s,score:a})=>{const l=o?o.weight:null;n*=Math.pow(a===0&&l?Number.EPSILON:a,(l||1)*(t?1:s))}),r.score=n})}function mu(e,t){const r=e.matches;t.matches=[],pe(r)&&r.forEach(n=>{if(!pe(n.indices)||!n.indices.length)return;const{indices:o,value:s}=n;let a={indices:o,value:s};n.key&&(a.key=n.key.src),n.idx>-1&&(a.refIndex=n.idx),t.matches.push(a)})}function gu(e,t){t.score=e.score}function bu(e,t,{includeMatches:r=$.includeMatches,includeScore:n=$.includeScore}={}){const o=[];return r&&o.push(mu),n&&o.push(gu),e.map(s=>{const{idx:a}=s,l={item:t[a],refIndex:a};return o.length&&o.forEach(i=>{i(s,l)}),l})}class Je{constructor(t,r={},n){this.options={...$,...r},this.options.useExtendedSearch,this._keyStore=new Vl(this.options.keys),this.setCollection(t,n)}setCollection(t,r){if(this._docs=t,r&&!(r instanceof Or))throw new Error(zl);this._myIndex=r||Ao(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){pe(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=()=>!1){const r=[];for(let n=0,o=this._docs.length;n<o;n+=1){const s=this._docs[n];t(s,n)&&(this.removeAt(n),n-=1,o-=1,r.push(s))}return r}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:r=-1}={}){const{includeMatches:n,includeScore:o,shouldSort:s,sortFn:a,ignoreFieldNorm:l}=this.options;let i=xe(t)?xe(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return vu(i,{ignoreFieldNorm:l}),s&&i.sort(a),To(r)&&r>-1&&(i=i.slice(0,r)),bu(i,this._docs,{includeMatches:n,includeScore:o})}_searchStringList(t){const r=pr(t,this.options),{records:n}=this._myIndex,o=[];return n.forEach(({v:s,i:a,n:l})=>{if(!pe(s))return;const{isMatch:i,score:u,indices:f}=r.searchIn(s);i&&o.push({item:s,idx:a,matches:[{score:u,value:s,norm:l,indices:f}]})}),o}_searchLogical(t){const r=zo(t,this.options),n=(l,i,u)=>{if(!l.children){const{keyId:d,searcher:g}=l,m=this._findMatches({key:this._keyStore.get(d),value:this._myIndex.getValueForItemAtKeyId(i,d),searcher:g});return m&&m.length?[{idx:u,item:i,matches:m}]:[]}const f=[];for(let d=0,g=l.children.length;d<g;d+=1){const m=l.children[d],p=n(m,i,u);if(p.length)f.push(...p);else if(l.operator===Ot.AND)return[]}return f},o=this._myIndex.records,s={},a=[];return o.forEach(({$:l,i})=>{if(pe(l)){let u=n(r,l,i);u.length&&(s[i]||(s[i]={idx:i,item:l,matches:[]},a.push(s[i])),u.forEach(({matches:f})=>{s[i].matches.push(...f)}))}}),a}_searchObjectList(t){const r=pr(t,this.options),{keys:n,records:o}=this._myIndex,s=[];return o.forEach(({$:a,i:l})=>{if(!pe(a))return;let i=[];n.forEach((u,f)=>{i.push(...this._findMatches({key:u,value:a[f],searcher:r}))}),i.length&&s.push({idx:l,item:a,matches:i})}),s}_findMatches({key:t,value:r,searcher:n}){if(!pe(r))return[];let o=[];if(Ee(r))r.forEach(({v:s,i:a,n:l})=>{if(!pe(s))return;const{isMatch:i,score:u,indices:f}=n.searchIn(s);i&&o.push({score:u,key:t,value:s,idx:a,norm:l,indices:f})});else{const{v:s,n:a}=r,{isMatch:l,score:i,indices:u}=n.searchIn(s);l&&o.push({score:i,key:t,value:s,norm:a,indices:u})}return o}}Je.version="6.6.2";Je.createIndex=Ao;Je.parseIndex=Ql;Je.config=$;Je.parseQuery=zo;fu(du);const yu=Object.freeze(Object.defineProperty({__proto__:null,default:Je},Symbol.toStringTag,{value:"Module"})),xu=Mn(yu);var pn;function wu(){return pn||(pn=1,function(e){var t=ve&&ve.__createBinding||(Object.create?function(m,p,b,v){v===void 0&&(v=b),Object.defineProperty(m,v,{enumerable:!0,get:function(){return p[b]}})}:function(m,p,b,v){v===void 0&&(v=b),m[v]=p[b]}),r=ve&&ve.__setModuleDefault||(Object.create?function(m,p){Object.defineProperty(m,"default",{enumerable:!0,value:p})}:function(m,p){m.default=p}),n=ve&&ve.__importStar||function(m){if(m&&m.__esModule)return m;var p={};if(m!=null)for(var b in m)b!=="default"&&Object.prototype.hasOwnProperty.call(m,b)&&t(p,m,b);return r(p,m),p},o=ve&&ve.__importDefault||function(m){return m&&m.__esModule?m:{default:m}};Object.defineProperty(e,"__esModule",{value:!0}),e.useDeepMatches=e.useMatches=e.NO_GROUP=void 0;var s=n(re()),a=ke(),l=qe(),i=o(xu);e.NO_GROUP={name:"none",priority:l.Priority.NORMAL};var u={keys:[{name:"name",weight:.5},{name:"keywords",getFn:function(m){var p;return((p=m.keywords)!==null&&p!==void 0?p:"").split(",")},weight:.5},"subtitle"],ignoreLocation:!0,includeScore:!0,includeMatches:!0,threshold:.2,minMatchCharLength:1};function f(m,p){return p.priority-m.priority}function d(){var m=(0,a.useKBar)(function(M){return{search:M.searchQuery,actions:M.actions,rootActionId:M.currentRootActionId}}),p=m.search,b=m.actions,v=m.rootActionId,w=s.useMemo(function(){return Object.keys(b).reduce(function(M,x){var _=b[x];if(!_.parent&&!v&&M.push(_),_.id===v)for(var I=0;I<_.children.length;I++)M.push(_.children[I]);return M},[]).sort(f)},[b,v]),y=s.useCallback(function(M){for(var x=[],_=0;_<M.length;_++)x.push(M[_]);return function I(k,S){S===void 0&&(S=x);for(var N=0;N<k.length;N++)if(k[N].children.length>0){for(var A=k[N].children,D=0;D<A.length;D++)S.push(A[D]);I(k[N].children,S)}return S}(M)},[]),h=!p,j=s.useMemo(function(){return h?w:y(w)},[y,w,h]),C=s.useMemo(function(){return new i.default(j,u)},[j]),P=g(j,p,C),O=s.useMemo(function(){for(var M,x,_={},I=[],k=[],S=0;S<P.length;S++){var N=P[S],A=N.action,D=N.score||l.Priority.NORMAL,R={name:typeof A.section=="string"?A.section:((M=A.section)===null||M===void 0?void 0:M.name)||e.NO_GROUP.name,priority:typeof A.section=="string"?D:((x=A.section)===null||x===void 0?void 0:x.priority)||0+D};_[R.name]||(_[R.name]=[],I.push(R)),_[R.name].push({priority:A.priority+D,action:A})}k=I.sort(f).map(function(Z){return{name:Z.name,actions:_[Z.name].sort(f).map(function(q){return q.action})}});for(var V=[],S=0;S<k.length;S++){var W=k[S];W.name!==e.NO_GROUP.name&&V.push(W.name);for(var Q=0;Q<W.actions.length;Q++)V.push(W.actions[Q])}return V},[P]),E=s.useMemo(function(){return v},[O]);return s.useMemo(function(){return{results:O,rootActionId:E}},[E,O])}e.useMatches=d;function g(m,p,b){var v=s.useMemo(function(){return{filtered:m,search:p}},[m,p]),w=(0,l.useThrottledValue)(v),y=w.filtered,h=w.search;return s.useMemo(function(){if(h.trim()==="")return y.map(function(P){return{score:0,action:P}});var j=[],C=b.search(h);return j=C.map(function(P){var O=P.item,E=P.score;return{score:1/((E??0)+1),action:O}}),j},[y,h,b])}e.useDeepMatches=d}(ve)),ve}var me={},Wt,hn;function _u(){if(hn)return Wt;hn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,a=(b,v)=>{for(var w in v)t(b,w,{get:v[w],enumerable:!0})},l=(b,v,w,y)=>{if(v&&typeof v=="object"||typeof v=="function")for(let h of n(v))!s.call(b,h)&&h!==w&&t(b,h,{get:()=>v[h],enumerable:!(y=r(v,h))||y.enumerable});return b},i=(b,v,w)=>(w=b!=null?e(o(b)):{},l(!b||!b.__esModule?t(w,"default",{value:b,enumerable:!0}):w,b)),u=b=>l(t({},"__esModule",{value:!0}),b),f={};a(f,{composeRefs:()=>m,useComposedRefs:()=>p}),Wt=u(f);var d=i(re());function g(b,v){if(typeof b=="function")return b(v);b!=null&&(b.current=v)}function m(...b){return v=>{let w=!1;const y=b.map(h=>{const j=g(h,v);return!w&&typeof j=="function"&&(w=!0),j});if(w)return()=>{for(let h=0;h<y.length;h++){const j=y[h];typeof j=="function"?j():g(b[h],null)}}}}function p(...b){return d.useCallback(m(...b),b)}return Wt}var Ht,vn;function ju(){if(vn)return Ht;vn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,a=(O,E)=>{for(var M in E)t(O,M,{get:E[M],enumerable:!0})},l=(O,E,M,x)=>{if(E&&typeof E=="object"||typeof E=="function")for(let _ of n(E))!s.call(O,_)&&_!==M&&t(O,_,{get:()=>E[_],enumerable:!(x=r(E,_))||x.enumerable});return O},i=(O,E,M)=>(M=O!=null?e(o(O)):{},l(!O||!O.__esModule?t(M,"default",{value:O,enumerable:!0}):M,O)),u=O=>l(t({},"__esModule",{value:!0}),O),f={};a(f,{Root:()=>b,Slot:()=>b,Slottable:()=>h,createSlot:()=>p,createSlottable:()=>y}),Ht=u(f);var d=i(re()),g=_u(),m=mr();function p(O){const E=v(O),M=d.forwardRef((x,_)=>{const{children:I,...k}=x,S=d.Children.toArray(I),N=S.find(j);if(N){const A=N.props.children,D=S.map(R=>R===N?d.Children.count(A)>1?d.Children.only(null):d.isValidElement(A)?A.props.children:null:R);return(0,m.jsx)(E,{...k,ref:_,children:d.isValidElement(A)?d.cloneElement(A,void 0,D):null})}return(0,m.jsx)(E,{...k,ref:_,children:I})});return M.displayName=`${O}.Slot`,M}var b=p("Slot");function v(O){const E=d.forwardRef((M,x)=>{const{children:_,...I}=M;if(d.isValidElement(_)){const k=P(_),S=C(I,_.props);return _.type!==d.Fragment&&(S.ref=x?(0,g.composeRefs)(x,k):k),d.cloneElement(_,S)}return d.Children.count(_)>1?d.Children.only(null):null});return E.displayName=`${O}.SlotClone`,E}var w=Symbol("radix.slottable");function y(O){const E=({children:M})=>(0,m.jsx)(m.Fragment,{children:M});return E.displayName=`${O}.Slottable`,E.__radixId=w,E}var h=y("Slottable");function j(O){return d.isValidElement(O)&&typeof O.type=="function"&&"__radixId"in O.type&&O.type.__radixId===w}function C(O,E){const M={...E};for(const x in E){const _=O[x],I=E[x];/^on[A-Z]/.test(x)?_&&I?M[x]=(...S)=>{const N=I(...S);return _(...S),N}:_&&(M[x]=_):x==="style"?M[x]={..._,...I}:x==="className"&&(M[x]=[_,I].filter(Boolean).join(" "))}return{...O,...M}}function P(O){let E=Object.getOwnPropertyDescriptor(O.props,"ref")?.get,M=E&&"isReactWarning"in E&&E.isReactWarning;return M?O.ref:(E=Object.getOwnPropertyDescriptor(O,"ref")?.get,M=E&&"isReactWarning"in E&&E.isReactWarning,M?O.props.ref:O.props.ref||O.ref)}return Ht}var Gt,mn;function Su(){if(mn)return Gt;mn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,a=(h,j)=>{for(var C in j)t(h,C,{get:j[C],enumerable:!0})},l=(h,j,C,P)=>{if(j&&typeof j=="object"||typeof j=="function")for(let O of n(j))!s.call(h,O)&&O!==C&&t(h,O,{get:()=>j[O],enumerable:!(P=r(j,O))||P.enumerable});return h},i=(h,j,C)=>(C=h!=null?e(o(h)):{},l(!h||!h.__esModule?t(C,"default",{value:h,enumerable:!0}):C,h)),u=h=>l(t({},"__esModule",{value:!0}),h),f={};a(f,{Primitive:()=>v,Root:()=>y,dispatchDiscreteCustomEvent:()=>w}),Gt=u(f);var d=i(re()),g=i(Pn()),m=ju(),p=mr(),b=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],v=b.reduce((h,j)=>{const C=(0,m.createSlot)(`Primitive.${j}`),P=d.forwardRef((O,E)=>{const{asChild:M,...x}=O,_=M?C:j;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),(0,p.jsx)(_,{...x,ref:E})});return P.displayName=`Primitive.${j}`,{...h,[j]:P}},{});function w(h,j){h&&g.flushSync(()=>h.dispatchEvent(j))}var y=v;return Gt}var Yt,gn;function Eu(){if(gn)return Yt;gn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,a=(m,p)=>{for(var b in p)t(m,b,{get:p[b],enumerable:!0})},l=(m,p,b,v)=>{if(p&&typeof p=="object"||typeof p=="function")for(let w of n(p))!s.call(m,w)&&w!==b&&t(m,w,{get:()=>p[w],enumerable:!(v=r(p,w))||v.enumerable});return m},i=(m,p,b)=>(b=m!=null?e(o(m)):{},l(!m||!m.__esModule?t(b,"default",{value:m,enumerable:!0}):b,m)),u=m=>l(t({},"__esModule",{value:!0}),m),f={};a(f,{useLayoutEffect:()=>g}),Yt=u(f);var d=i(re()),g=globalThis?.document?d.useLayoutEffect:()=>{};return Yt}var Xt,bn;function Ou(){if(bn)return Xt;bn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,a=(h,j)=>{for(var C in j)t(h,C,{get:j[C],enumerable:!0})},l=(h,j,C,P)=>{if(j&&typeof j=="object"||typeof j=="function")for(let O of n(j))!s.call(h,O)&&O!==C&&t(h,O,{get:()=>j[O],enumerable:!(P=r(j,O))||P.enumerable});return h},i=(h,j,C)=>(C=h!=null?e(o(h)):{},l(!h||!h.__esModule?t(C,"default",{value:h,enumerable:!0}):C,h)),u=h=>l(t({},"__esModule",{value:!0}),h),f={};a(f,{Portal:()=>w,Root:()=>y}),Xt=u(f);var d=i(re()),g=i(Pn()),m=Su(),p=Eu(),b=mr(),v="Portal",w=d.forwardRef((h,j)=>{const{container:C,...P}=h,[O,E]=d.useState(!1);(0,p.useLayoutEffect)(()=>E(!0),[]);const M=C||O&&globalThis?.document?.body;return M?g.default.createPortal((0,b.jsx)(m.Primitive.div,{...P,ref:j}),M):null});w.displayName=v;var y=w;return Xt}var yn;function Cu(){if(yn)return me;yn=1;var e=me&&me.__createBinding||(Object.create?function(i,u,f,d){d===void 0&&(d=f),Object.defineProperty(i,d,{enumerable:!0,get:function(){return u[f]}})}:function(i,u,f,d){d===void 0&&(d=f),i[d]=u[f]}),t=me&&me.__setModuleDefault||(Object.create?function(i,u){Object.defineProperty(i,"default",{enumerable:!0,value:u})}:function(i,u){i.default=u}),r=me&&me.__importStar||function(i){if(i&&i.__esModule)return i;var u={};if(i!=null)for(var f in i)f!=="default"&&Object.prototype.hasOwnProperty.call(i,f)&&e(u,i,f);return t(u,i),u};Object.defineProperty(me,"__esModule",{value:!0}),me.KBarPortal=void 0;var n=Ou(),o=r(re()),s=Qe(),a=ke();function l(i){var u=i.children,f=i.container,d=(0,a.useKBar)(function(g){return{showing:g.visualState!==s.VisualState.hidden}}).showing;return d?o.createElement(n.Portal,{container:f},u):null}return me.KBarPortal=l,me}var ie={},xn;function Mu(){if(xn)return ie;xn=1;var e=ie&&ie.__assign||function(){return e=Object.assign||function(i){for(var u,f=1,d=arguments.length;f<d;f++){u=arguments[f];for(var g in u)Object.prototype.hasOwnProperty.call(u,g)&&(i[g]=u[g])}return i},e.apply(this,arguments)},t=ie&&ie.__createBinding||(Object.create?function(i,u,f,d){d===void 0&&(d=f),Object.defineProperty(i,d,{enumerable:!0,get:function(){return u[f]}})}:function(i,u,f,d){d===void 0&&(d=f),i[d]=u[f]}),r=ie&&ie.__setModuleDefault||(Object.create?function(i,u){Object.defineProperty(i,"default",{enumerable:!0,value:u})}:function(i,u){i.default=u}),n=ie&&ie.__importStar||function(i){if(i&&i.__esModule)return i;var u={};if(i!=null)for(var f in i)f!=="default"&&Object.prototype.hasOwnProperty.call(i,f)&&t(u,i,f);return r(u,i),u},o=ie&&ie.__rest||function(i,u){var f={};for(var d in i)Object.prototype.hasOwnProperty.call(i,d)&&u.indexOf(d)<0&&(f[d]=i[d]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var g=0,d=Object.getOwnPropertySymbols(i);g<d.length;g++)u.indexOf(d[g])<0&&Object.prototype.propertyIsEnumerable.call(i,d[g])&&(f[d[g]]=i[d[g]]);return f};Object.defineProperty(ie,"__esModule",{value:!0}),ie.KBarPositioner=void 0;var s=n(re()),a={position:"fixed",display:"flex",alignItems:"flex-start",justifyContent:"center",width:"100%",inset:"0px",padding:"14vh 16px 16px"};function l(i){return i?e(e({},a),i):a}return ie.KBarPositioner=s.forwardRef(function(i,u){var f=i.style,d=i.children,g=o(i,["style","children"]);return s.createElement("div",e({ref:u,style:l(f)},g),d)}),ie}var ue={},wn;function Bo(){return wn||(wn=1,function(e){var t=ue&&ue.__assign||function(){return t=Object.assign||function(d){for(var g,m=1,p=arguments.length;m<p;m++){g=arguments[m];for(var b in g)Object.prototype.hasOwnProperty.call(g,b)&&(d[b]=g[b])}return d},t.apply(this,arguments)},r=ue&&ue.__createBinding||(Object.create?function(d,g,m,p){p===void 0&&(p=m),Object.defineProperty(d,p,{enumerable:!0,get:function(){return g[m]}})}:function(d,g,m,p){p===void 0&&(p=m),d[p]=g[m]}),n=ue&&ue.__setModuleDefault||(Object.create?function(d,g){Object.defineProperty(d,"default",{enumerable:!0,value:g})}:function(d,g){d.default=g}),o=ue&&ue.__importStar||function(d){if(d&&d.__esModule)return d;var g={};if(d!=null)for(var m in d)m!=="default"&&Object.prototype.hasOwnProperty.call(d,m)&&r(g,d,m);return n(g,d),g},s=ue&&ue.__rest||function(d,g){var m={};for(var p in d)Object.prototype.hasOwnProperty.call(d,p)&&g.indexOf(p)<0&&(m[p]=d[p]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var b=0,p=Object.getOwnPropertySymbols(d);b<p.length;b++)g.indexOf(p[b])<0&&Object.prototype.propertyIsEnumerable.call(d,p[b])&&(m[p[b]]=d[p[b]]);return m};Object.defineProperty(e,"__esModule",{value:!0}),e.KBarSearch=e.getListboxItemId=e.KBAR_LISTBOX=void 0;var a=o(re()),l=Qe(),i=ke();e.KBAR_LISTBOX="kbar-listbox";var u=function(d){return"kbar-listbox-item-"+d};e.getListboxItemId=u;function f(d){var g=(0,i.useKBar)(function(x){return{search:x.searchQuery,currentRootActionId:x.currentRootActionId,actions:x.actions,activeIndex:x.activeIndex,showing:x.visualState===l.VisualState.showing}}),m=g.query,p=g.search,b=g.actions,v=g.currentRootActionId,w=g.activeIndex,y=g.showing,h=g.options,j=a.useState(p),C=j[0],P=j[1];a.useEffect(function(){m.setSearch(C)},[C,m]);var O=d.defaultPlaceholder,E=s(d,["defaultPlaceholder"]);a.useEffect(function(){return m.setSearch(""),m.getInput().focus(),function(){return m.setSearch("")}},[v,m]);var M=a.useMemo(function(){var x=O??"Type a command or search…";return v&&b[v]?b[v].name:x},[b,v,O]);return a.createElement("input",t({},E,{ref:m.inputRefSetter,autoFocus:!0,autoComplete:"off",role:"combobox",spellCheck:"false","aria-expanded":y,"aria-controls":e.KBAR_LISTBOX,"aria-activedescendant":(0,e.getListboxItemId)(w),value:C,placeholder:M,onChange:function(x){var _,I,k;(_=d.onChange)===null||_===void 0||_.call(d,x),P(x.target.value),(k=(I=h?.callbacks)===null||I===void 0?void 0:I.onQueryChange)===null||k===void 0||k.call(I,x.target.value)},onKeyDown:function(x){var _;if((_=d.onKeyDown)===null||_===void 0||_.call(d,x),v&&!p&&x.key==="Backspace"){var I=b[v].parent;m.setCurrentRootAction(I)}}}))}e.KBarSearch=f}(ue)),ue}var de={};function Fe(){return Fe=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Fe.apply(this,arguments)}function Pu(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var Tu=["bottom","height","left","right","top","width"],ku=function(t,r){return t===void 0&&(t={}),r===void 0&&(r={}),Tu.some(function(n){return t[n]!==r[n]})},Pe=new Map,$o,Ru=function e(){var t=[];Pe.forEach(function(r,n){var o=n.getBoundingClientRect();ku(o,r.rect)&&(r.rect=o,t.push(r))}),t.forEach(function(r){r.callbacks.forEach(function(n){return n(r.rect)})}),$o=window.requestAnimationFrame(e)};function Iu(e,t){return{observe:function(){var n=Pe.size===0;Pe.has(e)?Pe.get(e).callbacks.push(t):Pe.set(e,{rect:void 0,hasRectChanged:!1,callbacks:[t]}),n&&Ru()},unobserve:function(){var n=Pe.get(e);if(n){var o=n.callbacks.indexOf(t);o>=0&&n.callbacks.splice(o,1),n.callbacks.length||Pe.delete(e),Pe.size||cancelAnimationFrame($o)}}}}var Ct=typeof window<"u"?X.useLayoutEffect:X.useEffect;function Au(e,t){t===void 0&&(t={width:0,height:0});var r=X.useState(e.current),n=r[0],o=r[1],s=X.useReducer(Nu,t),a=s[0],l=s[1],i=X.useRef(!1);return Ct(function(){e.current!==n&&o(e.current)}),Ct(function(){if(n&&!i.current){i.current=!0;var u=n.getBoundingClientRect();l({rect:u})}},[n]),X.useEffect(function(){if(n){var u=Iu(n,function(f){l({rect:f})});return u.observe(),function(){u.unobserve()}}},[n]),a}function Nu(e,t){var r=t.rect;return e.height!==r.height||e.width!==r.width?r:e}var Du=function(){return 50},Lu=function(t){return t},zu=function(t,r){var n=r?"offsetWidth":"offsetHeight";return t[n]},Ko=function(t){for(var r=Math.max(t.start-t.overscan,0),n=Math.min(t.end+t.overscan,t.size-1),o=[],s=r;s<=n;s++)o.push(s);return o};function Bu(e){var t,r=e.size,n=r===void 0?0:r,o=e.estimateSize,s=o===void 0?Du:o,a=e.overscan,l=a===void 0?1:a,i=e.paddingStart,u=i===void 0?0:i,f=e.paddingEnd,d=f===void 0?0:f,g=e.parentRef,m=e.horizontal,p=e.scrollToFn,b=e.useObserver,v=e.initialRect,w=e.onScrollElement,y=e.scrollOffsetFn,h=e.keyExtractor,j=h===void 0?Lu:h,C=e.measureSize,P=C===void 0?zu:C,O=e.rangeExtractor,E=O===void 0?Ko:O,M=m?"width":"height",x=m?"scrollLeft":"scrollTop",_=X.useRef({scrollOffset:0,measurements:[]}),I=X.useState(0),k=I[0],S=I[1];_.current.scrollOffset=k;var N=b||Au,A=N(g,v),D=A[M];_.current.outerSize=D;var R=X.useCallback(function(G){g.current&&(g.current[x]=G)},[g,x]),V=p||R;p=X.useCallback(function(G){V(G,R)},[R,V]);var W=X.useState({}),Q=W[0],Z=W[1],q=X.useCallback(function(){return Z({})},[]),U=X.useRef([]),ae=X.useMemo(function(){var G=U.current.length>0?Math.min.apply(Math,U.current):0;U.current=[];for(var te=_.current.measurements.slice(0,G),Y=G;Y<n;Y++){var he=j(Y),ee=Q[he],we=te[Y-1]?te[Y-1].end:u,be=typeof ee=="number"?ee:s(Y),ye=we+be;te[Y]={index:Y,start:we,size:be,end:ye,key:he}}return te},[s,Q,u,n,j]),Ze=(((t=ae[n-1])==null?void 0:t.end)||u)+d;_.current.measurements=ae,_.current.totalSize=Ze;var H=w?w.current:g.current,Ue=X.useRef(y);Ue.current=y,Ct(function(){if(!H){S(0);return}var G=function(Y){var he=Ue.current?Ue.current(Y):H[x];S(he)};return G(),H.addEventListener("scroll",G,{capture:!1,passive:!0}),function(){H.removeEventListener("scroll",G)}},[H,x]);var Ie=Ku(_.current),Ae=Ie.start,We=Ie.end,Oe=X.useMemo(function(){return E({start:Ae,end:We,overscan:l,size:ae.length})},[Ae,We,l,ae.length,E]),Cr=X.useRef(P);Cr.current=P;var Fo=X.useMemo(function(){for(var G=[],te=function(we,be){var ye=Oe[we],ut=ae[ye],Ne=Fe(Fe({},ut),{},{measureRef:function(dt){if(dt){var Nt=Cr.current(dt,m);if(Nt!==Ne.size){var Pr=_.current.scrollOffset;Ne.start<Pr&&R(Pr+(Nt-Ne.size)),U.current.push(ye),Z(function(qo){var Dt;return Fe(Fe({},qo),{},(Dt={},Dt[Ne.key]=Nt,Dt))})}}}});G.push(Ne)},Y=0,he=Oe.length;Y<he;Y++)te(Y);return G},[Oe,R,m,ae]),Mr=X.useRef(!1);Ct(function(){Mr.current&&Z({}),Mr.current=!0},[s]);var It=X.useCallback(function(G,te){var Y=te===void 0?{}:te,he=Y.align,ee=he===void 0?"start":he,we=_.current,be=we.scrollOffset,ye=we.outerSize;ee==="auto"&&(G<=be?ee="start":G>=be+ye?ee="end":ee="start"),ee==="start"?p(G):ee==="end"?p(G-ye):ee==="center"&&p(G-ye/2)},[p]),At=X.useCallback(function(G,te){var Y=te===void 0?{}:te,he=Y.align,ee=he===void 0?"auto":he,we=Pu(Y,["align"]),be=_.current,ye=be.measurements,ut=be.scrollOffset,Ne=be.outerSize,Ce=ye[Math.max(0,Math.min(G,n-1))];if(Ce){if(ee==="auto")if(Ce.end>=ut+Ne)ee="end";else if(Ce.start<=ut)ee="start";else return;var dt=ee==="center"?Ce.start+Ce.size/2:ee==="end"?Ce.end:Ce.start;It(dt,Fe({align:ee},we))}},[It,n]),Vo=X.useCallback(function(){for(var G=arguments.length,te=new Array(G),Y=0;Y<G;Y++)te[Y]=arguments[Y];At.apply(void 0,te),requestAnimationFrame(function(){At.apply(void 0,te)})},[At]);return{virtualItems:Fo,totalSize:Ze,scrollToOffset:It,scrollToIndex:Vo,measure:q}}var $u=function(t,r,n,o){for(;t<=r;){var s=(t+r)/2|0,a=n(s);if(a<o)t=s+1;else if(a>o)r=s-1;else return s}return t>0?t-1:0};function Ku(e){for(var t=e.measurements,r=e.outerSize,n=e.scrollOffset,o=t.length-1,s=function(u){return t[u].start},a=$u(0,o,s,n),l=a;l<o&&t[l].end<n+r;)l++;return{start:a,end:l}}const Fu=Object.freeze(Object.defineProperty({__proto__:null,defaultRangeExtractor:Ko,useVirtual:Bu},Symbol.toStringTag,{value:"Module"})),Vu=Mn(Fu);var _n;function qu(){if(_n)return de;_n=1;var e=de&&de.__assign||function(){return e=Object.assign||function(d){for(var g,m=1,p=arguments.length;m<p;m++){g=arguments[m];for(var b in g)Object.prototype.hasOwnProperty.call(g,b)&&(d[b]=g[b])}return d},e.apply(this,arguments)},t=de&&de.__createBinding||(Object.create?function(d,g,m,p){p===void 0&&(p=m),Object.defineProperty(d,p,{enumerable:!0,get:function(){return g[m]}})}:function(d,g,m,p){p===void 0&&(p=m),d[p]=g[m]}),r=de&&de.__setModuleDefault||(Object.create?function(d,g){Object.defineProperty(d,"default",{enumerable:!0,value:g})}:function(d,g){d.default=g}),n=de&&de.__importStar||function(d){if(d&&d.__esModule)return d;var g={};if(d!=null)for(var m in d)m!=="default"&&Object.prototype.hasOwnProperty.call(d,m)&&t(g,d,m);return r(g,d),g};Object.defineProperty(de,"__esModule",{value:!0}),de.KBarResults=void 0;var o=n(re()),s=Vu,a=Bo(),l=ke(),i=qe(),u=0,f=function(d){var g=o.useRef(null),m=o.useRef(null),p=o.useRef(d.items);p.current=d.items;var b=(0,s.useVirtual)({size:p.current.length,parentRef:m}),v=(0,l.useKBar)(function(M){return{search:M.searchQuery,currentRootActionId:M.currentRootActionId,activeIndex:M.activeIndex}}),w=v.query,y=v.search,h=v.currentRootActionId,j=v.activeIndex,C=v.options;o.useEffect(function(){var M=function(x){var _;x.isComposing||(x.key==="ArrowUp"||x.ctrlKey&&x.key==="p"?(x.preventDefault(),x.stopPropagation(),w.setActiveIndex(function(I){var k=I>u?I-1:I;if(typeof p.current[k]=="string"){if(k===0)return I;k-=1}return k})):x.key==="ArrowDown"||x.ctrlKey&&x.key==="n"?(x.preventDefault(),x.stopPropagation(),w.setActiveIndex(function(I){var k=I<p.current.length-1?I+1:I;if(typeof p.current[k]=="string"){if(k===p.current.length-1)return I;k+=1}return k})):x.key==="Enter"&&(x.preventDefault(),x.stopPropagation(),(_=g.current)===null||_===void 0||_.click()))};return window.addEventListener("keydown",M,{capture:!0}),function(){return window.removeEventListener("keydown",M,{capture:!0})}},[w]);var P=b.scrollToIndex;o.useEffect(function(){P(j,{align:j<=1?"end":"auto"})},[j,P]),o.useEffect(function(){w.setActiveIndex(typeof d.items[u]=="string"?u+1:u)},[y,h,d.items,w]);var O=o.useCallback(function(M){var x,_;typeof M!="string"&&(M.command?(M.command.perform(M),w.toggle()):(w.setSearch(""),w.setCurrentRootAction(M.id)),(_=(x=C.callbacks)===null||x===void 0?void 0:x.onSelectAction)===null||_===void 0||_.call(x,M))},[w,C]),E=(0,i.usePointerMovedSinceMount)();return o.createElement("div",{ref:m,style:{maxHeight:d.maxHeight||400,position:"relative",overflow:"auto"}},o.createElement("div",{role:"listbox",id:a.KBAR_LISTBOX,style:{height:b.totalSize+"px",width:"100%"}},b.virtualItems.map(function(M){var x=p.current[M.index],_=typeof x!="string"&&{onPointerMove:function(){return E&&j!==M.index&&w.setActiveIndex(M.index)},onPointerDown:function(){return w.setActiveIndex(M.index)},onClick:function(){return O(x)}},I=M.index===j;return o.createElement("div",e({ref:I?g:null,id:(0,a.getListboxItemId)(M.index),role:"option","aria-selected":I,key:M.index,style:{position:"absolute",top:0,left:0,width:"100%",transform:"translateY("+M.start+"px)"}},_),o.cloneElement(d.onRender({item:x,active:I}),{ref:M.measureRef}))})))};return de.KBarResults=f,de}var ge={},jn;function Uu(){if(jn)return ge;jn=1;var e=ge&&ge.__createBinding||(Object.create?function(a,l,i,u){u===void 0&&(u=i),Object.defineProperty(a,u,{enumerable:!0,get:function(){return l[i]}})}:function(a,l,i,u){u===void 0&&(u=i),a[u]=l[i]}),t=ge&&ge.__setModuleDefault||(Object.create?function(a,l){Object.defineProperty(a,"default",{enumerable:!0,value:l})}:function(a,l){a.default=l}),r=ge&&ge.__importStar||function(a){if(a&&a.__esModule)return a;var l={};if(a!=null)for(var i in a)i!=="default"&&Object.prototype.hasOwnProperty.call(a,i)&&e(l,a,i);return t(l,a),l};Object.defineProperty(ge,"__esModule",{value:!0}),ge.useRegisterActions=void 0;var n=r(re()),o=ke();function s(a,l){l===void 0&&(l=[]);var i=(0,o.useKBar)().query,u=n.useMemo(function(){return a},l);n.useEffect(function(){if(u.length){var f=i.registerActions(u);return function(){f()}}},[i,u])}return ge.useRegisterActions=s,ge}var fe={},Sn;function Wu(){if(Sn)return fe;Sn=1;var e=fe&&fe.__assign||function(){return e=Object.assign||function(d){for(var g,m=1,p=arguments.length;m<p;m++){g=arguments[m];for(var b in g)Object.prototype.hasOwnProperty.call(g,b)&&(d[b]=g[b])}return d},e.apply(this,arguments)},t=fe&&fe.__createBinding||(Object.create?function(d,g,m,p){p===void 0&&(p=m),Object.defineProperty(d,p,{enumerable:!0,get:function(){return g[m]}})}:function(d,g,m,p){p===void 0&&(p=m),d[p]=g[m]}),r=fe&&fe.__setModuleDefault||(Object.create?function(d,g){Object.defineProperty(d,"default",{enumerable:!0,value:g})}:function(d,g){d.default=g}),n=fe&&fe.__importStar||function(d){if(d&&d.__esModule)return d;var g={};if(d!=null)for(var m in d)m!=="default"&&Object.prototype.hasOwnProperty.call(d,m)&&t(g,d,m);return r(g,d),g};Object.defineProperty(fe,"__esModule",{value:!0}),fe.KBarAnimator=void 0;var o=n(re()),s=Qe(),a=ke(),l=qe(),i=[{opacity:0,transform:"scale(.99)"},{opacity:1,transform:"scale(1.01)"},{opacity:1,transform:"scale(1)"}],u=[{transform:"scale(1)"},{transform:"scale(.98)"},{transform:"scale(1)"}],f=function(d){var g,m,p=d.children,b=d.style,v=d.className,w=d.disableCloseOnOuterClick,y=(0,a.useKBar)(function(k){return{visualState:k.visualState,currentRootActionId:k.currentRootActionId}}),h=y.visualState,j=y.currentRootActionId,C=y.query,P=y.options,O=o.useRef(null),E=o.useRef(null),M=((g=P?.animations)===null||g===void 0?void 0:g.enterMs)||0,x=((m=P?.animations)===null||m===void 0?void 0:m.exitMs)||0;o.useEffect(function(){if(h!==s.VisualState.showing){var k=h===s.VisualState.animatingIn?M:x,S=O.current;S?.animate(i,{duration:k,easing:h===s.VisualState.animatingOut?"ease-in":"ease-out",direction:h===s.VisualState.animatingOut?"reverse":"normal",fill:"forwards"})}},[P,h,M,x]);var _=o.useRef();o.useEffect(function(){if(h===s.VisualState.showing){var k=O.current,S=E.current;if(!k||!S)return;var N=new ResizeObserver(function(A){for(var D=0,R=A;D<R.length;D++){var V=R[D],W=V.contentRect;_.current||(_.current=W.height),k.animate([{height:_.current+"px"},{height:W.height+"px"}],{duration:M/2,easing:"ease-out",fill:"forwards"}),_.current=W.height}});return N.observe(S),function(){N.unobserve(S)}}},[h,P,M,x]);var I=o.useRef(!0);return o.useEffect(function(){if(I.current){I.current=!1;return}var k=O.current;k&&k.animate(u,{duration:M,easing:"ease-out"})},[j,M]),(0,l.useOuterClick)(O,function(){var k,S;w||(C.setVisualState(s.VisualState.animatingOut),(S=(k=P.callbacks)===null||k===void 0?void 0:k.onClose)===null||S===void 0||S.call(k))}),o.createElement("div",{ref:O,style:e(e(e({},i[0]),b),{pointerEvents:"auto"}),className:v},o.createElement("div",{ref:E},p))};return fe.KBarAnimator=f,fe}var $e={},En;function Hu(){return En||(En=1,function(e){var t=$e&&$e.__createBinding||(Object.create?function(n,o,s,a){a===void 0&&(a=s),Object.defineProperty(n,a,{enumerable:!0,get:function(){return o[s]}})}:function(n,o,s,a){a===void 0&&(a=s),n[a]=o[s]}),r=$e&&$e.__exportStar||function(n,o){for(var s in n)s!=="default"&&!Object.prototype.hasOwnProperty.call(o,s)&&t(o,n,s)};Object.defineProperty(e,"__esModule",{value:!0}),r(Mo(),e),r(Co(),e)}($e)),$e}var On;function Gu(){return On||(On=1,function(e){var t=Le&&Le.__createBinding||(Object.create?function(o,s,a,l){l===void 0&&(l=a),Object.defineProperty(o,l,{enumerable:!0,get:function(){return s[a]}})}:function(o,s,a,l){l===void 0&&(l=a),o[l]=s[a]}),r=Le&&Le.__exportStar||function(o,s){for(var a in o)a!=="default"&&!Object.prototype.hasOwnProperty.call(s,a)&&t(s,o,a)};Object.defineProperty(e,"__esModule",{value:!0}),e.Priority=e.createAction=void 0;var n=qe();Object.defineProperty(e,"createAction",{enumerable:!0,get:function(){return n.createAction}}),Object.defineProperty(e,"Priority",{enumerable:!0,get:function(){return n.Priority}}),r(wu(),e),r(Cu(),e),r(Mu(),e),r(Bo(),e),r(qu(),e),r(ke(),e),r(Uu(),e),r(Po(),e),r(Wu(),e),r(Qe(),e),r(Hu(),e)}(Le)),Le}var Yu=Gu();function Xu(){const{query:e}=Yu.useKBar();return c.jsx("div",{className:"w-full space-y-2",children:c.jsxs(Tt,{variant:"outline",className:"bg-background text-muted-foreground relative h-9 w-full justify-start rounded-[0.5rem] text-sm font-normal shadow-none sm:pr-12 md:w-40 lg:w-64",onClick:e?.toggle,children:[c.jsx(ac,{className:"mr-2 h-4 w-4"}),"Search...",c.jsxs("kbd",{className:"bg-muted pointer-events-none absolute top-[0.3rem] right-[0.3rem] hidden h-6 items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-100 select-none sm:flex",children:[c.jsx("span",{className:"text-xs",children:"⌘"}),"K"]})]})})}function Qu({className:e,...t}){return c.jsx(Ma,{"data-slot":"label",className:B("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}function Ju({...e}){return c.jsx(Pa,{"data-slot":"select",...e})}function Qt({...e}){return c.jsx(Da,{"data-slot":"select-group",...e})}function Zu({...e}){return c.jsx(Ra,{"data-slot":"select-value",...e})}function ed({className:e,size:t="default",children:r,clearable:n,onClear:o,...s}){return c.jsxs(Ta,{"data-slot":"select-trigger","data-size":t,className:B("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s,children:[r,n&&o&&c.jsx("button",{type:"button",onClick:a=>{a.stopPropagation(),o()},className:"hover:bg-muted rounded-sm p-1 transition-colors",children:c.jsx(wr,{className:"size-3 opacity-50"})}),c.jsx(ka,{asChild:!0,children:c.jsx(Zn,{className:"size-4 opacity-50"})})]})}function td({className:e,children:t,position:r="popper",...n}){return c.jsx(Ia,{children:c.jsxs(Aa,{"data-slot":"select-content",className:B("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",r==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[c.jsx(nd,{}),c.jsx(Na,{className:B("p-1",r==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),c.jsx(od,{})]})})}function Jt({className:e,...t}){return c.jsx(La,{"data-slot":"select-label",className:B("text-muted-foreground px-2 py-1.5 text-xs",e),...t})}function Zt({className:e,children:t,...r}){return c.jsxs(za,{"data-slot":"select-item",className:B("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[c.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:c.jsx(Ba,{children:c.jsx(Ms,{className:"size-4"})})}),c.jsx($a,{children:t})]})}function rd({className:e,...t}){return c.jsx(Ka,{"data-slot":"select-separator",className:B("bg-border pointer-events-none -mx-1 my-1 h-px",e),...t})}function nd({className:e,...t}){return c.jsx(Fa,{"data-slot":"select-scroll-up-button",className:B("flex cursor-default items-center justify-center py-1",e),...t,children:c.jsx(Is,{className:"size-4"})})}function od({className:e,...t}){return c.jsx(Va,{"data-slot":"select-scroll-down-button",className:B("flex cursor-default items-center justify-center py-1",e),...t,children:c.jsx(Zn,{className:"size-4"})})}const ad=[{name:"Default",value:"default"},{name:"Blue",value:"blue"},{name:"Green",value:"green"},{name:"Amber",value:"amber"}],sd=[{name:"Default",value:"default-scaled"},{name:"Blue",value:"blue-scaled"},{name:"Green",value:"green-scaled"}],id=[{name:"Mono",value:"mono-scaled"}];function cd(){const{activeTheme:e,setActiveTheme:t}=po(),r=n=>{t(n)};return c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx(Qu,{htmlFor:"theme-selector",className:"sr-only",children:"Theme"}),c.jsxs(Ju,{value:e,onValueChange:r,children:[c.jsxs(ed,{id:"theme-selector",className:"justify-start *:data-[slot=select-value]:w-12",children:[c.jsx("span",{className:"text-muted-foreground hidden sm:block",children:"Select a theme:"}),c.jsx("span",{className:"text-muted-foreground block sm:hidden",children:"Theme"}),c.jsx(Zu,{placeholder:"Select a theme"})]}),c.jsxs(td,{align:"end",children:[c.jsxs(Qt,{children:[c.jsx(Jt,{children:"Default"}),ad.map(n=>c.jsx(Zt,{value:n.value,children:n.name},n.name))]}),c.jsx(rd,{}),c.jsxs(Qt,{children:[c.jsx(Jt,{children:"Scaled"}),sd.map(n=>c.jsx(Zt,{value:n.value,children:n.name},n.name))]}),c.jsxs(Qt,{children:[c.jsx(Jt,{children:"Monospaced"}),id.map(n=>c.jsx(Zt,{value:n.value,children:n.name},n.name))]})]})]})]})}function ld(){const{setTheme:e,resolvedTheme:t}=Ya(),r=T.useCallback(n=>{const o=t==="dark"?"light":"dark",s=document.documentElement;if(!document.startViewTransition){e(o);return}n&&(s.style.setProperty("--x",`${n.clientX}px`),s.style.setProperty("--y",`${n.clientY}px`)),document.startViewTransition(()=>{e(o)})},[t,e]);return c.jsxs(Tt,{variant:"secondary",size:"icon",className:"group/toggle size-8",onClick:r,children:[c.jsx(Yi,{}),c.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}function ud(){return c.jsxs("header",{role:"banner",className:"sticky top-0 z-50 bg-background shadow flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12",children:[c.jsxs("div",{className:"flex items-center gap-2 px-4",children:[c.jsx(nl,{className:"-ml-1"}),c.jsx(gc,{orientation:"vertical",className:"mr-2 h-4"}),c.jsx(Ol,{})]}),c.jsxs("div",{className:"flex items-center gap-2 px-4",children:[c.jsx("div",{className:"hidden md:flex",children:c.jsx(Xu,{})}),c.jsx(ld,{}),c.jsx(cd,{})]})]})}function dd({children:e,policy:t,message:r}){const n=ot.get("sidebar_state")==="true",{activeTheme:o}=po(),s=o.endsWith("-scaled");return c.jsxs("div",{className:B("bg-background font-sans antialiased",`theme-${o}`,s?"theme-scaled":""),children:[c.jsx(uo,{}),c.jsxs(tl,{defaultOpen:n,children:[c.jsx(_l,{}),c.jsxs(al,{children:[c.jsx(ud,{}),c.jsx("div",{className:"flex flex-1 flex-col",children:c.jsx("div",{className:"@container/main flex flex-1 flex-col gap-2",children:c.jsx("div",{className:"flex flex-col gap-4 py-4 px-4 md:gap-6 md:py-6 md:px-6",children:t!==void 0?c.jsx(Fi,{policy:t,message:r,children:e}):e})})})]})]})]})}function fd(e){return c.jsx(T.StrictMode,{children:c.jsx(Wa,{children:c.jsx(Xa,{defaultTheme:"system",storageKey:"vite-ui-theme",children:c.jsxs(Ni,{children:[c.jsx(uo,{}),c.jsx(dd,{...e})]})})})})}function pd(){return c.jsx("div",{className:"flex flex-1 flex-col space-y-4",children:c.jsx("div",{className:"flex items-center justify-between space-y-2",children:c.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Identity Server Dashboard 👋"})})})}function yd(){return c.jsxs(fd,{children:[c.jsx(qa,{title:"Dashboard"}),c.jsx(pd,{})]})}export{yd as default};
//# sourceMappingURL=home-DJHc047C.js.map
