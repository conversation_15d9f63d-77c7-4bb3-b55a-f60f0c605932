using Imip.Ekb.Master.Agents;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbAgentConfiguration : IEntityTypeConfiguration<Agent>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbAgentConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<Agent> b)
    {
        b.ToTable($"M_Agent", schema);
        b.ConfigureByConvention();
    }
}
