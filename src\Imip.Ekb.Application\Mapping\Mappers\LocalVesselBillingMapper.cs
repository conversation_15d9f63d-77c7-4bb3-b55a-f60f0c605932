using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.Billing.LocalVesselBillings.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class LocalVesselBillingMapper : IMapperlyMapper
{
    [MapProperty(nameof(LocalVesselBilling.Id), nameof(LocalVesselBillingDto.Id))]
    [MapProperty(nameof(LocalVesselBilling.DocEntry), nameof(LocalVesselBillingDto.DocEntry))]
    [MapProperty(nameof(LocalVesselBilling.PortService), nameof(LocalVesselBillingDto.PortService))]
    [MapProperty(nameof(LocalVesselBilling.Status), nameof(LocalVesselBillingDto.Status))]
    [MapProperty(nameof(LocalVesselBilling.YearArrival), nameof(LocalVesselBillingDto.YearArrival))]
    [MapProperty(nameof(LocalVesselBilling.Jetty), nameof(LocalVesselBillingDto.Jetty))]
    [MapProperty(nameof(LocalVesselBilling.LocalId), nameof(LocalVesselBillingDto.LocalId))]
    [MapProperty(nameof(LocalVesselBilling.CreatedBy), nameof(LocalVesselBillingDto.CreatedBy))]
    [MapProperty(nameof(LocalVesselBilling.UpdatedBy), nameof(LocalVesselBillingDto.UpdatedBy))]
    [MapProperty(nameof(LocalVesselBilling.CreatedAt), nameof(LocalVesselBillingDto.CreatedAt))]
    [MapProperty(nameof(LocalVesselBilling.UpdatedAt), nameof(LocalVesselBillingDto.UpdatedAt))]
    [MapProperty(nameof(LocalVesselBilling.DocNum), nameof(LocalVesselBillingDto.DocNum))]
    [MapProperty(nameof(LocalVesselBilling.PostingDate), nameof(LocalVesselBillingDto.PostingDate))]
    [MapProperty(nameof(LocalVesselBilling.Remarks), nameof(LocalVesselBillingDto.Remarks))]
    [MapProperty(nameof(LocalVesselBilling.PeriodDate), nameof(LocalVesselBillingDto.PeriodDate))]
    [MapProperty(nameof(LocalVesselBilling.BillingNoteDate), nameof(LocalVesselBillingDto.BillingNoteDate))]
    [MapProperty(nameof(LocalVesselBilling.JettyId), nameof(LocalVesselBillingDto.JettyId))]
    public partial LocalVesselBillingDto MapToDto(LocalVesselBilling entity);

    [MapperIgnoreTarget(nameof(LocalVesselBilling.Id))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.DocEntry))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.CreatedBy))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.CreatedAt))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.CreationTime))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.CreatorId))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.LastModificationTime))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.LastModifierId))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.IsDeleted))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.DeleterId))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.DeletionTime))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.ExtraProperties))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.MasterJetty))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.Items))]
    public partial void MapToEntity(CreateUpdateLocalVesselBillingDto dto, LocalVesselBilling entity);

    [MapperIgnoreTarget(nameof(LocalVesselBilling.Id))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.DocEntry))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.CreatedBy))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.CreatedAt))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.CreationTime))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.CreatorId))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.LastModificationTime))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.LastModifierId))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.IsDeleted))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.DeleterId))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.DeletionTime))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.ExtraProperties))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.MasterJetty))]
    [MapperIgnoreTarget(nameof(LocalVesselBilling.Items))]
    public partial LocalVesselBilling MapToEntity(CreateUpdateLocalVesselBillingDto dto);

    public partial List<LocalVesselBillingDto> MapToDtoList(List<LocalVesselBilling> entities);
    public partial IEnumerable<LocalVesselBillingDto> MapToDtoEnumerable(IEnumerable<LocalVesselBilling> entities);

    public LocalVesselBillingWithItemsDto MapToDtoWithItems(LocalVesselBilling entity, List<BillingItemDto> items)
    {
        var dto = MapToDto(entity);
        var withItemsDto = new LocalVesselBillingWithItemsDto
        {
            Id = dto.Id,
            DocEntry = dto.DocEntry,
            PortService = dto.PortService,
            Status = dto.Status,
            YearArrival = dto.YearArrival,
            Jetty = dto.Jetty,
            LocalId = dto.LocalId,
            CreatedBy = dto.CreatedBy,
            UpdatedBy = dto.UpdatedBy,
            CreatedAt = dto.CreatedAt,
            UpdatedAt = dto.UpdatedAt,
            DocNum = dto.DocNum,
            PostingDate = dto.PostingDate,
            Remarks = dto.Remarks,
            PeriodDate = dto.PeriodDate,
            BillingNoteDate = dto.BillingNoteDate,
            JettyId = dto.JettyId,
            MasterJetty = dto.MasterJetty,
            Items = items
        };
        return withItemsDto;
    }
}