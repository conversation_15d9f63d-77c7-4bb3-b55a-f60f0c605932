using Imip.Ekb.BoundedZone.TradingVessels.TradingInvoice;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbTradingInvoiceConfiguration : IEntityTypeConfiguration<TradingInvoice>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbTradingInvoiceConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<TradingInvoice> b)
    {
        b.ToTable("R_Inv", EkbConsts.DbSchema);
        b.ConfigureByConvention();

        // Relationship: ZoneDetail has many TradingInvoices
        b.HasOne(x => x.ZoneDetail)
            .WithMany(z => z.TradingInvoices)
            .HasForeignKey(x => x.ZoneDetailId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);
    }
}