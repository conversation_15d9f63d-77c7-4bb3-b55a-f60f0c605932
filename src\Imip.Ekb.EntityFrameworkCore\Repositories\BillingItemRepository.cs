using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.Ekb.Repositories;

public class BillingItemRepository : EfCoreRepository<EkbDbContext, BillingItem, Guid>, IBillingItemRepository
{
    public BillingItemRepository(IDbContextProvider<EkbDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

    public async Task<IQueryable<BillingItem>> GetQueryableWithIncludesAsync()
    {
        return (await GetQueryableAsync()).AsNoTracking();
    }
}
