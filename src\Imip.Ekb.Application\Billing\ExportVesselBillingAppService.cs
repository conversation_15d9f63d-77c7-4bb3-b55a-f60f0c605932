using Imip.Ekb.Billing.LocalVesselBillings.Dtos;
using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Imip.Ekb.Billing.LocalVesselBillings.Interfaces;
using Imip.Ekb.Billing.ExportVesselBillings;

namespace Imip.Ekb.Application.Billing;

[Authorize]
public class ExportVesselBillingAppService :
    CrudAppService<
        ExportVesselBilling,
        ExportVesselBillingDto,
        Guid,
        QueryParametersDto,
        CreateUpdateExportVesselBillingDto>,
    IExportVesselBillingAppService
{
    private readonly IExportVesselBillingRepository _exportVesselBillingRepository;
    private readonly IRepository<BillingItem, Guid> _billingItemRepository;
    private readonly ExportVesselBillingMapper _exportVesselBillingMapper;
    private readonly ILogger<ExportVesselBillingAppService> _logger;

    public ExportVesselBillingAppService(
        IExportVesselBillingRepository exportVesselBillingRepository,
        IRepository<BillingItem, Guid> billingItemRepository,
        ExportVesselBillingMapper exportVesselBillingMapper,
        ILogger<ExportVesselBillingAppService> logger)
        : base(exportVesselBillingRepository)
    {
        _exportVesselBillingRepository = exportVesselBillingRepository;
        _billingItemRepository = billingItemRepository;
        _exportVesselBillingMapper = exportVesselBillingMapper;
        _logger = logger;
    }

    public override async Task<PagedResultDto<ExportVesselBillingDto>> GetListAsync(QueryParametersDto input)
    {
        await CheckGetListPolicyAsync();

        var query = await _exportVesselBillingRepository.GetQueryableWithIncludesAsync();

        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            query = query.OrderBy(input.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);
        var entities = await AsyncExecuter.ToListAsync(
            query.PageBy(input.SkipCount, input.MaxResultCount)
        );
        var dtos = entities.Select(MapToGetListOutputDto).ToList();
        return new PagedResultDto<ExportVesselBillingDto>(totalCount, dtos);
    }

    public async Task<PagedResultDto<ExportVesselBillingDto>> FilterListAsync(QueryParametersDto parameters)
    {
        await CheckGetListPolicyAsync();
        var query = await _exportVesselBillingRepository.GetQueryableWithIncludesAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(MapToGetListOutputDto).ToList();
        return new PagedResultDto<ExportVesselBillingDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    public override async Task<ExportVesselBillingDto> CreateAsync(CreateUpdateExportVesselBillingDto input)
    {
        await CheckCreatePolicyAsync();
        var entity = _exportVesselBillingMapper.MapToEntity(input);
        await _exportVesselBillingRepository.InsertAsync(entity, autoSave: true);
        var createdEntity = await _exportVesselBillingRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(createdEntity);
    }

    public override async Task<ExportVesselBillingDto> UpdateAsync(Guid id, CreateUpdateExportVesselBillingDto input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await _exportVesselBillingRepository.GetAsync(id);
        _exportVesselBillingMapper.MapToEntity(input, entity);
        await _exportVesselBillingRepository.UpdateAsync(entity, autoSave: true);
        var updatedEntity = await _exportVesselBillingRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(updatedEntity);
    }

    public async Task<ExportVesselBillingWithItemsDto> GetWithItemsAsync(Guid id)
    {
        await CheckGetPolicyAsync();
        var entity = await _exportVesselBillingRepository.GetAsync(id);
        var items = await GetItemsAsync(entity.DocEntry);
        return _exportVesselBillingMapper.MapToDtoWithItems(entity, items);
    }

    public async Task<List<BillingItemDto>> GetItemsAsync(int docEntry)
    {
        var items = await _billingItemRepository.GetListAsync(
            x => x.DocEntry == docEntry && x.Type == "ExportVesselBilling"
        );
        return new List<BillingItemDto>();
    }

    private IQueryable<ExportVesselBilling> ApplyDynamicQuery(IQueryable<ExportVesselBilling> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ExportVesselBilling>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ExportVesselBilling>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }

    protected override ExportVesselBillingDto MapToGetOutputDto(ExportVesselBilling entity)
    {
        return _exportVesselBillingMapper.MapToDto(entity);
    }

    protected override ExportVesselBillingDto MapToGetListOutputDto(ExportVesselBilling entity)
    {
        return _exportVesselBillingMapper.MapToDto(entity);
    }

    protected override ExportVesselBilling MapToEntity(CreateUpdateExportVesselBillingDto createInput)
    {
        return _exportVesselBillingMapper.MapToEntity(createInput);
    }

    protected override void MapToEntity(CreateUpdateExportVesselBillingDto updateInput, ExportVesselBilling entity)
    {
        _exportVesselBillingMapper.MapToEntity(updateInput, entity);
    }
}