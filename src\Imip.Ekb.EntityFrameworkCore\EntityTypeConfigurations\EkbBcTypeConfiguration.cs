using Imip.Ekb.Master.BcTypes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;


namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbBcTypeConfiguration : IEntityTypeConfiguration<BcType>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbBcTypeConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<BcType> builder)
    {
        builder.ToTable($"M_TBC", schema);
        builder.ConfigureByConvention();
    }
}