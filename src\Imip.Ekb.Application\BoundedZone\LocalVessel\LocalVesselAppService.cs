using LocalVesselEntity = Imip.Ekb.BoundedZone.LocalVessels.LocalVessel;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.LocalVessels;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp;
using Imip.Ekb.Master.Agents.Dtos;
using Imip.Ekb.Master.Tradings.Dtos;
using Imip.Ekb.Master.Surveyors.Dtos;
using Imip.Ekb.Master.Jetties.Dtos;
using Imip.Ekb.Master.Cargos.Dtos;
using Imip.Ekb.BoundedZone.LocalVessel;
using Imip.Ekb.Master.Dtos;
using Microsoft.Extensions.Configuration;
using Imip.Ekb.Master.PortOfLoadings.Dtos;
using Imip.Ekb.Master.DestinationPorts.Dtos;

namespace Imip.Ekb.Application.BoundedZone.LocalVessel;

[Authorize]
public class LocalVesselAppService :
    CrudAppService<
        LocalVesselEntity,
        LocalVesselDto,
        Guid,
        QueryParametersDto,
        CreateUpdateLocalVesselDto>,
    ILocalVesselAppService
{
    private readonly ILocalVesselRepository _localVesselRepository;
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly LocalVesselMapper _localVesselMapper;
    private readonly VesselMapper _vesselMapper;
    private readonly IConfiguration _configuration;
    private readonly ZoneDetailMapper _zoneDetailMapper;
    private readonly ILogger<LocalVesselAppService> _logger;

    public LocalVesselAppService(
        ILocalVesselRepository localVesselRepository,
        IZoneDetailRepository zoneDetailRepository,
        LocalVesselMapper localVesselMapper,
        VesselMapper vesselMapper,
        IConfiguration configuration,
        ZoneDetailMapper zoneDetailMapper,
        ILogger<LocalVesselAppService> logger)
        : base(localVesselRepository)
    {
        _configuration = configuration;
        _localVesselRepository = localVesselRepository;
        _zoneDetailRepository = zoneDetailRepository;
        _localVesselMapper = localVesselMapper;
        _vesselMapper = vesselMapper;
        _zoneDetailMapper = zoneDetailMapper;
        _logger = logger;
    }

    public override async Task<PagedResultDto<LocalVesselDto>> GetListAsync(QueryParametersDto input)
    {
        await CheckGetListPolicyAsync();

        var query = await _localVesselRepository.GetQueryableWithIncludesAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            query = query.OrderBy(input.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);

        var entities = await AsyncExecuter.ToListAsync(
            query.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(MapToGetListOutputDto).ToList();

        return new PagedResultDto<LocalVesselDto>(totalCount, dtos);
    }

    // DRY: Extracted mapping logic for ZoneDetail to VesselItemDto
    private VesselItemDto MapZoneDetailToItemDto(ZoneDetail zoneDetail)
    {
        var itemDto = _vesselMapper.MapZoneDetailToItemDtoWithType(zoneDetail, "LocalVessel");

        if (zoneDetail.Tenant != null)
        {
            itemDto.Tenant = _vesselMapper.MapTenantToTenantShortDto(zoneDetail.Tenant);
        }

        itemDto.Attachments = zoneDetail.DocAttachment != null && zoneDetail.DocAttachment.Any()
            ? zoneDetail.DocAttachment.Select(att => _vesselMapper.MapDocAttachmentToDto(att)).ToList()
            : new List<DocAttachmentSortDto>();

        return itemDto;
    }

    private IQueryable<LocalVesselEntity> ApplyDynamicQuery(IQueryable<LocalVesselEntity> query, QueryParametersDto parameters)
    {
        // Apply filters if provided
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<LocalVesselEntity>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<LocalVesselEntity>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }


    // Generate next DocNum for a given postDate (YYMM + 4 digit increment, reset every month)
    public async Task<string> GenerateNextDocNumAsync(DateTime postDate)
    {
        var prefix = postDate.ToString("yyMM");
        var queryable = await _localVesselRepository.GetQueryableAsync();
        var maxDocNum = queryable
            .Where(x => x.DocNum.StartsWith(prefix))
            .OrderByDescending(x => x.DocNum)
            .Select(x => x.DocNum)
            .FirstOrDefault();

        int nextIncrement = 1;
        if (!string.IsNullOrEmpty(maxDocNum) && maxDocNum.Length > 4)
        {
            var lastIncrementStr = maxDocNum.Substring(4, 4);
            if (int.TryParse(lastIncrementStr, out var lastIncrement))
            {
                nextIncrement = lastIncrement + 1;
            }
        }
        var docNum = $"{prefix}{nextIncrement:D4}";
        return docNum;
    }

    public override async Task<LocalVesselDto> CreateAsync(CreateUpdateLocalVesselDto input)
    {
        await CheckCreatePolicyAsync();

        var dt = input.PostingDate;
        input.DocNum = (await GenerateNextDocNumAsync(dt.ToDateTime(TimeOnly.MinValue))).ToString();

        var entity = _localVesselMapper.CreateEntityWithId(input, Guid.NewGuid());

        await _localVesselRepository.InsertAsync(entity, autoSave: false);

        // Set HeaderId for each item after entity is inserted (if needed)
        if (input.Items != null && input.Items.Count != 0)
        {
            foreach (var itemDto in input.Items)
            {
                // If HeaderId is Guid, assign entity.Id
                itemDto.HeaderId = entity.Id;
                itemDto.DocNum = entity.DocEntry;
                itemDto.DocType = EkbConsts.LocalVesselType.Local;

                var itemEntity = _zoneDetailMapper.CreateEntityWithId(itemDto, Guid.NewGuid());
                // Set CreatedBy from authenticated user on detail
                itemEntity.CreatedBy = CurrentUser.UserName ?? "System";
                // Ensure required fields are set
                itemEntity.IsUrgent = string.IsNullOrEmpty(itemEntity.IsUrgent) ? "N" : itemEntity.IsUrgent;
                itemEntity.IsScan = string.IsNullOrEmpty(itemEntity.IsScan) ? "N" : itemEntity.IsScan;
                itemEntity.IsOriginal = string.IsNullOrEmpty(itemEntity.IsOriginal) ? "N" : itemEntity.IsOriginal;
                itemEntity.IsSend = string.IsNullOrEmpty(itemEntity.IsSend) ? "N" : itemEntity.IsSend;
                itemEntity.IsFeOri = string.IsNullOrEmpty(itemEntity.IsFeOri) ? "N" : itemEntity.IsFeOri;
                itemEntity.IsFeSend = string.IsNullOrEmpty(itemEntity.IsFeSend) ? "N" : itemEntity.IsFeSend;
                itemEntity.IsChange = string.IsNullOrEmpty(itemEntity.IsChange) ? "N" : itemEntity.IsChange;
                itemEntity.Deleted = string.IsNullOrEmpty(itemEntity.Deleted) ? "N" : itemEntity.Deleted;
                itemEntity.DocType = string.IsNullOrEmpty(itemEntity.DocType) ? "Local" : itemEntity.DocType;
                await _zoneDetailRepository.InsertAsync(itemEntity, autoSave: false);
            }
        }

        // Save all changes in a single transaction
        await CurrentUnitOfWork.SaveChangesAsync();

        // Load the entity with includes for proper mapping
        var createdEntity = await _localVesselRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(createdEntity);
    }

    public override async Task<LocalVesselDto> UpdateAsync(Guid id, CreateUpdateLocalVesselDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _localVesselRepository.GetAsync(id);
        _localVesselMapper.MapToEntity(input, entity);

        await _localVesselRepository.UpdateAsync(entity, autoSave: false);


        // Handle items update - update existing items and add new ones
        if (input.Items != null)
        {
            // Get existing items
            var existingItems = await _zoneDetailRepository.GetByHeaderIdAsync(id);

            // Process each item in the input
            for (int i = 0; i < input.Items.Count; i++)
            {
                var itemDto = input.Items[i];
                itemDto.HeaderId = id;

                if (i < existingItems.Count)
                {
                    // Update existing item
                    var existingItem = existingItems[i];
                    _zoneDetailMapper.MapToEntity(itemDto, existingItem);
                    await _zoneDetailRepository.UpdateAsync(existingItem, autoSave: false);
                }
                else
                {
                    // If HeaderId is Guid, assign entity.Id
                    itemDto.HeaderId = entity.Id;
                    itemDto.DocNum = entity.DocEntry;
                    itemDto.DocType = EkbConsts.LocalVesselType.Local;

                    var itemEntity = _zoneDetailMapper.CreateEntityWithId(itemDto, Guid.NewGuid());
                    // Set CreatedBy from authenticated user on detail
                    itemEntity.CreatedBy = CurrentUser.UserName ?? "System";
                    // Ensure required fields are set
                    itemEntity.IsUrgent = string.IsNullOrEmpty(itemEntity.IsUrgent) ? "N" : itemEntity.IsUrgent;
                    itemEntity.IsScan = string.IsNullOrEmpty(itemEntity.IsScan) ? "N" : itemEntity.IsScan;
                    itemEntity.IsOriginal = string.IsNullOrEmpty(itemEntity.IsOriginal) ? "N" : itemEntity.IsOriginal;
                    itemEntity.IsSend = string.IsNullOrEmpty(itemEntity.IsSend) ? "N" : itemEntity.IsSend;
                    itemEntity.IsFeOri = string.IsNullOrEmpty(itemEntity.IsFeOri) ? "N" : itemEntity.IsFeOri;
                    itemEntity.IsFeSend = string.IsNullOrEmpty(itemEntity.IsFeSend) ? "N" : itemEntity.IsFeSend;
                    itemEntity.IsChange = string.IsNullOrEmpty(itemEntity.IsChange) ? "N" : itemEntity.IsChange;
                    itemEntity.Deleted = string.IsNullOrEmpty(itemEntity.Deleted) ? "N" : itemEntity.Deleted;
                    itemEntity.DocType = string.IsNullOrEmpty(itemEntity.DocType) ? "Local" : itemEntity.DocType;
                    await _zoneDetailRepository.InsertAsync(itemEntity, autoSave: false);
                }
            }

            // Remove excess existing items if input has fewer items
            for (int i = input.Items.Count; i < existingItems.Count; i++)
            {
                await _zoneDetailRepository.DeleteAsync(existingItems[i], autoSave: false);
            }
        }

        // Save all changes in a single transaction
        await CurrentUnitOfWork.SaveChangesAsync();

        // Load the entity with includes for proper mapping
        var updatedEntity = await _localVesselRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(updatedEntity);
    }



    public override async Task<LocalVesselDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _localVesselRepository.GetQueryableWithItemsSplitAsync(id);

        if (entity == null)
        {
            throw new UserFriendlyException("LocalVessel not found");
        }

        // Map the items from the navigation property
        var items = entity.Items?.Select(zoneDetail =>
        {
            var itemDto = _vesselMapper.MapZoneDetailToItemDtoWithType(zoneDetail, "LocalVessel");

            // Use navigation property for Tenant
            if (zoneDetail.Tenant != null)
            {
                itemDto.Tenant = _vesselMapper.MapTenantToTenantShortDto(zoneDetail.Tenant);
            }

            // Map DocAttachments if available
            if (zoneDetail.DocAttachment != null && zoneDetail.DocAttachment.Any())
            {
                itemDto.Attachments = zoneDetail.DocAttachment.Select(att => _vesselMapper.MapDocAttachmentToDto(att)).ToList();
            }
            else
            {
                itemDto.Attachments = new List<DocAttachmentSortDto>();
            }

            return itemDto;
        }).ToList() ?? new List<VesselItemDto>();

        return _localVesselMapper.MapToDtoWithItems(entity, items);
    }

    protected override LocalVesselDto MapToGetOutputDto(LocalVesselEntity entity)
    {
        return _localVesselMapper.MapToDto(entity);
    }

    protected override LocalVesselDto MapToGetListOutputDto(LocalVesselEntity entity)
    {
        return _localVesselMapper.MapToDto(entity);
    }

    protected override LocalVesselEntity MapToEntity(CreateUpdateLocalVesselDto createInput)
    {
        return _localVesselMapper.MapToEntity(createInput);
    }

    protected override void MapToEntity(CreateUpdateLocalVesselDto updateInput, LocalVesselEntity entity)
    {
        _localVesselMapper.MapToEntity(updateInput, entity);
    }

    private static string GetFileStreamUrl(string? path, string baseUrl)
    {
        // Return the URL
        return $"{baseUrl}/api/filestream/stream/ekb/{path}";
    }

    public async Task<LocalVesselWithItemsDto> GetWithItemsAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var vesselWithItems = await _localVesselRepository.GetProjectedByIdAsync(id, x => new LocalVesselWithItemsDto
        {
            Id = x.Id,
            ConcurrencyStamp = x.ConcurrencyStamp,
            DocEntry = x.DocEntry,
            DocNum = x.DocNum,
            PostingDate = x.PostingDate,
            VesselType = x.VesselType,
            VesselArrival = x.VesselArrival,
            VesselDeparture = x.VesselDeparture,
            Shipment = x.Shipment,
            VesselQty = x.VesselQty,
            PortOrigin = x.PortOrigin,
            DestinationPort = x.DestinationPort,
            TransType = x.TransType,
            DocType = x.DocType,
            CreatedBy = x.CreatedBy,
            UpdatedBy = x.UpdatedBy,
            Voyage = x.Voyage,
            GrossWeight = x.GrossWeight,
            DocStatus = x.DocStatus,
            BerthingDate = x.BerthingDate,
            AnchorageDate = x.AnchorageDate,
            ReportDate = x.ReportDate,
            UnloadingDate = x.UnloadingDate,
            FinishUnloadingDate = x.FinishUnloadingDate,
            GrtWeight = x.GrtWeight,
            GrtVessel = x.GrtVessel,
            JettyId = x.JettyId,
            VesselId = x.VesselId,
            BargeId = x.BargeId,
            MasterAgentId = x.MasterAgentId,
            MasterTradingId = x.MasterTradingId,
            MasterSurveyorId = x.MasterSurveyorId,
            AsideDate = x.AsideDate,
            CastOfDate = x.CastOfDate,
            PortOriginId = x.PortOriginId,
            DestinationPortId = x.DestinationPortId,
            MasterAgent = x.MasterAgent == null ? null : new AgentDto
            {
                Id = x.MasterAgent.Id,
                Name = x.MasterAgent.Name,
                Status = x.MasterAgent.Status,
                Type = x.MasterAgent.Type,
                NpwpNo = x.MasterAgent.NpwpNo,
                BdmSapcode = x.MasterAgent.BdmSapcode,
                TaxCode = x.MasterAgent.TaxCode,
                AddressNpwp = x.MasterAgent.AddressNpwp,
                Address = x.MasterAgent.Address,
                SapcodeS4 = x.MasterAgent.SapcodeS4,
            },
            MasterTrading = x.MasterTrading == null ? null : new TradingDto
            {
                Id = x.MasterTrading.Id,
                Name = x.MasterTrading.Name,
                Address = x.MasterTrading.Address,
                Npwp = x.MasterTrading.Npwp,
            },
            MasterSurveyor = x.MasterSurveyor == null ? null : new SurveyorDto
            {
                Id = x.MasterSurveyor.Id,
                Name = x.MasterSurveyor.Name,
                Address = x.MasterSurveyor.Address,
                Npwp = x.MasterSurveyor.Npwp,
            },
            MasterJetty = x.MasterJetty == null ? null : new JettyDto
            {
                Id = x.MasterJetty.Id,
                Name = x.MasterJetty.Name,
                Alias = x.MasterJetty.Alias,
                Max = x.MasterJetty.Max,
                Port = x.MasterJetty.Port,
                IsCustomArea = x.MasterJetty.IsCustomArea,
            },
            Vessel = x.Vessel == null ? null : new CargoDto
            {
                Id = x.Vessel.Id,
                Name = x.Vessel.Name,
                Alias = x.Vessel.Alias,
                Flag = x.Vessel.Flag,
                GrossWeight = x.Vessel.GrossWeight,
                Type = x.Vessel.Type,
                LoaQty = x.Vessel.LoaQty,
            },
            Barge = x.Barge == null ? null : new CargoDto
            {
                Id = x.Barge.Id,
                Name = x.Barge.Name,
                Alias = x.Barge.Alias,
                Flag = x.Barge.Flag,
                GrossWeight = x.Barge.GrossWeight,
                Type = x.Barge.Type,
                LoaQty = x.Barge.LoaQty,
            },
            MasterPortOrigin = x.MasterPortOrigin == null ? null : new PortOfLoadingDto
            {
                Id = x.MasterPortOrigin.Id,
                Name = x.MasterPortOrigin.Name,
                DocType = x.MasterPortOrigin.DocType,
            },
            MasterDestinationPort = x.MasterDestinationPort == null ? null : new DestinationPortDto
            {
                Id = x.MasterDestinationPort.Id,
                Name = x.MasterDestinationPort.Name,
                DocType = x.MasterDestinationPort.DocType,
            },
            Items = x.Items.Select(zoneDetail => new VesselItemDto
            {
                Id = zoneDetail.Id,
                ConcurrencyStamp = zoneDetail.ConcurrencyStamp,
                DocEntry = zoneDetail.DocEntry,
                DocNum = zoneDetail.DocNum ?? 0,
                TenantName = zoneDetail.Tenant != null ? zoneDetail.Tenant.Name : null,
                ItemName = zoneDetail.ItemName,
                ItemQty = zoneDetail.ItemQty,
                UnitQty = zoneDetail.UnitQty,
                Cargo = zoneDetail.Cargo,
                Shipment = zoneDetail.Shipment,
                Remarks = zoneDetail.Remarks,
                NoBl = zoneDetail.NoBl,
                DateBl = zoneDetail.DateBl,
                AjuNo = zoneDetail.AjuNo,
                RegNo = zoneDetail.RegNo,
                RegDate = zoneDetail.RegDate,
                SppbNo = zoneDetail.SppbNo,
                SppbDate = zoneDetail.SppbDate,
                SppdNo = zoneDetail.SppdNo,
                SppdDate = zoneDetail.SppdDate,
                GrossWeight = zoneDetail.GrossWeight,
                UnitWeight = zoneDetail.UnitWeight,
                HeaderId = zoneDetail.HeaderId,
                LetterNo = zoneDetail.LetterNo,
                DocType = zoneDetail.DocType,
                VesselType = "LocalVessel",
                ShippingInstructionNo = zoneDetail.ShippingInstructionNo,
                ShippingInstructionDate = zoneDetail.ShippingInstructionDate,
                RegType = zoneDetail.RegType,
                Status = zoneDetail.Status,
                LetterDate = zoneDetail.LetterDate,
                TenantId = zoneDetail.TenantId,
                BcTypeId = zoneDetail.BcTypeId,
                BusinessPartnerId = zoneDetail.BusinessPartnerId,
                AgentId = zoneDetail.AgentId,
                MasterExportClassificationId = zoneDetail.MasterExportClassificationId,
                Item = zoneDetail.Item,
                Tenant = zoneDetail.Tenant == null ? null : new TenantShortDto
                {
                    Id = zoneDetail.Tenant.Id,
                    Name = zoneDetail.Tenant.Name,
                    FullName = zoneDetail.Tenant.FullName,
                    DocEntry = zoneDetail.Tenant.DocEntry,
                    Npwp = zoneDetail.Tenant.Npwp,
                    Address = zoneDetail.Tenant.Address,
                    Nib = zoneDetail.Tenant.Nib,
                    Phone = zoneDetail.Tenant.Phone,
                    NoAndDateNotaris = zoneDetail.Tenant.NoAndDateNotaris,
                    DescNotaris = zoneDetail.Tenant.DescNotaris,
                },
                BcType = zoneDetail.BcType == null ? null : new BcTypeShortDto
                {
                    Id = zoneDetail.BcType.Id,
                    Type = zoneDetail.BcType.Type,
                    TransName = zoneDetail.BcType.TransName,
                },
                BusinessPartner = zoneDetail.BusinessPartner == null ? null : new BusinessPartnerShortDto
                {
                    Id = zoneDetail.BusinessPartner.Id,
                    Name = zoneDetail.BusinessPartner.Name,
                    Npwp = zoneDetail.BusinessPartner.Npwp,
                    Nitku = zoneDetail.BusinessPartner.Nitku,
                    DocEntry = zoneDetail.BusinessPartner.DocEntry
                },
                MasterAgent = zoneDetail.MasterAgent == null ? null : new AgentShortDto
                {
                    Id = zoneDetail.MasterAgent.Id,
                    Name = zoneDetail.MasterAgent.Name,
                    Type = zoneDetail.MasterAgent.Type,
                },
                Attachments = zoneDetail.DocAttachment != null ? zoneDetail.DocAttachment.Select(att => new DocAttachmentSortDto
                {
                    Id = att.Id,
                    DocType = att.DocType,
                    TransType = att.TransType,
                    Description = att.Description,
                    BlobName = att.BlobName,
                    ReferenceId = att.ReferenceId,
                    FileName = att.FileName,
                    TabName = att.TabName,
                    StreamUrl = att.FilePath, // Set FilePath for now, will map to URL after query
                    TypePa = att.TypePa
                }).ToList() : new List<DocAttachmentSortDto>()
            }).ToList()
        });

        if (vesselWithItems == null)
        {
            throw new UserFriendlyException("LocalVessel not found");
        }

        return vesselWithItems;
    }

    public async Task<PagedResultDto<LocalVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters)
    {
        await CheckGetListPolicyAsync();

        var entityQuery = await _localVesselRepository.GetQueryableWithIncludesAsync();
        entityQuery = ApplyDynamicQuery(entityQuery, parameters);

        // Define the projection selector
        System.Linq.Expressions.Expression<Func<LocalVesselEntity, LocalVesselProjectionDto>> selector = x => new LocalVesselProjectionDto
        {
            Id = x.Id,
            ConcurrencyStamp = x.ConcurrencyStamp,
            DocEntry = x.DocEntry,
            DocNum = x.DocNum,
            PostingDate = x.PostingDate,
            VesselType = x.VesselType,
            VesselArrival = x.VesselArrival,
            VesselDeparture = x.VesselDeparture,
            Shipment = x.Shipment,
            VesselQty = x.VesselQty,
            PortOrigin = x.PortOrigin,
            DestinationPort = x.DestinationPort,
            TransType = x.TransType,
            DocType = x.DocType,
            CreatedBy = x.CreatedBy,
            UpdatedBy = x.UpdatedBy,
            Voyage = x.Voyage,
            GrossWeight = x.GrossWeight,
            DocStatus = x.DocStatus,
            BerthingDate = x.BerthingDate,
            AnchorageDate = x.AnchorageDate,
            ReportDate = x.ReportDate,
            UnloadingDate = x.UnloadingDate,
            FinishUnloadingDate = x.FinishUnloadingDate,
            GrtWeight = x.GrtWeight,
            GrtVessel = x.GrtVessel,
            JettyId = x.JettyId,
            VesselId = x.VesselId,
            BargeId = x.BargeId,
            MasterAgentId = x.MasterAgentId,
            MasterTradingId = x.MasterTradingId,
            MasterSurveyorId = x.MasterSurveyorId,
            AsideDate = x.AsideDate,
            CastOfDate = x.CastOfDate,
            PortOriginId = x.PortOriginId,
            DestinationPortId = x.DestinationPortId,
            MasterAgent = x.MasterAgent == null ? null : new AgentProjectionDto
            {
                Id = x.MasterAgent.Id,
                Name = x.MasterAgent.Name,
                Status = x.MasterAgent.Status,
                Type = x.MasterAgent.Type,
                NpwpNo = x.MasterAgent.NpwpNo,
                BdmSapcode = x.MasterAgent.BdmSapcode,
                TaxCode = x.MasterAgent.TaxCode,
                AddressNpwp = x.MasterAgent.AddressNpwp,
                Address = x.MasterAgent.Address,
                SapcodeS4 = x.MasterAgent.SapcodeS4,
            },
            MasterTrading = x.MasterTrading == null ? null : new TradingProjectionDto
            {
                Id = x.MasterTrading.Id,
                Name = x.MasterTrading.Name,
                Address = x.MasterTrading.Address,
                Npwp = x.MasterTrading.Npwp,
            },
            MasterSurveyor = x.MasterSurveyor == null ? null : new SurveyorProjectionDto
            {
                Id = x.MasterSurveyor.Id,
                Name = x.MasterSurveyor.Name,
                Address = x.MasterSurveyor.Address,
                Npwp = x.MasterSurveyor.Npwp,
            },
            MasterJetty = x.MasterJetty == null ? null : new JettyProjectionDto
            {
                Id = x.MasterJetty.Id,
                Name = x.MasterJetty.Name,
                Alias = x.MasterJetty.Alias,
                Max = x.MasterJetty.Max,
                Port = x.MasterJetty.Port,
                IsCustomArea = x.MasterJetty.IsCustomArea,
            },
            Vessel = x.Vessel == null ? null : new CargoProjectionDto
            {
                Id = x.Vessel.Id,
                Name = x.Vessel.Name,
                Alias = x.Vessel.Alias,
                Flag = x.Vessel.Flag,
                GrossWeight = x.Vessel.GrossWeight,
                Type = x.Vessel.Type,
                LoaQty = x.Vessel.LoaQty,
            },
            Barge = x.Barge == null ? null : new CargoProjectionDto
            {
                Id = x.Barge.Id,
                Name = x.Barge.Name,
                Alias = x.Barge.Alias,
                Flag = x.Barge.Flag,
                GrossWeight = x.Barge.GrossWeight,
                Type = x.Barge.Type,
                LoaQty = x.Barge.LoaQty,
            },
            MasterPortOrigin = x.MasterPortOrigin == null ? null : new PortOfOriginProjectionDto
            {
                Id = x.MasterPortOrigin.Id,
                Name = x.MasterPortOrigin.Name,
                DocType = x.MasterPortOrigin.DocType,
            },
            MasterDestinationPort = x.MasterDestinationPort == null ? null : new DestinationPortProjectionDto
            {
                Id = x.MasterDestinationPort.Id,
                Name = x.MasterDestinationPort.Name,
                DocType = x.MasterDestinationPort.DocType,
            },
        };

        var projectedQuery = entityQuery.Select(selector);

        var totalCount = await AsyncExecuter.CountAsync(projectedQuery);
        var items = await AsyncExecuter.ToListAsync(
            projectedQuery.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        return new PagedResultDto<LocalVesselProjectionDto>
        {
            TotalCount = totalCount,
            Items = items
        };
    }
}