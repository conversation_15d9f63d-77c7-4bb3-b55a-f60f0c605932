using Imip.Ekb.BoundedZone.TradingVessels.TradingInvoice;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Mapping.Mappers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Application.BoundedZone.TradingVessel;

[Authorize]
public class TradingInvoiceAppService :
    CrudAppService<
        TradingInvoice,
        TradingInvoiceDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateTradingInvoiceDto>
{
    private readonly ITradingInvoiceRepository _tradingInvoiceRepository;
    private readonly TradingInvoiceMapper _tradingInvoiceMapper;
    private readonly ILogger<TradingInvoiceAppService> _logger;

    public TradingInvoiceAppService(
        ITradingInvoiceRepository tradingInvoiceRepository,
        TradingInvoiceMapper tradingInvoiceMapper,
        ILogger<TradingInvoiceAppService> logger)
        : base(tradingInvoiceRepository)
    {
        _tradingInvoiceRepository = tradingInvoiceRepository;
        _tradingInvoiceMapper = tradingInvoiceMapper;
        _logger = logger;
    }

    public override async Task<PagedResultDto<TradingInvoiceDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();
        var query = await _tradingInvoiceRepository.GetQueryableWithIncludesAsync();
        if (!string.IsNullOrWhiteSpace(input.Sorting))
            query = query.OrderBy(input.Sorting);
        else
            query = query.OrderByDescending(x => x.CreationTime);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var entities = await AsyncExecuter.ToListAsync(query.PageBy(input.SkipCount, input.MaxResultCount));
        var dtos = entities.Select(MapToGetListOutputDto).ToList();
        return new PagedResultDto<TradingInvoiceDto>(totalCount, dtos);
    }

    protected override TradingInvoiceDto MapToGetOutputDto(TradingInvoice entity)
        => _tradingInvoiceMapper.MapToDto(entity);

    protected override TradingInvoiceDto MapToGetListOutputDto(TradingInvoice entity)
        => _tradingInvoiceMapper.MapToDto(entity);

    protected override TradingInvoice MapToEntity(CreateUpdateTradingInvoiceDto createInput)
        => _tradingInvoiceMapper.MapToEntity(createInput);

    protected override void MapToEntity(CreateUpdateTradingInvoiceDto updateInput, TradingInvoice entity)
        => _tradingInvoiceMapper.MapToEntity(updateInput, entity);
}