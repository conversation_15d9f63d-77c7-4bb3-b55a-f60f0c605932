using Imip.Ekb.Attachments;
using Imip.Ekb.Application.Contracts.Attachments;
using Riok.Mapperly.Abstractions;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class DocAttachmentMapper : IMapperlyMapper
{
    // DocAttachment to DocAttachmentDto mapping
    [MapProperty(nameof(DocAttachment.Id), nameof(DocAttachmentDto.Id))]
    [MapProperty(nameof(DocAttachment.DocEntry), nameof(DocAttachmentDto.DocEntry))]
    [MapProperty(nameof(DocAttachment.FileName), nameof(DocAttachmentDto.FileName))]
    [MapProperty(nameof(DocAttachment.ContentType), nameof(DocAttachmentDto.ContentType))]
    [MapProperty(nameof(DocAttachment.CreatedBy), nameof(DocAttachmentDto.CreatedBy))]
    [MapProperty(nameof(DocAttachment.CreatedAt), nameof(DocAttachmentDto.CreatedAt))]
    [MapProperty(nameof(DocAttachment.UpdatedAt), nameof(DocAttachmentDto.UpdatedAt))]
    [MapProperty(nameof(DocAttachment.DocumentReferenceId), nameof(DocAttachmentDto.DocumentReferenceId))]
    [MapProperty(nameof(DocAttachment.ReferenceId), nameof(DocAttachmentDto.ReferenceId))]
    [MapProperty(nameof(DocAttachment.BlobName), nameof(DocAttachmentDto.BlobName))]
    [MapProperty(nameof(DocAttachment.Description), nameof(DocAttachmentDto.Description))]
    [MapProperty(nameof(DocAttachment.DocType), nameof(DocAttachmentDto.DocType))]
    [MapProperty(nameof(DocAttachment.TransType), nameof(DocAttachmentDto.TransType))]
    [MapProperty(nameof(DocAttachment.SecretKey), nameof(DocAttachmentDto.SecretKey))]
    [MapProperty(nameof(DocAttachment.TypePa), nameof(DocAttachmentDto.TypePa))]
    [MapProperty(nameof(DocAttachment.FilePath), nameof(DocAttachmentDto.FilePath))]
    [MapProperty(nameof(DocAttachment.NameNoExt), nameof(DocAttachmentDto.NameNoExt))]
    [MapProperty(nameof(DocAttachment.Size), nameof(DocAttachmentDto.Size))]
    [MapProperty(nameof(DocAttachment.Extension), nameof(DocAttachmentDto.Extension))]
    public partial DocAttachmentDto MapToDto(DocAttachment entity);

    // CreateUpdateDocAttachmentDto to DocAttachment mapping
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.FileName), nameof(DocAttachment.FileName))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.ContentType), nameof(DocAttachment.ContentType))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.CreatedBy), nameof(DocAttachment.CreatedBy))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.DocumentReferenceId), nameof(DocAttachment.DocumentReferenceId))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.ReferenceId), nameof(DocAttachment.ReferenceId))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.BlobName), nameof(DocAttachment.BlobName))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.Description), nameof(DocAttachment.Description))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.DocType), nameof(DocAttachment.DocType))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.TransType), nameof(DocAttachment.TransType))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.SecretKey), nameof(DocAttachment.SecretKey))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.TypePa), nameof(DocAttachment.TypePa))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.FilePath), nameof(DocAttachment.FilePath))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.NameNoExt), nameof(DocAttachment.NameNoExt))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.Size), nameof(DocAttachment.Size))]
    [MapProperty(nameof(CreateUpdateDocAttachmentDto.Extension), nameof(DocAttachment.Extension))]
    public partial DocAttachment MapToEntity(CreateUpdateDocAttachmentDto dto);

    // List mappings
    public partial List<DocAttachmentDto> MapToList(List<DocAttachment> entities);
}