
using Imip.Ekb.Master.BusinessPartners;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbBusinessPartnerConfiguration : IEntityTypeConfiguration<BusinessPartner>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbBusinessPartnerConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<BusinessPartner> b)
    {
        b.ToTable("M_BP", schema);
        b.ConfigureByConvention(); // Auto configure conventions

        b.HasMany(x => x.VesselTransactions)
            .WithOne(x => x.BusinessPartner)
            .HasForeignKey(x => x.BusinessPartnerId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);
    }
}