﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.PriceListAlls;

[Table("MPLAT")]
public class PriceListAll : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public long DocEntry { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal Price { get; set; }

    [Column("PeriodeStart")]
    public DateOnly PeriodStart { get; set; }

    [Column("PeriodeEnd")]
    public DateOnly PeriodEnd { get; set; }

    public long CreatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }
}
