using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.BoundedZone.Dtos;

public class CreateUpdateVesselItemDto : EntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public int? DocEntry { get; set; }
    public int? DocNum { get; set; }
    public string? TenantName { get; set; }
    public string? ItemName { get; set; }
    public decimal? ItemQty { get; set; }
    public string? UnitQty { get; set; }
    public string? Cargo { get; set; }
    public string? Shipment { get; set; }
    public string? Remarks { get; set; }
    public string? NoBl { get; set; }
    public DateOnly? DateBl { get; set; }
    public string? AjuNo { get; set; }
    public string? RegNo { get; set; }
    public DateOnly? RegDate { get; set; }
    public string? SppbNo { get; set; }
    public DateOnly? SppbDate { get; set; }
    public string? SppdNo { get; set; }
    public DateOnly? SppdDate { get; set; }
    public decimal? GrossWeight { get; set; }
    public string? UnitWeight { get; set; }
    public Guid? HeaderId { get; set; }
    public string? LetterNo { get; set; }
    public string? DocType { get; set; }
    public string VesselType { get; set; } = string.Empty; // "Import", "Export", "LocalIn", "LocalOut"
    public string? ShippingInstructionNo { get; set; }
    public string? RegType { get; set; }
    public string? Status { get; set; }
    public DateOnly? ShippingInstructionDate { get; set; }
    public DateOnly? LetterDate { get; set; }
    [Required]
    public Guid? TenantId { get; set; }
    public Guid? BcTypeId { get; set; }
    public Guid? BusinessPartnerId { get; set; }
    public Guid? AgentId { get; set; }
    public Guid? MasterExportClassificationId { get; set; }
}
