using System;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.ImportVessels.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.BoundedZone.ImportVessel;

public interface IImportVesselCustomAppService
{
    Task<PagedResultDto<ImportVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters);
    Task<ImportVesselWithItemsDto> GetWithItemsAsync(Guid id);
}