﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class Added_DocAttachment : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "BcTypeId",
                table: "T_MDOC",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "HeaderId",
                table: "T_MDOC",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TenantId",
                table: "T_MDOC",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TenantGroupId",
                table: "M_Tenant",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddUniqueConstraint(
                name: "AK_THEXP_Id",
                table: "THEXP",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_T_MDOC_Header_Id",
                table: "T_MDOC_Header",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_T_MDOC_Id",
                table: "T_MDOC",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_M_Tenant_Id",
                table: "M_Tenant",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_M_TBC_Id",
                table: "M_TBC",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_L_master_Id",
                table: "L_master",
                column: "Id");

            migrationBuilder.AddColumn<Guid>(
                name: "ReferenceId",
                table: "M_Doc_attachment",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BlobName",
                table: "M_Doc_attachment",
                type: "nvarchar(200)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "M_Doc_attachment",
                type: "nvarchar(200)",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "HeaderId",
                table: "BEXP",
                type: "uniqueidentifier",
                nullable: true);
            migrationBuilder.AddColumn<Guid>(
                name: "ZoneDetailId",
                table: "BEXP",
                type: "uniqueidentifier",
                nullable: true);
            migrationBuilder.AddColumn<Guid>(
                name: "MasterTenantId",
                table: "BEXP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "BcTypeId",
                table: "BEXP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddForeignKey(
                table: "BEXP",
                name: "FK_BEXP_M_TBC_BcTypeId",
                column: "BcTypeId",
                principalTable: "M_TBC",
                principalColumn: "Id");
            migrationBuilder.AddForeignKey(
                table: "BEXP",
                name: "FK_BEXP_M_Tenant_MasterTenantId",
                column: "MasterTenantId",
                principalTable: "M_Tenant",
                principalColumn: "Id");
            migrationBuilder.AddForeignKey(
                table: "BEXP",
                name: "FK_BEXP_T_MDOC_ZoneDetailId",
                column: "ZoneDetailId",
                principalTable: "T_MDOC",
                principalColumn: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_BC_type_key",
                table: "T_MDOC",
                column: "BC_type_key");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_BcTypeId",
                table: "T_MDOC",
                column: "BcTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_DocNum",
                table: "T_MDOC",
                column: "DocNum");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_DocType",
                table: "T_MDOC",
                column: "DocType");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_HeaderId",
                table: "T_MDOC",
                column: "HeaderId");


            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_Tenant_key",
                table: "T_MDOC",
                column: "Tenant_key");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_TenantId",
                table: "T_MDOC",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_M_Tenant_Name",
                table: "M_Tenant",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_M_Tenant_TenantGroupId",
                table: "M_Tenant",
                column: "TenantGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_BEXP_BcTypeId",
                table: "BEXP",
                column: "BcTypeId");


            migrationBuilder.CreateIndex(
                name: "IX_BEXP_HeaderId",
                table: "BEXP",
                column: "HeaderId");

            migrationBuilder.CreateIndex(
                name: "IX_BEXP_MasterTenantId",
                table: "BEXP",
                column: "MasterTenantId");

            migrationBuilder.CreateIndex(
                name: "IX_BEXP_ZoneDetailId",
                table: "BEXP",
                column: "ZoneDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_M_Doc_attachment_DocType",
                table: "M_Doc_attachment",
                column: "DocType");

            migrationBuilder.CreateIndex(
                name: "IX_M_Doc_attachment_M_doc_key",
                table: "M_Doc_attachment",
                column: "M_doc_key");

            migrationBuilder.CreateIndex(
                name: "IX_M_Doc_attachment_ReferenceId",
                table: "M_Doc_attachment",
                column: "ReferenceId");

            migrationBuilder.CreateIndex(
                name: "IX_M_Doc_attachment_TransType",
                table: "M_Doc_attachment",
                column: "TransType");

            migrationBuilder.AddForeignKey(
                name: "FK_M_Tenant_MasterGroup_TenantGroupId",
                table: "M_Tenant",
                column: "TenantGroupId",
                principalTable: "MasterGroup",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_L_master_HeaderId",
                table: "T_MDOC",
                column: "HeaderId",
                principalTable: "L_master",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_M_TBC_BcTypeId",
                table: "T_MDOC",
                column: "BcTypeId",
                principalTable: "M_TBC",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_M_Tenant_TenantId",
                table: "T_MDOC",
                column: "TenantId",
                principalTable: "M_Tenant",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_R_Master_HeaderId",
                table: "T_MDOC",
                column: "HeaderId",
                principalTable: "R_Master",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_THEXP_HeaderId",
                table: "T_MDOC",
                column: "HeaderId",
                principalTable: "THEXP",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_T_MDOC_Header_HeaderId",
                table: "T_MDOC",
                column: "HeaderId",
                principalTable: "T_MDOC_Header",
                principalColumn: "Id");

            // migrationBuilder.CreateIndex(
            //     name: "IX_T_MDOC_BC_type_key",
            //     table: "T_MDOC",
            //     column: "BC_type_key");

            // migrationBuilder.CreateIndex(
            //     name: "IX_T_MDOC_Tenant_key",
            //     table: "T_MDOC",
            //     column: "Tenant_key");

            // Data migration queries (split for performance and easier debugging)

        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_M_Tenant_MasterGroup_TenantGroupId",
                table: "M_Tenant");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_L_master_HeaderId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_M_TBC_BcTypeId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_M_Tenant_TenantId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_R_Master_HeaderId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_THEXP_HeaderId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_T_MDOC_Header_HeaderId",
                table: "T_MDOC");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_THEXP_Id",
                table: "THEXP");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_T_MDOC_Header_Id",
                table: "T_MDOC_Header");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_T_MDOC_Id",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_BC_type_key",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_BcTypeId",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_DocNum",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_DocType",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_HeaderId",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_Tenant_key",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_TenantId",
                table: "T_MDOC");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_M_Tenant_Id",
                table: "M_Tenant");

            migrationBuilder.DropIndex(
                name: "IX_M_Tenant_Name",
                table: "M_Tenant");

            migrationBuilder.DropIndex(
                name: "IX_M_Tenant_TenantGroupId",
                table: "M_Tenant");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_M_TBC_Id",
                table: "M_TBC");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_L_master_Id",
                table: "L_master");

            migrationBuilder.DropColumn(
                name: "BcTypeId",
                table: "T_MDOC");

            migrationBuilder.DropColumn(
                name: "HeaderId",
                table: "T_MDOC");


            migrationBuilder.DropColumn(
                name: "TenantId",
                table: "T_MDOC");


            migrationBuilder.DropColumn(
                name: "TenantGroupId",
                table: "M_Tenant");
        }
    }
}
