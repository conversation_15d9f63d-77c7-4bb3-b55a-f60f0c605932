using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.Notuls;
using Imip.Ekb.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.Ekb.Repositories;

public class NotulRepository : EfCoreRepository<EkbDbContext, Notul, Guid>, INotulRepository
{
    public NotulRepository(IDbContextProvider<EkbDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }
    // Simple repository methods only
    public virtual async Task<IQueryable<Notul>> GetQueryableWithIncludesAsync()
    {
        return (await GetQueryableAsync()).AsNoTracking();
    }
}