using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Imip.Ekb.Billing.LocalVesselBillings.Dtos;

namespace Imip.Ekb.Billing.LocalVesselBillings.Dtos;

public class CreateUpdateExportVesselBillingDto : EntityDto<Guid>
{
    [StringLength(255)]
    public string PortService { get; set; } = null!;

    [StringLength(255)]
    public string Status { get; set; } = null!;

    [StringLength(50)]
    public string YearArrival { get; set; } = null!;

    public int Jetty { get; set; }

    public int ExportId { get; set; }

    public int UpdatedBy { get; set; }

    [StringLength(255)]
    public string? DocNum { get; set; }

    public DateOnly? PostingDate { get; set; }

    public string? Remarks { get; set; }

    [StringLength(100)]
    public string Type { get; set; } = null!;

    public DateOnly? PeriodDate { get; set; }

    public decimal? DeadWeight { get; set; }

    public DateOnly? BillingNoteDate { get; set; }

    public Guid? JettyId { get; set; }

    // Items for create/update
    public List<BillingItemDto>? Items { get; set; }
}