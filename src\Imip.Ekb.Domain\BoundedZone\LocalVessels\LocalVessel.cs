﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.Master.Agents;
using Imip.Ekb.Master.Cargos;
using Imip.Ekb.Master.DestinationPorts;
using Imip.Ekb.Master.Jetties;
using Imip.Ekb.Master.PortOfLoadings;
using Imip.Ekb.Master.Surveyors;
using Imip.Ekb.Master.Tradings;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.BoundedZone.LocalVessels;

[Table("L_master")]
public class LocalVessel : FullAuditedAggregateRoot<Guid>
{
    [Key]
    [Column("DocEntry")]
    public int DocEntry { get; set; }

    [StringLength(50)]
    public string DocNum { get; set; } = null!;

    public DateOnly PostingDate { get; set; }

    [StringLength(20)]
    public string VesselType { get; set; } = null!;

    public long VesselName { get; set; }

    [Column("tongkang")]
    public int? Tongkang { get; set; }

    public DateTime? VesselArrival { get; set; }

    public DateTime? VesselDeparture { get; set; }

    [StringLength(100)]
    public string Shipment { get; set; } = null!;

    [Column(TypeName = "decimal(20, 4)")]
    public decimal VesselQty { get; set; }

    [StringLength(200)]
    public string PortOrigin { get; set; } = null!;

    [StringLength(200)]
    public string DestinationPort { get; set; } = null!;

    public string? Remark { get; set; }

    [StringLength(5)]
    public string Deleted { get; set; } = null!;

    [StringLength(5)]
    public string TransType { get; set; } = null!;

    [StringLength(20)]
    public string DocType { get; set; } = null!;

    public long CreatedBy { get; set; }

    public long UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [Column("voyage")]
    [StringLength(40)]
    public string? Voyage { get; set; }

    [Column(TypeName = "numeric(20, 4)")]
    public decimal? GrossWeight { get; set; }

    [StringLength(10)]
    public string? DocStatus { get; set; }

    [Column("Jetty")]
    public long? Jetty { get; set; }

    [StringLength(100)]
    public string? Status { get; set; }

    [Column(TypeName = "numeric(20, 4)")]
    public decimal? BeratTugboat { get; set; }

    [Column("TglSandar", TypeName = "datetime")]
    public DateTime? BerthingDate { get; set; }

    [Column("TglLabuh", TypeName = "datetime")]
    public DateTime? AnchorageDate { get; set; }

    [Column("Reportdate")]
    public DateOnly? ReportDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? UnloadingDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? FinishUnloadingDate { get; set; }

    [Column(TypeName = "decimal(14, 4)")]
    public decimal? GrtWeight { get; set; }

    [StringLength(50)]
    public string? InvoiceStatus { get; set; }

    public long? AgentId { get; set; }

    [StringLength(255)]
    public string? AgentName { get; set; }

    [Column("status_bms")]
    [StringLength(100)]
    public string StatusBms { get; set; } = null!;

    [Column(TypeName = "decimal(20, 5)")]
    public decimal? GrtVessel { get; set; }

    public long? SurveyorId { get; set; }

    public long? TradingId { get; set; }
    public Guid? JettyId { get; set; }
    public Guid? VesselId { get; set; }
    public Guid? BargeId { get; set; }
    public Guid? MasterAgentId { get; set; }
    public Guid? MasterTradingId { get; set; }
    public Guid? MasterSurveyorId { get; set; }
    /// <summary>
    /// Aside date and time
    /// </summary>
    public DateTime? AsideDate { get; set; }

    /// <summary>
    /// Cast off date and time
    /// </summary>
    public DateTime? CastOfDate { get; set; }
    public Guid? PortOriginId { get; set; }
    public Guid? DestinationPortId { get; set; }

    public virtual Agent? MasterAgent { get; set; }
    public virtual Trading? MasterTrading { get; set; }
    public virtual Surveyor? MasterSurveyor { get; set; }
    public virtual Jetty? MasterJetty { get; set; }
    public virtual Cargo? Vessel { get; set; }
    public virtual Cargo? Barge { get; set; }
    public virtual PortOfLoading? MasterPortOrigin { get; set; }
    public virtual DestinationPort? MasterDestinationPort { get; set; }
    public virtual ICollection<ZoneDetail>? Items { get; set; }
}
