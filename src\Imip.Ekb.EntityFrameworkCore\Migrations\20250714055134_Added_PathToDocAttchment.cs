﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class Added_PathToDocAttchment : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                UPDATE BEXP
                SET ZoneDetailId = (SELECT tbc.Id FROM T_MDOC tbc WHERE BEXP.BaseId = tbc.DocEntry);
            ");

            migrationBuilder.Sql(@"
                UPDATE BEXP
                SET MasterTenantId = (SELECT tbc.Id FROM M_Tenant tbc WHERE BEXP.TenantId = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE BEXP
                SET BcTypeId = (SELECT tbc.Id FROM M_TBC tbc WHERE BEXP.BCType = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE BEXP
                SET HeaderId = (SELECT tbc.Id FROM BHEXP tbc WHERE BEXP.DocNum = tbc.DocEntry)
                WHERE Type = 'Export';
            ");
            migrationBuilder.Sql(@"
                UPDATE BEXP
                SET HeaderId = (SELECT tbc.Id FROM BHIMP tbc WHERE BEXP.DocNum = tbc.DocEntry)
                WHERE Type = 'Import';
            ");
            migrationBuilder.Sql(@"
                UPDATE BEXP
                SET HeaderId = (SELECT tbc.Id FROM BHLOCAL tbc WHERE BEXP.DocNum = tbc.DocEntry)
                WHERE Type IN ('IN', 'OUT');
            ");

            // Import (ImportDetails)
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/IMPORT/' + 
                    ISNULL(CAST(iv.DocNum AS VARCHAR), 'unknown') + '/' +
                    ISNULL(
                        UPPER(REPLACE(REPLACE(REPLACE(ISNULL(zd.No_bl, zd.BL_No), ' ', '_'), '-', '_'), '/', '_')),
                        'nobl'
                    )
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC zd ON att.ReferenceId = zd.Id
                INNER JOIN T_MDOC_Header iv ON zd.HeaderId = iv.Id
                WHERE att.DocType = 'Import'
                AND att.TransType = 'ImportDetails'
                ");

            // Import (ImportDetails Inv)
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/IMPORT/' + 
                    ISNULL(CAST(head.DocNum AS VARCHAR), 'unknown') + '/' +
                    ISNULL(
                        UPPER(REPLACE(REPLACE(REPLACE(ISNULL(zd.No_bl, zd.BL_No), ' ', '_'), '-', '_'), '/', '_')),
                        'nobl'
                    ) + '/' +
                    ISNULL(CAST(iv.No_inv AS VARCHAR), 'unknown')
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC zd ON att.ReferenceId = zd.Id
                INNER JOIN T_MDOC_Bl iv ON iv.ZoneDetailId = zd.Id
                INNER JOIN T_MDOC_Header head ON zd.HeaderId = head.Id
                WHERE att.DocType = 'Import'
                AND att.TransType = 'ImportDetails'
                ");

            // Local (detail)
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/LOCAL/' + ISNULL(CAST(lv.DocNum AS VARCHAR), 'unknown')
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC zd ON att.ReferenceId = zd.Id
                INNER JOIN L_Master lv ON zd.HeaderId = lv.Id
                WHERE att.DocType = 'Local'
                AND att.TransType = 'detail'
                ");

            // BillingImport
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/BillingImport/' + CAST(ib.DocNum AS VARCHAR)
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                LEFT JOIN BHIMP ib ON bi.HeaderId = ib.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingImport'
                ");

            // BillingExport
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/BillingExport/' + CAST(eb.DocNum AS VARCHAR)
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                LEFT JOIN BHEXP eb ON bi.HeaderId = eb.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingExport'
                ");

            // BillingLocalIN
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/BillingLocalIN/' + CAST(lvb.DocNum AS VARCHAR)
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                LEFT JOIN BHLOCAL lvb ON bi.HeaderId = lvb.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingLocalIN'
                ");

            // BillingLocalOUT
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/BillingLocalOUT/' + CAST(lvb.DocNum AS VARCHAR)
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                LEFT JOIN BHLOCAL lvb ON bi.HeaderId = lvb.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingLocalOUT'
                ");

            // BillingDetail
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/BILLING/BillingDetail/' + ISNULL(CAST(bi.DocEntry AS VARCHAR), ISNULL(CAST(att.DocEntry AS VARCHAR), 'unknown'))
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingDetail'
                ");

            // BillingOther
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 
                    'docs/BILLING/BillingOther/' + 
                    CASE
                        WHEN bi.Type = 'Import' THEN (select CAST(DocNum as VARCHAR) from BHIMP where Id = bi.HeaderId)
                        WHEN bi.Type = 'Export' THEN (select CAST(DocNum as VARCHAR) from BHEXP where Id = bi.HeaderId)
                        ELSE (select CAST(DocNum as VARCHAR) from BHLOCAL where Id = bi.HeaderId)
                    END
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingOther'
                ");

            // Fallback for any not set
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                    SET Path = 'docs/DEFAULT'
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC zd ON att.ReferenceId = zd.Id
                WHERE att.DocType = 'Import'
                AND att.TransType = 'ImportDetails'
                AND att.Path IS NULL OR Path = ''
                ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Path = 'docs/DEFAULT'
                FROM M_Doc_attachment
                WHERE Path IS NULL OR Path = ''
                ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // 
        }
    }
}
