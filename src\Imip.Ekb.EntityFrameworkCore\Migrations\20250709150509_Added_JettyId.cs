﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class Added_JettyId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "JettyId",
                table: "THEXP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "JettyId",
                table: "T_MDOC_Header",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ExportVesselId",
                table: "T_MDOC",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ImportVesselId",
                table: "T_MDOC",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "LocalVesselId",
                table: "T_MDOC",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TradingVesselId",
                table: "T_MDOC",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsCustomArea",
                table: "M_Jetty",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "JettyId",
                table: "L_master",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "JettyId",
                table: "BHLOCAL",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "JettyId",
                table: "BHIMP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "JettyId",
                table: "BHEXP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddUniqueConstraint(
                name: "AK_M_Jetty_Id",
                table: "M_Jetty",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_THEXP_JettyId",
                table: "THEXP",
                column: "JettyId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_Header_JettyId",
                table: "T_MDOC_Header",
                column: "JettyId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_ExportVesselId",
                table: "T_MDOC",
                column: "ExportVesselId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_ImportVesselId",
                table: "T_MDOC",
                column: "ImportVesselId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_LocalVesselId",
                table: "T_MDOC",
                column: "LocalVesselId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_TradingVesselId",
                table: "T_MDOC",
                column: "TradingVesselId");

            migrationBuilder.CreateIndex(
                name: "IX_L_master_JettyId",
                table: "L_master",
                column: "JettyId");

            migrationBuilder.CreateIndex(
                name: "IX_BHLOCAL_JettyId",
                table: "BHLOCAL",
                column: "JettyId");

            migrationBuilder.CreateIndex(
                name: "IX_BHIMP_JettyId",
                table: "BHIMP",
                column: "JettyId");

            migrationBuilder.CreateIndex(
                name: "IX_BHEXP_JettyId",
                table: "BHEXP",
                column: "JettyId");

            migrationBuilder.AddForeignKey(
                name: "FK_BHEXP_M_Jetty_JettyId",
                table: "BHEXP",
                column: "JettyId",
                principalTable: "M_Jetty",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BHIMP_M_Jetty_JettyId",
                table: "BHIMP",
                column: "JettyId",
                principalTable: "M_Jetty",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BHLOCAL_M_Jetty_JettyId",
                table: "BHLOCAL",
                column: "JettyId",
                principalTable: "M_Jetty",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_L_master_M_Jetty_JettyId",
                table: "L_master",
                column: "JettyId",
                principalTable: "M_Jetty",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_L_master_LocalVesselId",
                table: "T_MDOC",
                column: "LocalVesselId",
                principalTable: "L_master",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_R_Master_TradingVesselId",
                table: "T_MDOC",
                column: "TradingVesselId",
                principalTable: "R_Master",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_THEXP_ExportVesselId",
                table: "T_MDOC",
                column: "ExportVesselId",
                principalTable: "THEXP",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_T_MDOC_Header_ImportVesselId",
                table: "T_MDOC",
                column: "ImportVesselId",
                principalTable: "T_MDOC_Header",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_Header_M_Jetty_JettyId",
                table: "T_MDOC_Header",
                column: "JettyId",
                principalTable: "M_Jetty",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_THEXP_M_Jetty_JettyId",
                table: "THEXP",
                column: "JettyId",
                principalTable: "M_Jetty",
                principalColumn: "Id");


            migrationBuilder.Sql(@"
                UPDATE T_MDOC_Header
                SET JettyId = (SELECT tbc.Id FROM M_Jetty tbc WHERE T_MDOC_Header.Jetty = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE L_master
                SET JettyId = (SELECT tbc.Id FROM M_Jetty tbc WHERE L_master.Jetty = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE THEXP
                SET JettyId = (SELECT tbc.Id FROM M_Jetty tbc WHERE THEXP.Jetty = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE BHIMP
                SET JettyId = (SELECT tbc.Id FROM M_Jetty tbc WHERE BHIMP.Jetty = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE BHLOCAL
                SET JettyId = (SELECT tbc.Id FROM M_Jetty tbc WHERE BHLOCAL.Jetty = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE BHEXP
                SET JettyId = (SELECT tbc.Id FROM M_Jetty tbc WHERE BHEXP.Jetty = tbc.DocEntry);
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BHEXP_M_Jetty_JettyId",
                table: "BHEXP");

            migrationBuilder.DropForeignKey(
                name: "FK_BHIMP_M_Jetty_JettyId",
                table: "BHIMP");

            migrationBuilder.DropForeignKey(
                name: "FK_BHLOCAL_M_Jetty_JettyId",
                table: "BHLOCAL");

            migrationBuilder.DropForeignKey(
                name: "FK_L_master_M_Jetty_JettyId",
                table: "L_master");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_L_master_LocalVesselId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_R_Master_TradingVesselId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_THEXP_ExportVesselId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_T_MDOC_Header_ImportVesselId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_Header_M_Jetty_JettyId",
                table: "T_MDOC_Header");

            migrationBuilder.DropForeignKey(
                name: "FK_THEXP_M_Jetty_JettyId",
                table: "THEXP");

            migrationBuilder.DropIndex(
                name: "IX_THEXP_JettyId",
                table: "THEXP");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_Header_JettyId",
                table: "T_MDOC_Header");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_ExportVesselId",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_ImportVesselId",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_LocalVesselId",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_TradingVesselId",
                table: "T_MDOC");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_M_Jetty_Id",
                table: "M_Jetty");

            migrationBuilder.DropIndex(
                name: "IX_L_master_JettyId",
                table: "L_master");

            migrationBuilder.DropIndex(
                name: "IX_BHLOCAL_JettyId",
                table: "BHLOCAL");

            migrationBuilder.DropIndex(
                name: "IX_BHIMP_JettyId",
                table: "BHIMP");

            migrationBuilder.DropIndex(
                name: "IX_BHEXP_JettyId",
                table: "BHEXP");

            migrationBuilder.DropColumn(
                name: "JettyId",
                table: "THEXP");

            migrationBuilder.DropColumn(
                name: "JettyId",
                table: "T_MDOC_Header");

            migrationBuilder.DropColumn(
                name: "ExportVesselId",
                table: "T_MDOC");

            migrationBuilder.DropColumn(
                name: "ImportVesselId",
                table: "T_MDOC");

            migrationBuilder.DropColumn(
                name: "LocalVesselId",
                table: "T_MDOC");

            migrationBuilder.DropColumn(
                name: "TradingVesselId",
                table: "T_MDOC");

            migrationBuilder.DropColumn(
                name: "IsCustomArea",
                table: "M_Jetty");

            migrationBuilder.DropColumn(
                name: "JettyId",
                table: "L_master");

            migrationBuilder.DropColumn(
                name: "JettyId",
                table: "BHLOCAL");

            migrationBuilder.DropColumn(
                name: "JettyId",
                table: "BHIMP");

            migrationBuilder.DropColumn(
                name: "JettyId",
                table: "BHEXP");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_HeaderId",
                table: "T_MDOC",
                column: "HeaderId");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_L_master_HeaderId",
                table: "T_MDOC",
                column: "HeaderId",
                principalTable: "L_master",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_R_Master_HeaderId",
                table: "T_MDOC",
                column: "HeaderId",
                principalTable: "R_Master",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_THEXP_HeaderId",
                table: "T_MDOC",
                column: "HeaderId",
                principalTable: "THEXP",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_T_MDOC_Header_HeaderId",
                table: "T_MDOC",
                column: "HeaderId",
                principalTable: "T_MDOC_Header",
                principalColumn: "Id");
        }
    }
}
