﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.BoundedZone.ImportVessels;

public interface IImportVesselRepository : IRepository<ImportVessel, Guid>
{
    Task<IQueryable<ImportVessel>> GetQueryableWithIncludesAsync();
    Task<IQueryable<ImportVessel>> GetQueryableWithItemsAsync();
    Task<ImportVessel?> GetQueryableWithItemsSplitAsync(Guid id);
    Task<int> CountAsync();
}