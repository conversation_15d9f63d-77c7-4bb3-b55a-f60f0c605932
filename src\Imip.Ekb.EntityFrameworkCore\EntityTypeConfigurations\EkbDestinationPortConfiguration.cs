using Imip.Ekb.Master.DestinationPorts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbDestinationPortConfiguration : IEntityTypeConfiguration<DestinationPort>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbDestinationPortConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<DestinationPort> b)
    {
        b.ToTable("M_DestinationPort", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions
    }
}