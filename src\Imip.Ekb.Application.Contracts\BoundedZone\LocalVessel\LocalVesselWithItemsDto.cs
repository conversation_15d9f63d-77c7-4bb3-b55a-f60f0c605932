using System.Collections.Generic;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.BoundedZone.LocalVessel;

namespace Imip.Ekb.BoundedZone.LocalVessels;

public class LocalVesselWithItemsDto : LocalVesselDto
{
    public List<VesselItemDto> Items { get; set; } = new();
}

public class LocalVesselProjectionWithItemsDto : LocalVesselProjectionDto
{
    public List<VesselItemDto> Items { get; set; } = new();
}