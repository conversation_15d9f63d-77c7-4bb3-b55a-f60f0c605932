using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Master.Dtos;

public class AgentProjectionDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? Type { get; set; }
    public string? NpwpNo { get; set; }
    public string? BdmSapcode { get; set; }
    public string? TaxCode { get; set; }
    public string? AddressNpwp { get; set; }
    public string? Address { get; set; }
    public string? SapcodeS4 { get; set; }
}

public class TradingProjectionDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Address { get; set; }
    public string? Npwp { get; set; }
    public string IsActive { get; set; } = string.Empty;
}

public class SurveyorProjectionDto : AuditedEntityDto<Guid>
{
    public long DocEntry { get; set; }
    public string Name { get; set; } = null!;
    public string? Address { get; set; }
    public string? Npwp { get; set; }
    public string IsActive { get; set; } = null!;
}

public class JettyProjectionDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Alias { get; set; } = string.Empty;
    public decimal Max { get; set; }
    public string Deleted { get; set; } = string.Empty;
    public string? Port { get; set; }
    public bool? IsCustomArea { get; set; }
}

public class CargoProjectionDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? Alias { get; set; }
    public string? Flag { get; set; }
    public decimal GrossWeight { get; set; }
    public string? Type { get; set; }
    public decimal? LoaQty { get; set; }
}

public class PortOfOriginProjectionDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DocType { get; set; } = string.Empty;
}

public class DestinationPortProjectionDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DocType { get; set; } = string.Empty;
}