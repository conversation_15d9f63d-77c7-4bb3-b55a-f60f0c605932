using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Imip.Ekb.Master.Jetties.Dtos;

namespace Imip.Ekb.Billing.LocalVesselBillings.Dtos;

public class ImportVesselBillingDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }

    [StringLength(255)]
    public string PortService { get; set; } = null!;

    [StringLength(255)]
    public string Status { get; set; } = null!;

    [StringLength(50)]
    public string YearArrival { get; set; } = null!;

    public int Jetty { get; set; }

    public int ImportId { get; set; }

    public int CreatedBy { get; set; }

    public int UpdatedBy { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    [StringLength(255)]
    public string? DocNum { get; set; }

    public DateOnly? PostingDate { get; set; }

    public string? Remarks { get; set; }

    public DateOnly? PeriodDate { get; set; }

    public DateOnly? BillingNoteDate { get; set; }

    public Guid? JettyId { get; set; }

    // Navigation properties
    public JettyDto? MasterJetty { get; set; }
}