using Imip.Ekb.Master.Tradings;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbTradingConfiguration : IEntityTypeConfiguration<Trading>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbTradingConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<Trading> b)
    {
        b.ToTable("master_tradings", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        // Configure DocEntry property to handle potential type mismatch
        b.Property(x => x.DocEntry)
            .HasColumnType("bigint"); // Ensure it's stored as bigint in database

        b.Property(x => x.CreatedBy)
            .HasColumnType("bigint");
        b.Property(x => x.UpdatedBy)
            .HasColumnType("bigint");
    }
}