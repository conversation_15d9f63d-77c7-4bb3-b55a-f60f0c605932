﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class Added_seedData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_L_master_HeaderId",
                table: "T_MDOC"
            );

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_R_Master_HeaderId",
                table: "T_MDOC"
            );
            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_THEXP_HeaderId",
                table: "T_MDOC"
            );
            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_T_MDOC_Header_HeaderId",
                table: "T_MDOC"
            );

            migrationBuilder.Sql(@"
                UPDATE T_MDOC
                SET BcTypeId = (SELECT tbc.Id FROM M_TBC tbc WHERE T_MDOC.BC_type_key = tbc.DocEntry);
            ");

            migrationBuilder.Sql(@"
                UPDATE T_MDOC
                SET TenantId = (SELECT t.Id FROM M_Tenant t WHERE T_MDOC.Tenant_key = t.DocEntry);
            ");

            migrationBuilder.Sql(@"
                UPDATE T_MDOC
                SET HeaderId = (SELECT h.Id FROM T_MDOC_Header h WHERE T_MDOC.DocNum = h.DocEntry)
                WHERE DocType = 'Import';
            ");

            migrationBuilder.Sql(@"
                UPDATE T_MDOC
                SET HeaderId = (SELECT l.Id FROM L_master l WHERE l.DocEntry = T_MDOC.DocNum)
                WHERE DocType = 'Local'
                AND EXISTS (SELECT 1 FROM L_master l WHERE l.DocEntry = T_MDOC.DocNum);
            ");

            migrationBuilder.Sql(@"
                UPDATE T_MDOC
                SET HeaderId = (SELECT e.Id FROM THEXP e WHERE T_MDOC.DocNum = e.DocEntry)
                WHERE DocType = 'Export';
            ");

            migrationBuilder.Sql(@"
                UPDATE T_MDOC
                SET HeaderId = (SELECT r.Id FROM R_Master r WHERE T_MDOC.DocNum = r.DocEntry)
                WHERE DocType = 'trading';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT t.Id FROM T_MDOC t WHERE M_Doc_attachment.M_doc_key = t.DocEntry AND t.DocType = 'Import')
                WHERE DocType = 'Import' AND TransType = 'ImportDetails';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT b.Id FROM T_MDOC_bl b WHERE M_Doc_attachment.M_doc_key = b.DocEntry AND b.DocType = 'Import')
                WHERE DocType = 'Import' AND TransType = 'Inv';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT l.Id FROM BHLOCAL l WHERE M_Doc_attachment.M_doc_key = l.DocEntry AND l.Type = 'IN')
                WHERE DocType = 'Billing' AND TransType = 'BillingLocalOUT';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT l.Id FROM BHLOCAL l WHERE M_Doc_attachment.M_doc_key = l.DocEntry AND l.Type = 'OUT')
                WHERE DocType = 'Billing' AND TransType = 'BillingLocalIN';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT e.Id FROM BHEXP e WHERE M_Doc_attachment.M_doc_key = e.DocEntry)
                WHERE DocType = 'Billing' AND TransType = 'BillingExport';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT i.Id FROM BHIMP i WHERE M_Doc_attachment.M_doc_key = i.DocEntry)
                WHERE DocType = 'Billing' AND TransType = 'BillingImport';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT b.Id FROM BEXP b WHERE M_Doc_attachment.M_doc_key = b.DocEntry)
                WHERE DocType = 'Billing' AND TransType = 'BillingDetail';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT t.Id FROM T_MDOC t WHERE M_Doc_attachment.M_doc_key = t.DocEntry AND t.DocType = 'Local')
                WHERE DocType = 'Local' AND TransType = 'detail';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT t.Id FROM T_MDOC t WHERE M_Doc_attachment.M_doc_key = t.DocEntry AND t.DocType = 'trading')
                WHERE DocType = 'Trading' AND TransType = 'ImportDetails';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT t.Id FROM T_MDOC t WHERE M_Doc_attachment.M_doc_key = t.DocEntry AND t.DocType = 'trading')
                WHERE DocType = 'Trading' AND TransType = 'Detail';
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET ReferenceId = (SELECT t.Id FROM M_Tenant t WHERE M_Doc_attachment.M_doc_key = t.DocEntry)
                WHERE DocType = 'MasterTenant' AND TransType IN ('ESign', 'Logo', 'DraftBC23', 'ESignReimport', 'Map', 'StockpiledReport', 'StockpiledImage', 'DraftBC20', 'ESignExport', 'TemplateConfig');
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
