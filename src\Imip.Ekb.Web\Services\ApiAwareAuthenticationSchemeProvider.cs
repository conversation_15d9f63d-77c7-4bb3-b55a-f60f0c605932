using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using System.Threading.Tasks;

namespace Imip.Ekb.Web.Services;

/// <summary>
/// Custom authentication scheme provider that selects the appropriate authentication scheme
/// based on whether the request is for an API endpoint or a web page.
/// </summary>
public class ApiAwareAuthenticationSchemeProvider : AuthenticationSchemeProvider
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ApiAwareAuthenticationSchemeProvider(
        IOptions<AuthenticationOptions> options,
        IHttpContextAccessor httpContextAccessor)
        : base(options)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public override async Task<AuthenticationScheme?> GetDefaultChallengeSchemeAsync()
    {
        var httpContext = _httpContextAccessor.HttpContext;

        // If we have an HTTP context and the request is for an API endpoint,
        // use Bearer authentication for challenges
        if (httpContext != null && IsApiRequest(httpContext.Request))
        {
            return await GetSchemeAsync("Bearer");
        }

        // For non-API requests, use the default cookie authentication
        return await GetSchemeAsync("Cookies");
    }

    public override async Task<AuthenticationScheme?> GetDefaultAuthenticateSchemeAsync()
    {
        var httpContext = _httpContextAccessor.HttpContext;

        // If we have an HTTP context and the request is for an API endpoint,
        // try Bearer authentication first
        if (httpContext != null && IsApiRequest(httpContext.Request))
        {
            // Check if Authorization header is present
            if (httpContext.Request.Headers.ContainsKey("Authorization"))
            {
                return await GetSchemeAsync("Bearer");
            }
        }

        // For non-API requests or API requests without Authorization header,
        // use the default cookie authentication
        return await GetSchemeAsync("Cookies");
    }

    private static bool IsApiRequest(HttpRequest request)
    {
        // Check if the request path starts with /api
        // But exclude logout endpoint which should use cookie authentication
        // Also exclude application-configuration endpoint which should use cookie authentication for frontend
        return request.Path.StartsWithSegments("/api") &&
               !request.Path.StartsWithSegments("/api/auth/logout") &&
               !request.Path.StartsWithSegments("/api/abp/application-configuration");
    }
}
