using Imip.Ekb.Web.Services.Interfaces;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Security.Claims;
using Volo.Abp.Users;
using Volo.Abp.Identity;

namespace Imip.Ekb.Web.Authorization.Claims;

public class IdentityServerClaimsPrincipalFactory : AbpClaimsPrincipalFactory, ITransientDependency
{
    private readonly ILogger<IdentityServerClaimsPrincipalFactory> _logger;
    private readonly ITokenService _tokenService;
    private readonly IdentityUserManager _userManager;

    public IdentityServerClaimsPrincipalFactory(
        IServiceProvider serviceProvider,
        Microsoft.Extensions.Options.IOptions<AbpClaimsPrincipalFactoryOptions> options,
        ITokenService tokenService,
        IdentityUserManager userManager,
        ILogger<IdentityServerClaimsPrincipalFactory> logger)
        : base(serviceProvider, options)
    {
        _logger = logger;
        _tokenService = tokenService;
        _userManager = userManager;
    }

    public override async Task<ClaimsPrincipal> CreateAsync(ClaimsPrincipal? principal)
    {
        var claimsPrincipal = await base.CreateAsync(principal);

        // If the principal is not authenticated, return as is
        if (claimsPrincipal?.Identity?.IsAuthenticated != true)
        {
            return claimsPrincipal;
        }

        try
        {
            // Get the current user ID from the claims
            var userId = claimsPrincipal.FindFirst(AbpClaimTypes.UserId)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogDebug("No user ID found in claims, returning base claims principal");
                return claimsPrincipal;
            }

            // Get user information from local database (but preserve JWT roles)
            var user = await _userManager.FindByIdAsync(userId);
            if (user != null)
            {
                // Update the claims principal with local user information but preserve JWT roles
                return await UpdateClaimsPrincipalWithLocalUser(claimsPrincipal, user);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user from local database");
        }

        return claimsPrincipal;
    }

    private async Task<ClaimsPrincipal> UpdateClaimsPrincipalWithLocalUser(ClaimsPrincipal claimsPrincipal, IdentityUser user)
    {
        if (claimsPrincipal.Identity is not ClaimsIdentity identity)
        {
            return claimsPrincipal;
        }

        // Add or update ABP-specific claims
        AddOrUpdateClaim(identity, AbpClaimTypes.UserId, user.Id.ToString());
        AddOrUpdateClaim(identity, AbpClaimTypes.UserName, user.UserName);
        AddOrUpdateClaim(identity, AbpClaimTypes.Email, user.Email ?? "");

        if (!string.IsNullOrEmpty(user.Name))
        {
            AddOrUpdateClaim(identity, AbpClaimTypes.Name, user.Name);
        }

        if (!string.IsNullOrEmpty(user.Surname))
        {
            AddOrUpdateClaim(identity, AbpClaimTypes.SurName, user.Surname);
        }

        if (user.TenantId.HasValue)
        {
            AddOrUpdateClaim(identity, AbpClaimTypes.TenantId, user.TenantId.Value.ToString());
        }

        // IMPORTANT: Do NOT override JWT roles with local database roles
        // The JWT token contains the authoritative roles from Identity Server
        // We should preserve these roles instead of replacing them

        // Count existing role claims (from JWT)
        var existingRoleClaims = identity.Claims.Where(c => c.Type == ClaimTypes.Role).ToList();
        _logger.LogInformation("Preserving {RoleCount} JWT role claims for user {UserName}: {Roles}",
            existingRoleClaims.Count, user.UserName, string.Join(", ", existingRoleClaims.Select(c => c.Value)));

        // Log what roles would be in local database for comparison
        var localRoles = await _userManager.GetRolesAsync(user);
        _logger.LogInformation("Local database has {LocalRoleCount} roles for user {UserName}: {LocalRoles}",
            localRoles.Count, user.UserName, string.Join(", ", localRoles));

        _logger.LogDebug("Updated claims principal with local user info but preserved JWT roles: {UserName} with {RoleCount} JWT roles",
            user.UserName, existingRoleClaims.Count);
        return claimsPrincipal;
    }

    private void AddOrUpdateClaim(ClaimsIdentity identity, string claimType, string value)
    {
        // Remove existing claim if it exists
        var existingClaim = identity.FindFirst(claimType);
        if (existingClaim != null)
        {
            identity.RemoveClaim(existingClaim);
        }

        // Add new claim
        identity.AddClaim(new Claim(claimType, value));
    }
}