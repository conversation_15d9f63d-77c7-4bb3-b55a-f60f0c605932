﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.BoundedZone;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.BcTypes;

[Table("M_TBC")]
public class BcType : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    [StringLength(10)]
    public string Type { get; set; } = null!;

    [Column("Created_by")]
    [StringLength(255)]
    public string CreatedBy { get; set; } = null!;

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [Column("Trans_no")]
    public int? TransNo { get; set; }

    [Column("Trans_name")]
    [StringLength(255)]
    public string? TransName { get; set; }

    [StringLength(255)]
    public string Status { get; set; } = null!;

    public virtual ICollection<BillingItem>? BillingItems { get; set; }
    public virtual ICollection<ZoneDetail>? VesselTransactions { get; set; }
}
