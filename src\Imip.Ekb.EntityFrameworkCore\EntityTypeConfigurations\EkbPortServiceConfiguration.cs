using Imip.Ekb.Master.PortServices;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbPortServiceConfiguration : IEntityTypeConfiguration<PortService>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbPortServiceConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<PortService> b)
    {
        b.ToTable("MPS", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions
    }
}