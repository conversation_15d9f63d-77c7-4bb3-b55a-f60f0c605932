using Imip.Ekb.Billing.LocalVesselBillings.Dtos;
using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Imip.Ekb.Billing.LocalVesselBillings.Interfaces;

namespace Imip.Ekb.Application.Billing;

[Authorize]
public class LocalVesselBillingAppService :
    CrudAppService<
        LocalVesselBilling,
        LocalVesselBillingDto,
        Guid,
        QueryParametersDto,
        CreateUpdateLocalVesselBillingDto>,
    ILocalVesselBillingAppService
{
    private readonly ILocalVesselBillingRepository _localVesselBillingRepository;
    private readonly IRepository<BillingItem, Guid> _billingItemRepository;
    private readonly LocalVesselBillingMapper _localVesselBillingMapper;
    private readonly ILogger<LocalVesselBillingAppService> _logger;

    public LocalVesselBillingAppService(
        ILocalVesselBillingRepository localVesselBillingRepository,
        IRepository<BillingItem, Guid> billingItemRepository,
        LocalVesselBillingMapper localVesselBillingMapper,
        ILogger<LocalVesselBillingAppService> logger)
        : base(localVesselBillingRepository)
    {
        _localVesselBillingRepository = localVesselBillingRepository;
        _billingItemRepository = billingItemRepository;
        _localVesselBillingMapper = localVesselBillingMapper;
        _logger = logger;
    }

    public override async Task<PagedResultDto<LocalVesselBillingDto>> GetListAsync(QueryParametersDto input)
    {
        await CheckGetListPolicyAsync();

        var query = await _localVesselBillingRepository.GetQueryableWithIncludesAsync();

        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            query = query.OrderBy(input.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);
        var entities = await AsyncExecuter.ToListAsync(
            query.PageBy(input.SkipCount, input.MaxResultCount)
        );
        var dtos = entities.Select(MapToGetListOutputDto).ToList();
        return new PagedResultDto<LocalVesselBillingDto>(totalCount, dtos);
    }

    public async Task<PagedResultDto<LocalVesselBillingDto>> FilterListAsync(QueryParametersDto parameters)
    {
        await CheckGetListPolicyAsync();
        var query = await _localVesselBillingRepository.GetQueryableWithIncludesAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(MapToGetListOutputDto).ToList();
        return new PagedResultDto<LocalVesselBillingDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    public override async Task<LocalVesselBillingDto> CreateAsync(CreateUpdateLocalVesselBillingDto input)
    {
        await CheckCreatePolicyAsync();
        var entity = _localVesselBillingMapper.MapToEntity(input);
        await _localVesselBillingRepository.InsertAsync(entity, autoSave: true);
        var createdEntity = await _localVesselBillingRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(createdEntity);
    }

    public override async Task<LocalVesselBillingDto> UpdateAsync(Guid id, CreateUpdateLocalVesselBillingDto input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await _localVesselBillingRepository.GetAsync(id);
        _localVesselBillingMapper.MapToEntity(input, entity);
        await _localVesselBillingRepository.UpdateAsync(entity, autoSave: true);
        var updatedEntity = await _localVesselBillingRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(updatedEntity);
    }

    public async Task<LocalVesselBillingWithItemsDto> GetWithItemsAsync(Guid id)
    {
        await CheckGetPolicyAsync();
        var entity = await _localVesselBillingRepository.GetAsync(id);
        var items = await GetItemsAsync(entity.DocEntry);
        return _localVesselBillingMapper.MapToDtoWithItems(entity, items);
    }

    public async Task<List<BillingItemDto>> GetItemsAsync(int docEntry)
    {
        var items = await _billingItemRepository.GetListAsync(
            x => x.DocEntry == docEntry && x.Type == "LocalVesselBilling"
        );
        return new List<BillingItemDto>();
    }

    private IQueryable<LocalVesselBilling> ApplyDynamicQuery(IQueryable<LocalVesselBilling> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<LocalVesselBilling>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<LocalVesselBilling>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }

    protected override LocalVesselBillingDto MapToGetOutputDto(LocalVesselBilling entity)
    {
        return _localVesselBillingMapper.MapToDto(entity);
    }

    protected override LocalVesselBillingDto MapToGetListOutputDto(LocalVesselBilling entity)
    {
        return _localVesselBillingMapper.MapToDto(entity);
    }

    protected override LocalVesselBilling MapToEntity(CreateUpdateLocalVesselBillingDto createInput)
    {
        return _localVesselBillingMapper.MapToEntity(createInput);
    }

    protected override void MapToEntity(CreateUpdateLocalVesselBillingDto updateInput, LocalVesselBilling entity)
    {
        _localVesselBillingMapper.MapToEntity(updateInput, entity);
    }
}