using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.BoundedZone.Dtos;

public class ZoneDetailWithVesselHeadersDto : ZoneDetailDto
{
    // Vessel Header Information
    public ImportVesselShortDto? ImportVessel { get; set; }
    public ExportVesselShortDto? ExportVessel { get; set; }
    public LocalVesselShortDto? LocalVessel { get; set; }
    public TradingVesselShortDto? TradingVessel { get; set; }
}

public class ImportVesselShortDto
{
    public Guid Id { get; set; }
    public int? DocNum { get; set; }
    public string? VesselName { get; set; }
    public string? Voyage { get; set; }
    public DateTime? VesselArrival { get; set; }
    public DateTime? VesselDeparture { get; set; }
    public string? Shipment { get; set; }
    public string? PortOrigin { get; set; }
    public string? DestinationPort { get; set; }
    public DateTime? BerthingDate { get; set; }
    public DateTime? AnchorageDate { get; set; }
    public DateTime? UnloadingDate { get; set; }
    public DateTime? FinishUnloadingDate { get; set; }
    public decimal? GrtWeight { get; set; }
    public string? Status { get; set; }
    public string? AgentName { get; set; }

    // Master entity links
    public MasterJettyShortDto? MasterJetty { get; set; }
    public MasterPortOriginShortDto? MasterPortOrigin { get; set; }
    public MasterDestinationPortShortDto? MasterDestinationPort { get; set; }
    public MasterVesselShortDto? MasterVessel { get; set; }
}

public class ExportVesselShortDto
{
    public Guid Id { get; set; }
    public string DocNum { get; set; } = string.Empty;
    public string? VesselName { get; set; }
    public string? Voyage { get; set; }
    public DateTime VesselArrival { get; set; }
    public DateTime? VesselDeparture { get; set; }
    public string? Shipment { get; set; }
    public string? PortOrigin { get; set; }
    public string? DestinationPort { get; set; }
    public DateTime? BerthingDate { get; set; }
    public DateTime? AnchorageDate { get; set; }
    public DateTime? UnloadingDate { get; set; }
    public DateTime? FinishUnloadingDate { get; set; }
    public decimal? GrtWeight { get; set; }
    public string? Status { get; set; }
    public string? AgentName { get; set; }

    // Master entity links
    public MasterJettyShortDto? MasterJetty { get; set; }
    public MasterPortOriginShortDto? MasterPortOrigin { get; set; }
    public MasterDestinationPortShortDto? MasterDestinationPort { get; set; }
    public MasterVesselShortDto? MasterVessel { get; set; }
}

public class LocalVesselShortDto
{
    public Guid Id { get; set; }
    public string DocNum { get; set; } = string.Empty;
    public string? VesselName { get; set; }
    public string? Voyage { get; set; }
    public DateTime? VesselArrival { get; set; }
    public DateTime? VesselDeparture { get; set; }
    public string? Shipment { get; set; }
    public string? PortOrigin { get; set; }
    public string? DestinationPort { get; set; }
    public DateTime? BerthingDate { get; set; }
    public DateTime? AnchorageDate { get; set; }
    public DateTime? UnloadingDate { get; set; }
    public DateTime? FinishUnloadingDate { get; set; }
    public decimal? GrtWeight { get; set; }
    public string? Status { get; set; }
    public string? AgentName { get; set; }

    // Master entity links
    public MasterJettyShortDto? MasterJetty { get; set; }
    public MasterPortOriginShortDto? MasterPortOrigin { get; set; }
    public MasterDestinationPortShortDto? MasterDestinationPort { get; set; }
    public MasterVesselShortDto? MasterVessel { get; set; }
    public MasterVesselShortDto? MasterBarge { get; set; }
}

public class TradingVesselShortDto
{
    public Guid Id { get; set; }
    public string? VesselName { get; set; }
    public string? Voyage { get; set; }
    public DateTime? VesselArrival { get; set; }
    public DateTime? VesselDeparture { get; set; }
    public string? Shipment { get; set; }
    public string? PortOrigin { get; set; }
    public string? DestinationPort { get; set; }
    public DateTime? BerthingDate { get; set; }
    public DateTime? AnchorageDate { get; set; }
    public DateTime? UnloadingDate { get; set; }
    public DateTime? FinishUnloadingDate { get; set; }
    public decimal? GrtWeight { get; set; }
    public string? Status { get; set; }
    public string? AgentName { get; set; }

    // Master entity links
    public MasterJettyShortDto? MasterJetty { get; set; }
    public MasterPortOriginShortDto? MasterPortOrigin { get; set; }
    public MasterDestinationPortShortDto? MasterDestinationPort { get; set; }
    public MasterVesselShortDto? MasterVessel { get; set; }
}

// Master entity short DTOs
public class MasterJettyShortDto
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Alias { get; set; }
    public string? Port { get; set; }
    public decimal? Max { get; set; }
}

public class MasterPortOriginShortDto
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Code { get; set; }
    public string? Country { get; set; }
}

public class MasterDestinationPortShortDto
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Code { get; set; }
    public string? Country { get; set; }
}

public class MasterVesselShortDto
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Alias { get; set; }
    public string? Type { get; set; }
    public decimal? GrossWeight { get; set; }
}