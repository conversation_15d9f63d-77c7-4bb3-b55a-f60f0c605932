using System;
using Imip.Ekb.Attachments;
using Imip.Ekb.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.Ekb.Repositories;

public class DocAttachmentRepository : EfCoreRepository<EkbDbContext, DocAttachment, Guid>, IDocAttachmentRepository
{
    public DocAttachmentRepository(IDbContextProvider<EkbDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

}
