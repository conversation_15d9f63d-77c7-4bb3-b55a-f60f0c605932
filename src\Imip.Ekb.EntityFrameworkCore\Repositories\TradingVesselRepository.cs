using Imip.Ekb.BoundedZone.TradingVessels;
using Imip.Ekb.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.Ekb.Repositories;
public class TradingVesselRepository : EfCoreRepository<EkbDbContext, TradingVessel, Guid>, ITradingVesselRepository
{
    public TradingVesselRepository(IDbContextProvider<EkbDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

    // Simple repository methods only
    public virtual async Task<IQueryable<TradingVessel>> GetQueryableWithIncludesAsync()
    {
        return (await GetQueryableAsync())
            .AsNoTracking()
            .Where(x => !x.IsDeleted);
    }

    public virtual async Task<IQueryable<TradingVessel>> GetQueryableWithItemsAsync()
    {
        return (await GetQueryableAsync())
            .AsNoTracking()
            .Include(x => x.Items!)
                .ThenInclude(z => z.Tenant)
            .Include(x => x.Items!)
                .ThenInclude(z => z.DocAttachment)
            .Include(x => x.Items!)
                .ThenInclude(z => z.BusinessPartner)
            .Where(x => !x.IsDeleted);
    }

    public virtual async Task<int> CountAsync()
    {
        var query = await GetQueryableAsync();

        // Use a more efficient count query
        return await query
            .Where(x => !x.IsDeleted)
            .CountAsync();
    }
}