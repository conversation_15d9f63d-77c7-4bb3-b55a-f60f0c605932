﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class AddedBlobNameToAttachment : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add index to Type column for better performance
            migrationBuilder.CreateIndex(
                name: "IX_M_Doc_attachment_Type",
                table: "M_Doc_attachment",
                column: "Type");

            // Add index to Path column
            migrationBuilder.CreateIndex(
                name: "IX_M_Doc_attachment_Path",
                table: "M_Doc_attachment",
                column: "Path");

            // Add index to BlobName column
            migrationBuilder.CreateIndex(
                name: "IX_M_Doc_attachment_BlobName",
                table: "M_Doc_attachment",
                column: "BlobName");

            // Import (ImportDetails)
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET BlobName = 
                    'IMPORT/' + 
                    ISNULL(CAST(iv.DocNum AS VARCHAR), 'unknown') + '/' +
                    ISNULL(
                        UPPER(REPLACE(REPLACE(REPLACE(ISNULL(zd.No_bl, zd.BL_No), ' ', '-'), '-', '-'), '/', '-')),
                        'nobl'
                    ) + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC zd ON att.ReferenceId = zd.Id
                INNER JOIN T_MDOC_Header iv ON zd.HeaderId = iv.Id
                WHERE att.DocType = 'Import'
                AND att.TransType = 'ImportDetails'
                and BlobName IS NULL
                ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET BlobName = 
                    'TR/' + 
                    ISNULL(CAST(iv.DocNo AS VARCHAR), 'unknown') + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC zd ON att.ReferenceId = zd.Id
                INNER JOIN R_Master iv ON zd.HeaderId = iv.Id
                WHERE att.DocType = 'Trading'
                AND att.TransType = 'Detail'
                and BlobName IS NULL
                ");

            // Import (ImportDetails Inv)
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET BlobName = 
                    'IMPORT/' + 
                    ISNULL(CAST(head.DocNum AS VARCHAR), 'unknown') + '/' +
                    ISNULL(
                        UPPER(REPLACE(REPLACE(REPLACE(ISNULL(zd.No_bl, zd.BL_No), ' ', '-'), '-', '-'), '/', '-')),
                        'nobl'
                    ) + '/' +
                    UPPER(REPLACE(REPLACE(REPLACE(CAST(iv.No_inv AS VARCHAR), ' ', '-'), '-', '-'), '/', '-'))
                    + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC_Bl iv ON att.ReferenceId = iv.Id
                INNER JOIN T_MDOC zd ON iv.ZoneDetailId = zd.Id
                INNER JOIN T_MDOC_Header head ON zd.HeaderId = head.Id
                WHERE att.DocType = 'Import'
                AND att.TransType = 'Inv'
                and BlobName IS NULL
                ");

            // Local (detail)
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET BlobName = 
                    'LOCAL/' + ISNULL(CAST(lv.DocNum AS VARCHAR), 'unknown')
                     + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC zd ON att.ReferenceId = zd.Id
                INNER JOIN L_Master lv ON zd.HeaderId = lv.Id
                WHERE att.DocType = 'Local'
                AND att.TransType = 'detail'
                and BlobName IS NULL
                ");

            // BillingImport
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET BlobName = 
                    'BillingImport/' + CAST(ib.DocNum AS VARCHAR)
                     + '/' + name
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                LEFT JOIN BHIMP ib ON bi.HeaderId = ib.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingImport'
                and BlobName IS NULL
                ");

            // BillingExport
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET BlobName = 
                    'BillingExport/' + CAST(eb.DocNum AS VARCHAR)
                     + '/' + name
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                LEFT JOIN BHEXP eb ON bi.HeaderId = eb.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingExport'
                and BlobName IS NULL
                ");

            // BillingLocalIN
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET BlobName = 
                    'BillingLocalIN/' + CAST(lvb.DocNum AS VARCHAR)
                     + '/' + name
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                LEFT JOIN BHLOCAL lvb ON bi.HeaderId = lvb.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingLocalIN'
                and BlobName IS NULL
                ");

            // BillingLocalOUT
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET BlobName = 
                    'BillingLocalOUT/' + CAST(lvb.DocNum AS VARCHAR)
                     + '/' + name
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                LEFT JOIN BHLOCAL lvb ON bi.HeaderId = lvb.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingLocalOUT'
                and BlobName IS NULL
                ");

            // BillingDetail
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET BlobName = 
                    'BILLING/BillingDetail/' + ISNULL(CAST(bi.DocEntry AS VARCHAR), ISNULL(CAST(att.DocEntry AS VARCHAR), 'unknown'))
                     + '/' + name
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingDetail'
                and BlobName IS NULL
                ");

            // BillingOther
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET BlobName = 
                    'BILLING/BillingOther/' + 
                    CASE
                        WHEN bi.Type = 'Import' THEN (select CAST(DocNum as VARCHAR) from BHIMP where Id = bi.HeaderId)
                        WHEN bi.Type = 'Export' THEN (select CAST(DocNum as VARCHAR) from BHEXP where Id = bi.HeaderId)
                        ELSE (select CAST(DocNum as VARCHAR) from BHLOCAL where Id = bi.HeaderId)
                    END
                     + '/' + name
                FROM M_Doc_attachment att
                LEFT JOIN BEXP bi ON att.ReferenceId = bi.Id
                WHERE att.DocType = 'Billing'
                AND att.TransType = 'BillingOther'
                and BlobName IS NULL
                ");

            // Fallback for any not set
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                    SET BlobName = 'MONITORING'  + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN T_MDOC zd ON att.ReferenceId = zd.Id
                WHERE att.DocType = 'Import'
                AND att.TransType = 'ImportDetails'
                AND zd.DocNum IS NULL
                and BlobName IS NULL
                ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                    SET BlobName = 'ErrorCheck'  + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN doc_check_errors zd ON att.ReferenceId = zd.Id
                WHERE att.DocType = 'Common'
                AND att.TransType = 'ErrorCheck'
                and BlobName IS NULL
                ");


            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                    SET BlobName = 'NOTUL'  + '/' + name
                FROM M_Doc_attachment att
                INNER JOIN notuls zd ON att.ReferenceId = zd.Id
                WHERE att.DocType = 'Import'
                AND att.TransType = 'Notul'
                and BlobName IS NULL
                ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                    SET BlobName = 'TENANT'  + '/' + '/' + zd.Name + '/' + att.TransType  + '/'  + att.name
                FROM M_Doc_attachment att
                INNER JOIN M_Tenant zd ON att.ReferenceId = zd.Id
                WHERE att.DocType = 'MasterTenant'
                and BlobName IS NULL
                ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET BlobName = 'DEFAULT'  + '/' + name
                FROM M_Doc_attachment
                WHERE (Path IS NULL OR Path = '')
                and BlobName IS NULL
                ");

            // Update Type column based on file extension in name column
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Type = 'application/pdf'
                WHERE (Type IS NULL OR Type = '')
                  AND (RIGHT(LOWER(name), 4) = '.pdf')
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Type = 'application/vnd.ms-excel'
                WHERE (Type IS NULL OR Type = '')
                  AND (RIGHT(LOWER(name), 4) = '.xls')
                  AND (RIGHT(LOWER(name), 5) <> '.xlsx')
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                WHERE (Type IS NULL OR Type = '')
                  AND (RIGHT(LOWER(name), 5) = '.xlsx')
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Type = 'application/zip'
                WHERE (Type IS NULL OR Type = '')
                  AND (RIGHT(LOWER(name), 4) = '.zip')
            ");

            // Update Type column for additional file types
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Type = 'application/x-rar-compressed'
                WHERE (Type IS NULL OR Type = '')
                  AND (RIGHT(LOWER(name), 4) = '.rar')
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Type = 'image/jpeg'
                WHERE (Type IS NULL OR Type = '')
                  AND (RIGHT(LOWER(name), 4) = '.jpg')
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Type = 'image/jpeg'
                WHERE (Type IS NULL OR Type = '')
                  AND (RIGHT(LOWER(name), 5) = '.jpeg')
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Type = 'application/vnd.ms-excel.sheet.macroEnabled.12'
                WHERE (Type IS NULL OR Type = '')
                  AND (RIGHT(LOWER(name), 5) = '.xlsm')
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Type = 'application/msword'
                WHERE (Type IS NULL OR Type = '')
                  AND (RIGHT(LOWER(name), 4) = '.doc')
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Type = 'image/png'
                WHERE (Type IS NULL OR Type = '')
                  AND (RIGHT(LOWER(name), 4) = '.png')
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET Type = 'application/vnd.ms-works'
                WHERE (Type IS NULL OR Type = '')
                  AND (RIGHT(LOWER(name), 4) = '.wps')
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
