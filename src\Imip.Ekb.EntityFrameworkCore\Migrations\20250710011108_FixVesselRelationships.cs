﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class FixVesselRelationships : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_L_master_LocalVesselId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_R_Master_TradingVesselId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_THEXP_ExportVesselId",
                table: "T_MDOC");


            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_T_MDOC_Header_ImportVesselId",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_ExportVesselId",
                table: "T_MDOC");


            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_ImportVesselId",
                table: "T_MDOC");


            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_LocalVesselId",
                table: "T_MDOC");


            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_TradingVesselId",
                table: "T_MDOC");

            migrationBuilder.DropColumn(
                name: "ExportVesselId",
                table: "T_MDOC");


            migrationBuilder.DropColumn(
                name: "ImportVesselId",
                table: "T_MDOC");

            migrationBuilder.DropColumn(
                name: "LocalVesselId",
                table: "T_MDOC");

            migrationBuilder.DropColumn(
                name: "TradingVesselId",
                table: "T_MDOC");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_L_master_HeaderId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_R_Master_HeaderId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_THEXP_HeaderId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_T_MDOC_Header_HeaderId",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_HeaderId",
                table: "T_MDOC");
        }
    }
}
