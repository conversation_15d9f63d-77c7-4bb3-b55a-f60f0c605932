using Imip.Ekb.Models;
using System;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.ImportVessels.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Imip.Ekb.BoundedZone.ImportVessel;

namespace Imip.Ekb.BoundedZone.ImportVessels;

public interface IImportVesselAppService :
    ICrudAppService<
        ImportVesselDto,
        Guid,
        QueryParametersDto,
        CreateUpdateImportVesselDto>
{
    /// <summary>
    /// Get a filtered list of ImportVessels with dynamic query support
    /// </summary>
    Task<PagedResultDto<ImportVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters);
    Task<ImportVesselWithItemsDto> GetWithItemsAsync(Guid id);
}