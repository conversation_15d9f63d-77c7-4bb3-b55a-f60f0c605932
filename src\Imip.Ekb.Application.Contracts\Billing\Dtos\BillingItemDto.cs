using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Billing.LocalVesselBillings.Dtos;

public class BillingItemDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }

    public int DocNum { get; set; }

    [StringLength(100)]
    public string NoBl { get; set; } = null!;

    public DateOnly? DateBl { get; set; }

    public int TenantId { get; set; }

    public int Bctype { get; set; }

    public string? LoadingItem { get; set; }

    public decimal LoadingQty { get; set; }

    public string? Remark { get; set; }

    [StringLength(5)]
    public string Deleted { get; set; } = null!;

    [StringLength(255)]
    public string? Status { get; set; }

    [StringLength(255)]
    public string? PortServiceType { get; set; }

    [StringLength(255)]
    public string? LoadingUnloadingType { get; set; }

    public int CreatedBy { get; set; }

    public int UpdatedBy { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    [StringLength(100)]
    public string? Unit { get; set; }

    [StringLength(255)]
    public string? Notification { get; set; }

    public string? ItemName { get; set; }

    [StringLength(255)]
    public string? CompanyHeader { get; set; }

    public decimal Total { get; set; }

    [StringLength(255)]
    public string? Signature1 { get; set; }

    [StringLength(255)]
    public string? Signature2 { get; set; }

    [StringLength(255)]
    public string? Signature3 { get; set; }

    public int? Bp { get; set; }

    public int? Jetty { get; set; }

    public string? AttachmentText { get; set; }

    [StringLength(100)]
    public string Type { get; set; } = null!;

    public decimal? Price { get; set; }

    public decimal? ServiceLoading { get; set; }

    public string? Classification { get; set; }

    [StringLength(255)]
    public string WeightCategory { get; set; } = null!;

    public decimal TotalServiceLoading { get; set; }

    [StringLength(255)]
    public string? CurrencyPortService { get; set; }

    [StringLength(255)]
    public string? CurrencyServiceLoading { get; set; }

    public int? BaseId { get; set; }

    public int? Agent { get; set; }

    [StringLength(255)]
    public string? BaseIdString { get; set; }

    public decimal? NetWeight { get; set; }

    public decimal? UnitPrice { get; set; }

    public decimal? TotalInv { get; set; }

    [StringLength(100)]
    public string? StatusServiceLoading { get; set; }

    public decimal? QtyEstimate { get; set; }

    public decimal? PriceEstimate { get; set; }

    [StringLength(200)]
    public string? BillingType { get; set; }

    [StringLength(200)]
    public string? ChargeTo { get; set; }

    [StringLength(50)]
    public string? TaxCode { get; set; }

    public decimal? QtyRevised { get; set; }

    public decimal? PriceRevised { get; set; }

    [StringLength(255)]
    public string? NoNota { get; set; }

    public decimal? TotalEstimate { get; set; }

    public decimal? TotalRevised { get; set; }

    [StringLength(255)]
    public string? NoNotaSl { get; set; }

    public int? LineNum { get; set; }

    [StringLength(255)]
    public string? IsTenant { get; set; }

    public decimal? SubTotal { get; set; }

    public decimal? VatValue { get; set; }

    public decimal? VatValueSl { get; set; }

    public decimal? SubTotalSl { get; set; }

    public decimal? TotalEstimateSl { get; set; }

    public Guid? HeaderId { get; set; }

    public Guid? ZoneDetailId { get; set; }

    public Guid? MasterTenantId { get; set; }

    public Guid? BcTypeId { get; set; }
}