using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Imip.Ekb.Web.Middleware;
/// <summary>
/// Middleware to add CORS headers to all responses
/// </summary>
public class GlobalCorsMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalCorsMiddleware> _logger;
    private readonly IConfiguration _configuration;
    private readonly string[] _allowedOrigins;

    public GlobalCorsMiddleware(
        RequestDelegate next,
        ILogger<GlobalCorsMiddleware> logger,
        IConfiguration configuration)
    {
        _next = next;
        _logger = logger;
        _configuration = configuration;

        // Get allowed origins from configuration
        var corsOriginsConfig = _configuration["App:CorsOrigins"];
        _allowedOrigins = corsOriginsConfig?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>();

        // Trim whitespace from origins
        for (var i = 0; i < _allowedOrigins.Length; i++)
        {
            _allowedOrigins[i] = _allowedOrigins[i].Trim();
        }

        _logger.LogInformation("GlobalCorsMiddleware initialized with allowed origins: {Origins}",
            string.Join(", ", _allowedOrigins));
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Get the origin from the request
        var requestOrigin = context.Request.Headers["Origin"].ToString();

        // Check if the origin is allowed
        var isAllowedOrigin = false;
        if (!string.IsNullOrEmpty(requestOrigin))
        {
            if (_allowedOrigins.Length == 0)
            {
                // If no origins are configured, allow all
                isAllowedOrigin = true;
                if (string.Equals(Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"), "Production", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogError("No CORS origins configured, allowing all origins in PRODUCTION! This is a security risk.");
                }
                else
                {
                    _logger.LogWarning("No CORS origins configured, allowing all origins");
                }
            }
            else
            {
                // Check if the request origin is in the allowed list
                foreach (var allowedOrigin in _allowedOrigins)
                {
                    if (string.Equals(requestOrigin, allowedOrigin, StringComparison.OrdinalIgnoreCase))
                    {
                        isAllowedOrigin = true;
                        break;
                    }
                }
            }
        }

        // If this is an OPTIONS request, handle it directly
        if (context.Request.Method == "OPTIONS")
        {
            _logger.LogInformation("Handling OPTIONS request for path: {Path}", context.Request.Path);

            if (isAllowedOrigin)
            {
                // Set CORS headers (overwrite if present)
                context.Response.Headers["Access-Control-Allow-Origin"] = requestOrigin;
                context.Response.Headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS";
                context.Response.Headers["Access-Control-Allow-Headers"] =
                    "Content-Type, Authorization, X-CSRF-TOKEN, X-Requested-With, Accept, Origin, Cache-Control, " +
                    "X-XSRF-TOKEN, X-PINGOTHER, X-FORWARDED-FOR, X-FORWARDED-PROTO, X-FORWARDED-HOST";
                context.Response.Headers["Access-Control-Allow-Credentials"] = "true";
                context.Response.Headers["Access-Control-Max-Age"] = "86400"; // 24 hours
                context.Response.Headers["Vary"] = "Origin"; // Important for caching

                // Return 200 OK and complete response
                context.Response.StatusCode = StatusCodes.Status200OK;
                await context.Response.CompleteAsync();
                return;
            }
        }

        // For non-OPTIONS requests, add CORS headers to the response
        if (isAllowedOrigin)
        {
            context.Response.OnStarting(() =>
            {
                // Set CORS headers (overwrite if present)
                context.Response.Headers["Access-Control-Allow-Origin"] = requestOrigin;
                context.Response.Headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS";
                context.Response.Headers["Access-Control-Allow-Headers"] =
                    "Content-Type, Authorization, X-CSRF-TOKEN, X-Requested-With, Accept, Origin, Cache-Control, " +
                    "X-XSRF-TOKEN, X-PINGOTHER, X-FORWARDED-FOR, X-FORWARDED-PROTO, X-FORWARDED-HOST";
                context.Response.Headers["Access-Control-Allow-Credentials"] = "true";
                context.Response.Headers["Vary"] = "Origin"; // Important for caching
                return Task.CompletedTask;
            });
        }

        // Continue to the next middleware
        await _next(context);
    }
}

// Recommended: Place UseGlobalCors() after UseRouting() and before authentication/authorization middlewares in the pipeline.
// Extension method used to add the middleware to the HTTP request pipeline
public static class GlobalCorsMiddlewareExtensions
{
    public static IApplicationBuilder UseGlobalCors(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<GlobalCorsMiddleware>();
    }
}