using Imip.Ekb.Master.Agents;
using Imip.Ekb.Master.Agents.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class AgentMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(Agent.Id), nameof(AgentDto.Id))]
    [MapperIgnoreSource(nameof(Agent.IsDeleted))]
    [MapperIgnoreSource(nameof(Agent.DeleterId))]
    [MapperIgnoreSource(nameof(Agent.DeletionTime))]
    [MapperIgnoreSource(nameof(Agent.LastModificationTime))]
    [MapperIgnoreSource(nameof(Agent.LastModifierId))]
    [MapperIgnoreSource(nameof(Agent.CreationTime))]
    [MapperIgnoreSource(nameof(Agent.CreatorId))]
    [MapperIgnoreSource(nameof(Agent.ExtraProperties))]
    [MapperIgnoreSource(nameof(Agent.ConcurrencyStamp))]
    public partial AgentDto MapToDto(Agent entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(Agent.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(Agent.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(Agent.CreatedAt))]
    public partial void MapToEntity(AgentCreateUpdateDto dto, Agent entity);

    // Custom mapping methods for complex scenarios
    public Agent CreateEntityWithId(AgentCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (Agent)Activator.CreateInstance(typeof(Agent), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<AgentDto> MapToDtoList(List<Agent> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<AgentDto> MapToDtoEnumerable(IEnumerable<Agent> entities);
}