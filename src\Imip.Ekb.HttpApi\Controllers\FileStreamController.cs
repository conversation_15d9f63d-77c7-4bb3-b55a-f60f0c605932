using Imip.Ekb.Application.Contracts.Attachments;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.Ekb.HttpApi.Controllers;

[RemoteService]
[Route("api/ekb/files")]
public class FileStreamController : AbpController
{
    private readonly IDocAttachmentAppService _docAttachmentAppService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<FileStreamController> _logger;

    public FileStreamController(
        IDocAttachmentAppService docAttachmentAppService,
        IConfiguration configuration,
        ILogger<FileStreamController> logger)
    {
        _docAttachmentAppService = docAttachmentAppService;
        _configuration = configuration;
        _logger = logger;
    }

    [HttpGet]
    [Route("download/{id}")]
    public async Task<IActionResult> DownloadAsync(Guid id)
    {
        try
        {
            var fileDto = await _docAttachmentAppService.DownloadAsync(id);
            return File(fileDto.Content, fileDto.ContentType, fileDto.FileName);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "File download failed for ID: {Id}", id);
            return NotFound(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during file download for ID: {Id}", id);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    [HttpGet]
    [Route("stream/{id}")]
    public async Task<IActionResult> StreamAsync(Guid id)
    {
        try
        {
            var fileDto = await _docAttachmentAppService.DownloadAsync(id);

            // Set content disposition to inline to display in the browser
            Response.Headers.Append("Content-Disposition", "inline");
            return File(fileDto.Content, fileDto.ContentType, enableRangeProcessing: true);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "File streaming failed for ID: {Id}", id);
            return NotFound(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during file streaming for ID: {Id}", id);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    [HttpGet]
    [Route("stream/{*filePath}")]
    public async Task<IActionResult> StreamByPathAsync(string filePath)
    {
        try
        {
            // Use the existing DocAttachmentAppService to get file from SFTP blob storage
            var fileDto = await _docAttachmentAppService.DownloadByPathAsync(filePath);

            // Set content disposition to inline to display in the browser
            Response.Headers.Append("Content-Disposition", "inline");
            return File(fileDto.Content, fileDto.ContentType, enableRangeProcessing: true);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "SFTP file streaming failed for path: {FilePath}", filePath);
            return NotFound(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error streaming file from SFTP path: {FilePath}", filePath);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    [HttpGet]
    [Route("info/{id}")]
    public async Task<IActionResult> GetFileInfoAsync(Guid id)
    {
        try
        {
            var docAttachment = await _docAttachmentAppService.GetAsync(id);
            return Ok(docAttachment);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "File info retrieval failed for ID: {Id}", id);
            return NotFound(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during file info retrieval for ID: {Id}", id);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }
}