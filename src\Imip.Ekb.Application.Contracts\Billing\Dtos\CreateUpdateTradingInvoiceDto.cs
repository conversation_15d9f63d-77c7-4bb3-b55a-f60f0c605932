using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.BoundedZone.Dtos;

public class CreateUpdateTradingInvoiceDto : EntityDto<Guid>
{
    public long DocNum { get; set; }
    public string? PInvNo { get; set; }
    public DateOnly? PInvDate { get; set; }
    public string? InvNo { get; set; }
    public DateOnly? InvDate { get; set; }
    public string? SuratJalan { get; set; }
    public string? PackingList { get; set; }
    public string? Faktur { get; set; }
    public long CreatedBy { get; set; }
    public long UpdatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string Deleted { get; set; } = null!;
    public string? Status { get; set; }
    public Guid? ZoneDetailId { get; set; }
}