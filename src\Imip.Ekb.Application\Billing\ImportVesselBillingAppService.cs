using Imip.Ekb.Billing.LocalVesselBillings.Dtos;
using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Imip.Ekb.Billing.LocalVesselBillings.Interfaces;
using Imip.Ekb.Billing.ImportVesselBillings;

namespace Imip.Ekb.Application.Billing;

[Authorize]
public class ImportVesselBillingAppService :
    CrudAppService<
        ImportVesselBilling,
        ImportVesselBillingDto,
        Guid,
        QueryParametersDto,
        CreateUpdateImportVesselBillingDto>,
    IImportVesselBillingAppService
{
    private readonly IImportVesselBillingRepository _importVesselBillingRepository;
    private readonly IRepository<BillingItem, Guid> _billingItemRepository;
    private readonly ImportVesselBillingMapper _importVesselBillingMapper;
    private readonly ILogger<ImportVesselBillingAppService> _logger;

    public ImportVesselBillingAppService(
        IImportVesselBillingRepository importVesselBillingRepository,
        IRepository<BillingItem, Guid> billingItemRepository,
        ImportVesselBillingMapper importVesselBillingMapper,
        ILogger<ImportVesselBillingAppService> logger)
        : base(importVesselBillingRepository)
    {
        _importVesselBillingRepository = importVesselBillingRepository;
        _billingItemRepository = billingItemRepository;
        _importVesselBillingMapper = importVesselBillingMapper;
        _logger = logger;
    }

    public override async Task<PagedResultDto<ImportVesselBillingDto>> GetListAsync(QueryParametersDto input)
    {
        await CheckGetListPolicyAsync();

        var query = await _importVesselBillingRepository.GetQueryableWithIncludesAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            query = query.OrderBy(input.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);

        var entities = await AsyncExecuter.ToListAsync(
            query.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(MapToGetListOutputDto).ToList();

        return new PagedResultDto<ImportVesselBillingDto>(totalCount, dtos);
    }

    public async Task<PagedResultDto<ImportVesselBillingDto>> FilterListAsync(QueryParametersDto parameters)
    {
        await CheckGetListPolicyAsync();

        var query = await _importVesselBillingRepository.GetQueryableWithIncludesAsync();
        query = ApplyDynamicQuery(query, parameters);

        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        var dtos = items.Select(MapToGetListOutputDto).ToList();

        return new PagedResultDto<ImportVesselBillingDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    public override async Task<ImportVesselBillingDto> CreateAsync(CreateUpdateImportVesselBillingDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _importVesselBillingMapper.MapToEntity(input);

        await _importVesselBillingRepository.InsertAsync(entity, autoSave: true);

        // Load the entity with includes for proper mapping
        var createdEntity = await _importVesselBillingRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(createdEntity);
    }

    public override async Task<ImportVesselBillingDto> UpdateAsync(Guid id, CreateUpdateImportVesselBillingDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _importVesselBillingRepository.GetAsync(id);
        _importVesselBillingMapper.MapToEntity(input, entity);

        await _importVesselBillingRepository.UpdateAsync(entity, autoSave: true);

        // Load the entity with includes for proper mapping
        var updatedEntity = await _importVesselBillingRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(updatedEntity);
    }

    public async Task<ImportVesselBillingWithItemsDto> GetWithItemsAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _importVesselBillingRepository.GetAsync(id);
        var items = await GetItemsAsync(entity.DocEntry);

        return _importVesselBillingMapper.MapToDtoWithItems(entity, items);
    }

    public async Task<List<BillingItemDto>> GetItemsAsync(int docEntry)
    {
        var items = await _billingItemRepository.GetListAsync(
            x => x.DocEntry == docEntry && x.Type == "ImportVesselBilling"
        );

        // For now, return empty list - you can create a BillingItemMapper if needed
        return new List<BillingItemDto>();
    }

    private IQueryable<ImportVesselBilling> ApplyDynamicQuery(IQueryable<ImportVesselBilling> query, QueryParametersDto parameters)
    {
        // Apply filters if provided
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ImportVesselBilling>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ImportVesselBilling>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }

    protected override ImportVesselBillingDto MapToGetOutputDto(ImportVesselBilling entity)
    {
        return _importVesselBillingMapper.MapToDto(entity);
    }

    protected override ImportVesselBillingDto MapToGetListOutputDto(ImportVesselBilling entity)
    {
        return _importVesselBillingMapper.MapToDto(entity);
    }

    protected override ImportVesselBilling MapToEntity(CreateUpdateImportVesselBillingDto createInput)
    {
        return _importVesselBillingMapper.MapToEntity(createInput);
    }

    protected override void MapToEntity(CreateUpdateImportVesselBillingDto updateInput, ImportVesselBilling entity)
    {
        _importVesselBillingMapper.MapToEntity(updateInput, entity);
    }
}