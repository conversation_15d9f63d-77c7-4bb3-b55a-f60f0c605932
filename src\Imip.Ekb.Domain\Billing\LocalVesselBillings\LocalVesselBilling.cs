﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.Master.Jetties;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Billing.LocalVesselBillings;

[Table("BHLOCAL")]
public class LocalVesselBilling : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    [StringLength(255)]
    public string PortService { get; set; } = null!;

    [StringLength(255)]
    public string Status { get; set; } = null!;

    [StringLength(255)]
    public string? YearArrival { get; set; }

    public int Jetty { get; set; }

    [Column("LocalID")]
    public int LocalId { get; set; }

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }

    public int? DocNum { get; set; }

    public DateOnly PostingDate { get; set; }

    [StringLength(255)]
    public string Remarks { get; set; } = null!;

    [StringLength(255)]
    public string Type { get; set; } = null!;

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    public DateOnly? PeriodDate { get; set; }

    public DateOnly? BillingNoteDate { get; set; }
    public Guid? JettyId { get; set; }
    public virtual Jetty? MasterJetty { get; set; }
    public virtual ICollection<BillingItem>? Items { get; set; }
}
