﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class AddedTabNameColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Tab<PERSON><PERSON>",
                table: "M_Doc_attachment",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'ImportJakartaBl'
                WHERE DocType = 'Import' AND TransType = 'ImportDetails'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by) = 6;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'ImportJakartaInv'
                WHERE DocType = 'Import' AND TransType = 'Inv'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by )  = 6;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'ImportPpjkSiteBl'
                WHERE DocType = 'Import' AND TransType = 'ImportDetails'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by and (ppjk_id is null or ppjk_id = 0) )  = 3;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'ImportTenantInv'
                WHERE DocType = 'Import' AND TransType = 'Inv'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by )  = 5;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'ImportPpjkJktBl'
                WHERE DocType = 'Import' AND TransType = 'ImportDetails'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by and (ppjk_id is not null or ppjk_id <> 0) )  = 3;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'TradingPpjkSiteDraft'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Draft Trading'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 3;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'TradingPpjkSiteProforma'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Proforma'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 3;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'TradingPpjkSiteActual'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Actual'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 3;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'TradingTenantProforma'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Proforma'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 5;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'TradingTenantActual'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Actual'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 5;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'TradingFinanceProforma'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Proforma'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 9;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'TradingFinanceActual'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Actual'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 9;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = TransType
                where TabName is null
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TabName",
                table: "M_Doc_attachment");
        }
    }
}
