using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.Ekb.Application.Contracts.Attachments;

/// <summary>
/// DTO for file upload metadata
/// </summary>
public class FileUploadDto
{
    /// <summary>
    /// Optional description of the file
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Optional reference ID that this file is associated with
    /// </summary>
    [Required]
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// Optional reference type that this file is associated with
    /// </summary>
    [StringLength(50)]
    [Required]
    public string? ReferenceType { get; set; }

    /// <summary>
    /// Document type for path generation
    /// </summary>
    [StringLength(50)]
    [Required]
    public string? DocType { get; set; }

    /// <summary>
    /// Transaction type for path generation
    /// </summary>
    [StringLength(50)]
    [Required]
    public string? TransType { get; set; }

    /// <summary>
    /// TypePa for path generation (optional)
    /// </summary>
    [StringLength(20)]
    public string? TypePa { get; set; }
    [Required]
    public int? DocumentReferenceId { get; set; }

    [Required]
    public string? TabName { get; set; }
}