using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.Ekb.Application.Contracts.Attachments;

/// <summary>
/// DTO for file upload with base64 encoded content
/// </summary>
public class FileUploadInputDto
{
    /// <summary>
    /// The name of the file
    /// </summary>
    [Required]
    [StringLength(255)]
    public string FileName { get; set; } = null!;

    /// <summary>
    /// The content type of the file
    /// </summary>
    [Required]
    [StringLength(255)]
    public string ContentType { get; set; } = null!;

    /// <summary>
    /// The file content as base64 encoded string
    /// </summary>
    [Required]
    public byte[] FileContent { get; set; } = null!;

    /// <summary>
    /// Optional description of the file
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Optional reference ID that this file is associated with
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// Optional reference type that this file is associated with
    /// </summary>
    [StringLength(50)]
    public string? ReferenceType { get; set; }
}