using Imip.Ekb.Master.ItemClassifications;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbItemClassificationClassification : IEntityTypeConfiguration<ItemClassification>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbItemClassificationClassification(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<ItemClassification> b)
    {
        b.ToTable("M_Classification", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        b.Property(x => x.Category)
            .HasColumnType("bigint");
    }
}