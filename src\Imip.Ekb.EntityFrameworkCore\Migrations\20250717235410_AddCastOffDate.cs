﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class AddCastOffDate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "AsideDate",
                table: "THEXP",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CastOfDate",
                table: "THEXP",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AsideDate",
                table: "T_MDOC_Header",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CastOfDate",
                table: "T_MDOC_Header",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AsideDate",
                table: "L_master",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CastOfDate",
                table: "L_master",
                type: "datetime2",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AsideDate",
                table: "THEXP");

            migrationBuilder.DropColumn(
                name: "CastOfDate",
                table: "THEXP");

            migrationBuilder.DropColumn(
                name: "AsideDate",
                table: "T_MDOC_Header");

            migrationBuilder.DropColumn(
                name: "CastOfDate",
                table: "T_MDOC_Header");

            migrationBuilder.DropColumn(
                name: "AsideDate",
                table: "L_master");

            migrationBuilder.DropColumn(
                name: "CastOfDate",
                table: "L_master");
        }
    }
}
