{
    "version": "0.2.0",
    "configurations": [
        {
            // Use IntelliSense to find out which attributes exist for C# debugging
            // Use hover for the description of the existing attributes
            // For further information visit https://github.com/dotnet/vscode-csharp/blob/main/debugger-launchjson.md.
            "name": ".NET Core Launch (web)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            // If you have changed target frameworks, make sure to update the program path.
            "program": "${workspaceFolder}/src/Imip.Ekb.Web/bin/Debug/net9.0/Imip.Ekb.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Imip.Ekb.Web",
            "stopAtEntry": false,
            // Enable launching a web browser when ASP.NET Core starts. For more information: https://aka.ms/VSCode-CS-LaunchJson-WebBrowser
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "ASPNETCORE_URLS": "http://localhost:5001"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
            "justMyCode": true // Enable Just My Code
        },
        {
            "name": "Launch DbMigrator",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/src/Imip.Ekb.DbMigrator/bin/Debug/net9.0/Imip.Ekb.DbMigrator.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Imip.Ekb.DbMigrator",
            "stopAtEntry": false,
            "console": "integratedTerminal",
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "justMyCode": true // Enable Just My Code
        },
        {
            "name": ".NET Core Attach",
            "type": "coreclr",
            "request": "attach"
        }
    ]
}