using System;
using System.ComponentModel.DataAnnotations;
using Imip.Ekb.Master.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.BoundedZone.ExportVessels;

public class ExportVesselProjectionDto : AuditedEntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public int DocEntry { get; set; }
    public string DocNum { get; set; }
    public DateOnly PostingDate { get; set; }
    public string? VesselName { get; set; }
    public string? Shipment { get; set; }
    public DateTime VesselArrival { get; set; }
    public DateTime? VesselDeparture { get; set; }
    public string? PortOrigin { get; set; }
    public string? DestinationPort { get; set; }
    public string? Voyage { get; set; }
    public decimal? GrossWeight { get; set; }
    public string? DocStatus { get; set; }
    public string? Status { get; set; }
    public string? Remarks { get; set; }
    public string? DocType { get; set; }
    public DateTime? BerthingDate { get; set; }
    public DateTime? AnchorageDate { get; set; }
    public DateOnly? ReportDate { get; set; }
    public DateTime? UnloadingDate { get; set; }
    public DateTime? FinishUnloadingDate { get; set; }
    public decimal? GrtWeight { get; set; }
    public string? InvoiceStatus { get; set; }
    public string? StatusBms { get; set; }
    public Guid? JettyId { get; set; }
    public Guid? VesselId { get; set; }
    public Guid? MasterAgentId { get; set; }
    public Guid? MasterTradingId { get; set; }
    public Guid? MasterSurveyorId { get; set; }
    /// <summary>
    /// Aside date and time
    /// </summary>
    public DateTime? AsideDate { get; set; }

    /// <summary>
    /// Cast off date and time
    /// </summary>
    public DateTime? CastOfDate { get; set; }
    public Guid? PortOriginId { get; set; }
    public Guid? DestinationPortId { get; set; }
    public virtual AgentProjectionDto? MasterAgent { get; set; }
    public virtual TradingProjectionDto? MasterTrading { get; set; }
    public virtual SurveyorProjectionDto? MasterSurveyor { get; set; }
    public virtual JettyProjectionDto? MasterJetty { get; set; }
    public virtual CargoProjectionDto? Vessel { get; set; }
    public virtual PortOfOriginProjectionDto? MasterPortOrigin { get; set; }
    public virtual DestinationPortProjectionDto? MasterDestinationPort { get; set; }
}