using Imip.Ekb.BoundedZone;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbZoneDetailConfiguration : IEntityTypeConfiguration<ZoneDetail>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbZoneDetailConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<ZoneDetail> b)
    {
        b.ToTable("T_MDOC", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions
        b.HasIndex(x => x.DocNum);
        b.HasIndex(x => x.DocType);
        b.<PERSON>nde<PERSON>(x => x.Tenant<PERSON>ey);
        b.<PERSON>Index(x => x.BcTypeKey);

        b.Has<PERSON>(x => x.Tenant)
            .WithMany(z => z.VesselTransactions)
            .HasForeignKey(x => x.TenantId)
            .HasPrincipalKey(z => z.Id)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        b.HasOne(x => x.MasterAgent)
            .WithMany(z => z.VesselTransactions)
            .HasForeignKey(x => x.AgentId)
            .HasPrincipalKey(z => z.Id)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        b.HasOne(x => x.ExportClassification)
            .WithMany(z => z.VesselTransactions)
            .HasForeignKey(x => x.MasterExportClassificationId)
            .HasPrincipalKey(z => z.Id)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        b.HasOne(x => x.BcType)
            .WithMany(z => z.VesselTransactions)
            .HasForeignKey(x => x.BcTypeId)
            .HasPrincipalKey(z => z.Id)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        b.HasOne(x => x.BusinessPartner)
           .WithMany(x => x.VesselTransactions)
           .HasForeignKey(x => x.BusinessPartnerId)
           .HasPrincipalKey(z => z.Id)
           .OnDelete(DeleteBehavior.NoAction)
           .IsRequired(false);

        // Configure vessel relationships using HeaderId (GUID) for foreign key
        // Keep DocEntry as PK, but use HeaderId for relationships to header entities
        // The foreign key is on ZoneDetail.HeaderId, not on vessel tables
        b.HasOne(x => x.ExportVessel)
            .WithMany(x => x.Items)
            .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.ImportVessel)
            .WithMany(x => x.Items)
            .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.LocalVessel)
            .WithMany(x => x.Items)
            .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.TradingVessel)
            .WithMany(x => x.Items)
            .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        // Configure properties
        b.Property(e => e.Container).HasDefaultValueSql("('0')");
        b.Property(e => e.DecreaseValue).HasDefaultValueSql("('0')");
        b.Property(e => e.DecreaseValuePph).HasDefaultValueSql("('0')");
        b.Property(e => e.DecreaseValuePpn).HasDefaultValueSql("('0')");
        b.Property(e => e.Deleted).HasDefaultValue("N");
        b.Property(e => e.DocType).HasDefaultValue("Import");
        b.Property(e => e.EBillingNo).IsFixedLength();
        b.Property(e => e.EsignDecimal).HasDefaultValueSql("('3')");
        b.Property(e => e.ExportClassificationId).IsFixedLength();
        b.Property(e => e.IncreaseValue).HasDefaultValueSql("('0')");
        b.Property(e => e.IncreaseValuePph).HasDefaultValueSql("('0')");
        b.Property(e => e.IncreaseValuePpn).HasDefaultValueSql("('0')");
        b.Property(e => e.IsChange).HasDefaultValue("N");
        b.Property(e => e.IsFeOri).HasDefaultValue("N");
        b.Property(e => e.IsFeSend).HasDefaultValue("N");
        b.Property(e => e.IsOriginal).HasDefaultValue("N");
        b.Property(e => e.IsParent).HasDefaultValue("N");
        b.Property(e => e.IsScan).HasDefaultValue("N");
        b.Property(e => e.IsSend).HasDefaultValue("N");

        b.Property(x => x.CargoId)
            .HasColumnType("bigint");
        b.Property(x => x.BargeId)
            .HasColumnType("bigint");
        b.Property(x => x.SurveyorId)
            .HasColumnType("bigint");
        b.Property(x => x.WarehouseId)
            .HasColumnType("bigint");
        b.Property(x => x.RepresentativeId)
            .HasColumnType("bigint");
        b.Property(x => x.InvoiceDetailId)
            .HasColumnType("bigint");
    }
}