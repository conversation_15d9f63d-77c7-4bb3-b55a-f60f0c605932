using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.Notuls;
using Imip.Ekb.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.Ekb.Repositories;

public class NotulHeaderRepository : EfCoreRepository<EkbDbContext, NotulHeader, Guid>, INotulHeaderRepository
{
    public NotulHeaderRepository(IDbContextProvider<EkbDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }
    // Simple repository methods only
    public virtual async Task<IQueryable<NotulHeader>> GetQueryableWithIncludesAsync()
    {
        return (await GetQueryableAsync()).AsNoTracking();
    }
}