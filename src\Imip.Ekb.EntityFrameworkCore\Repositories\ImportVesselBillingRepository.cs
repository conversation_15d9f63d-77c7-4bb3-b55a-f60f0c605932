using Imip.Ekb.Billing.ImportVesselBillings;
using Imip.Ekb.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.Ekb.Repositories;
public class ImportVesselBillingRepository : EfCoreRepository<EkbDbContext, ImportVesselBilling, Guid>, IImportVesselBillingRepository
{
    public ImportVesselBillingRepository(IDbContextProvider<EkbDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

    // Simple repository methods only
    public virtual async Task<IQueryable<ImportVesselBilling>> GetQueryableWithIncludesAsync()
    {
        return (await GetQueryableAsync())
            .AsNoTracking()
            .Include(x => x.MasterJetty)
            .Where(x => !x.IsDeleted);
    }

    public virtual async Task<int> CountAsync()
    {
        var query = await GetQueryableAsync();

        // Use a more efficient count query
        return await query
            .Where(x => !x.IsDeleted)
            .CountAsync();
    }
}