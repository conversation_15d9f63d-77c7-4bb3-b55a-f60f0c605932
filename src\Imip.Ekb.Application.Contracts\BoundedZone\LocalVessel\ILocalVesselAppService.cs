using System;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.LocalVessel;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.BoundedZone.LocalVessels;

public interface ILocalVesselAppService :
    ICrudAppService<
        LocalVesselDto,
        Guid,
        QueryParametersDto,
        CreateUpdateLocalVesselDto>
{
    Task<PagedResultDto<LocalVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters);
    Task<LocalVesselWithItemsDto> GetWithItemsAsync(Guid id);
}