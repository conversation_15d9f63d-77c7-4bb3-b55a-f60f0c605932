using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.BoundedZone;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Attachments;

[Table("M_Doc_attachment")]
public class DocAttachment : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    [Column("name")]
    [StringLength(255)]
    public string FileName { get; set; } = null!;

    [StringLength(255)]
    [Column("Type")]
    public string ContentType { get; set; } = null!;

    [Column("Created_by")]
    [StringLength(255)]
    public string CreatedBy { get; set; } = null!;

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [Column("M_doc_key")]
    public int? DocumentReferenceId { get; set; }

    /// <summary>
    /// Optional reference ID that this file is associated with
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// The blob name used to store the file in the blob storage
    /// </summary>
    public string? BlobName { get; set; }

    /// <summary>
    /// Optional description of the file
    /// </summary>
    public string? Description { get; set; }

    [StringLength(50)]
    public string DocType { get; set; } = null!;

    [StringLength(50)]
    public string TransType { get; set; } = null!;

    [StringLength(100)]
    public string? SecretKey { get; set; }

    [Column("TypePA")]
    [StringLength(20)]
    public string? TypePa { get; set; }

    [StringLength(255)]
    [Column("Path")]
    public string? FilePath { get; set; }

    [StringLength(255)]
    public string? NameNoExt { get; set; }

    [StringLength(255)]
    public string? Size { get; set; }

    [StringLength(255)]
    public string? Extension { get; set; }

    [StringLength(255)]
    public string? CreatedName { get; set; }

    [StringLength(255)]
    public string? TabName { get; set; }

    public virtual ZoneDetail? ZoneDetail { get; set; }
}
