using Imip.Ekb.Billing.LocalVesselBillings;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbBillingItemConfiguration : IEntityTypeConfiguration<BillingItem>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbBillingItemConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<BillingItem> b)
    {
        b.ToTable("BEXP", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        b.HasOne(x => x.ExportVesselBilling)
            .WithMany(x => x.Items)
            .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.<PERSON>(x => x.ImportVesselBilling)
            .WithMany(x => x.Items)
            .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.LocalVesselBilling)
            .WithMany(x => x.Items)
            .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.ZoneDetail)
            .WithMany()
            .HasForeignKey(x => x.ZoneDetailId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterTenant)
            .WithMany(z => z.BillingItems)
            .HasForeignKey(x => x.MasterTenantId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.BcType)
            .WithMany(z => z.BillingItems)
            .HasForeignKey(x => x.BcTypeId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.MasterJetty)
            .WithMany(x => x.BillingItems)
            .HasForeignKey(x => x.JettyId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);

        b.HasOne(x => x.BusinessPartner)
           .WithMany(x => x.BillingItems)
           .HasForeignKey(x => x.BusinessPartnerId)
           .HasPrincipalKey(z => z.Id)
           .IsRequired(false)
           .OnDelete(DeleteBehavior.NoAction);
    }
}
