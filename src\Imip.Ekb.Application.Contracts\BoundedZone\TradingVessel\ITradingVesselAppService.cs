using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.BoundedZone.TradingVessels;

public interface ITradingVesselAppService :
    ICrudAppService<
        TradingVesselDto,
        Guid,
        QueryParametersDto,
        CreateUpdateTradingVesselDto>
{
    Task<TradingVesselWithItemsDto> GetWithItemsAsync(Guid id);
    Task<List<VesselItemDto>> GetItemsAsync(int docEntry);
    Task<PagedResultDto<TradingVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters);
}