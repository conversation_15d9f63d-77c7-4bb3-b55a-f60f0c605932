using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.BoundedZone.ExportVessels;
using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.BoundedZone.LocalVessels;
using Imip.Ekb.Master.Cargos;
using Imip.Ekb.Master.Jetties;
using Imip.Ekb.Master.Tenants;
using Imip.Ekb.Attachments;
using Riok.Mapperly.Abstractions;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class VesselMapper : IMapperlyMapper
{
    // Import Vessel mappings
    [MapProperty(nameof(ImportVessel.Id), nameof(VesselHeaderDto.Id))]
    [MapProperty(nameof(ImportVessel.DocEntry), nameof(VesselHeaderDto.DocEntry))]
    [MapProperty(nameof(ImportVessel.DestinationPort), nameof(VesselHeaderDto.DestinationPort))]
    [MapProperty(nameof(ImportVessel.BerthingDate), nameof(VesselHeaderDto.BerthingDate))]
    [MapProperty(nameof(ImportVessel.AnchorageDate), nameof(VesselHeaderDto.AnchorageDate))]
    [MapProperty(nameof(ImportVessel.UnloadingDate), nameof(VesselHeaderDto.UnloadingDate))]
    [MapProperty(nameof(ImportVessel.FinishUnloadingDate), nameof(VesselHeaderDto.FinishUnloadingDate))]
    [MapProperty(nameof(ImportVessel.GrtWeight), nameof(VesselHeaderDto.GrtWeight))]
    [MapProperty(nameof(ImportVessel.AgentName), nameof(VesselHeaderDto.AgentName))]
    public partial VesselHeaderDto MapImportVesselToHeaderDto(ImportVessel entity);

    // Export Vessel mappings
    [MapProperty(nameof(ExportVessel.Id), nameof(VesselHeaderDto.Id))]
    [MapProperty(nameof(ExportVessel.DocEntry), nameof(VesselHeaderDto.DocEntry))]
    [MapProperty(nameof(ExportVessel.PortOrigin), nameof(VesselHeaderDto.PortOrigin))]
    [MapProperty(nameof(ExportVessel.DestinationPort), nameof(VesselHeaderDto.DestinationPort))]
    [MapProperty(nameof(ExportVessel.BerthingDate), nameof(VesselHeaderDto.BerthingDate))]
    [MapProperty(nameof(ExportVessel.AnchorageDate), nameof(VesselHeaderDto.AnchorageDate))]
    [MapProperty(nameof(ExportVessel.UnloadingDate), nameof(VesselHeaderDto.UnloadingDate))]
    [MapProperty(nameof(ExportVessel.FinishUnloadingDate), nameof(VesselHeaderDto.FinishUnloadingDate))]
    [MapProperty(nameof(ExportVessel.GrtWeight), nameof(VesselHeaderDto.GrtWeight))]
    [MapProperty(nameof(ExportVessel.AgentName), nameof(VesselHeaderDto.AgentName))]
    public partial VesselHeaderDto MapExportVesselToHeaderDto(ExportVessel entity);

    // Local Vessel mappings
    [MapProperty(nameof(LocalVessel.Id), nameof(VesselHeaderDto.Id))]
    [MapProperty(nameof(LocalVessel.DocEntry), nameof(VesselHeaderDto.DocEntry))]
    [MapProperty(nameof(LocalVessel.PortOrigin), nameof(VesselHeaderDto.PortOrigin))]
    [MapProperty(nameof(LocalVessel.DestinationPort), nameof(VesselHeaderDto.DestinationPort))]
    [MapProperty(nameof(LocalVessel.BerthingDate), nameof(VesselHeaderDto.BerthingDate))]
    [MapProperty(nameof(LocalVessel.AnchorageDate), nameof(VesselHeaderDto.AnchorageDate))]
    [MapProperty(nameof(LocalVessel.UnloadingDate), nameof(VesselHeaderDto.UnloadingDate))]
    [MapProperty(nameof(LocalVessel.FinishUnloadingDate), nameof(VesselHeaderDto.FinishUnloadingDate))]
    [MapProperty(nameof(LocalVessel.GrtWeight), nameof(VesselHeaderDto.GrtWeight))]
    [MapProperty(nameof(LocalVessel.AgentName), nameof(VesselHeaderDto.AgentName))]
    public partial VesselHeaderDto MapLocalVesselToHeaderDto(LocalVessel entity);

    // ZoneDetail to VesselItemDto mapping
    [MapProperty(nameof(ZoneDetail.Id), nameof(VesselItemDto.Id))]
    [MapProperty(nameof(ZoneDetail.DocEntry), nameof(VesselItemDto.DocEntry))]
    [MapProperty(nameof(ZoneDetail.DocNum), nameof(VesselItemDto.DocNum))]
    [MapProperty(nameof(ZoneDetail.ItemName), nameof(VesselItemDto.ItemName))]
    [MapProperty(nameof(ZoneDetail.ItemQty), nameof(VesselItemDto.ItemQty))]
    [MapProperty(nameof(ZoneDetail.UnitQty), nameof(VesselItemDto.UnitQty))]
    [MapProperty(nameof(ZoneDetail.Cargo), nameof(VesselItemDto.Cargo))]
    [MapProperty(nameof(ZoneDetail.Shipment), nameof(VesselItemDto.Shipment))]
    [MapProperty(nameof(ZoneDetail.Remarks), nameof(VesselItemDto.Remarks))]
    public partial VesselItemDto MapZoneDetailToItemDto(ZoneDetail entity);

    // Tenant mapping for VesselItemDto
    [MapProperty(nameof(MasterTenant.Name), nameof(VesselItemDto.TenantName))]
    public partial VesselItemDto MapTenantToVesselItemDto(MasterTenant tenant);

    // New mapping for TenantShortDto
    [MapProperty(nameof(MasterTenant.Id), nameof(TenantShortDto.Id))]
    [MapProperty(nameof(MasterTenant.DocEntry), nameof(TenantShortDto.DocEntry))]
    [MapProperty(nameof(MasterTenant.Name), nameof(TenantShortDto.Name))]
    [MapProperty(nameof(MasterTenant.FullName), nameof(TenantShortDto.FullName))]
    public partial TenantShortDto MapTenantToTenantShortDto(MasterTenant tenant);

    // New mapping for CargoShortDto
    [MapProperty(nameof(Cargo.Id), nameof(CargoShortDto.Id))]
    [MapProperty(nameof(Cargo.DocEntry), nameof(CargoShortDto.DocEntry))]
    [MapProperty(nameof(Cargo.Name), nameof(CargoShortDto.Name))]
    [MapProperty(nameof(Cargo.Alias), nameof(CargoShortDto.Alias))]
    [MapProperty(nameof(Cargo.Type), nameof(CargoShortDto.Type))]
    [MapProperty(nameof(Cargo.GrossWeight), nameof(CargoShortDto.GrossWeight))]
    public partial CargoShortDto MapCargoToCargoShortDto(Cargo cargo);

    // New mapping for JettyShortDto
    [MapProperty(nameof(Jetty.Id), nameof(JettyShortDto.Id))]
    [MapProperty(nameof(Jetty.DocEntry), nameof(JettyShortDto.DocEntry))]
    [MapProperty(nameof(Jetty.Name), nameof(JettyShortDto.Name))]
    [MapProperty(nameof(Jetty.Alias), nameof(JettyShortDto.Alias))]
    [MapProperty(nameof(Jetty.Port), nameof(JettyShortDto.Port))]
    [MapProperty(nameof(Jetty.Max), nameof(JettyShortDto.Max))]
    public partial JettyShortDto MapJettyToJettyShortDto(Jetty jetty);

    // DocAttachment mapping
    [MapProperty(nameof(DocAttachment.Id), nameof(DocAttachmentSortDto.Id))]
    [MapProperty(nameof(DocAttachment.DocType), nameof(DocAttachmentSortDto.DocType))]
    [MapProperty(nameof(DocAttachment.TransType), nameof(DocAttachmentSortDto.TransType))]
    [MapProperty(nameof(DocAttachment.Description), nameof(DocAttachmentSortDto.Description))]
    [MapProperty(nameof(DocAttachment.BlobName), nameof(DocAttachmentSortDto.BlobName))]
    [MapProperty(nameof(DocAttachment.ReferenceId), nameof(DocAttachmentSortDto.ReferenceId))]
    [MapProperty(nameof(DocAttachment.FileName), nameof(DocAttachmentSortDto.FileName))]
    [MapProperty(nameof(DocAttachment.FilePath), nameof(DocAttachmentSortDto.StreamUrl))]
    public partial DocAttachmentSortDto MapDocAttachmentToDto(DocAttachment entity);

    // List mappings
    public partial List<VesselHeaderDto> MapImportVesselListToHeaderDtoList(List<ImportVessel> entities);
    public partial List<VesselHeaderDto> MapExportVesselListToHeaderDtoList(List<ExportVessel> entities);
    public partial List<VesselHeaderDto> MapLocalVesselListToHeaderDtoList(List<LocalVessel> entities);
    public partial List<VesselItemDto> MapZoneDetailListToItemDtoList(List<ZoneDetail> entities);

    // Custom mapping methods
    public VesselHeaderDto MapImportVesselToHeaderDtoWithType(ImportVessel entity)
    {
        var dto = MapImportVesselToHeaderDto(entity);
        dto.VesselType = "Import";
        return dto;
    }

    public VesselHeaderDto MapExportVesselToHeaderDtoWithType(ExportVessel entity)
    {
        var dto = MapExportVesselToHeaderDto(entity);
        dto.VesselType = "Export";
        return dto;
    }

    public VesselHeaderDto MapLocalVesselToHeaderDtoWithType(LocalVessel entity)
    {
        var dto = MapLocalVesselToHeaderDto(entity);
        dto.VesselType = entity.VesselType == "IN" ? "LocalIn" : "LocalOut";
        return dto;
    }

    public VesselItemDto MapZoneDetailToItemDtoWithType(ZoneDetail entity, string vesselType)
    {
        var dto = MapZoneDetailToItemDto(entity);
        dto.VesselType = vesselType;
        return dto;
    }
}