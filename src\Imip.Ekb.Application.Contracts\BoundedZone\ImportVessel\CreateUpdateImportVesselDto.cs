using System;
using System.Collections.Generic;
using Imip.Ekb.BoundedZone.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.BoundedZone.ImportVessel;

public class CreateUpdateImportVesselDto : AuditedEntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public int? DocNum { get; set; }
    public string? Bp { get; set; }
    public string? VesselName { get; set; }
    public string? Shipment { get; set; }
    public string? ShipmentNo { get; set; }
    public DateTime? VesselArrival { get; set; }
    public string CreatedBy { get; set; } = null!;
    public string? UpdatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public DateOnly? PostingDate { get; set; }
    public string? Color { get; set; }
    public string? Flags { get; set; }
    public string? Remarks { get; set; }
    public string? Status { get; set; }
    public string IsLocked { get; set; } = null!;
    public string IsChange { get; set; } = null!;
    public string TransType { get; set; } = null!;
    public string DocType { get; set; } = null!;
    public int? BcType { get; set; }
    public string? PortOrigin { get; set; }
    public DateOnly? EmailToPpjk { get; set; }
    public string? MatchKey { get; set; }
    public string? Voyage { get; set; }
    public string Deleted { get; set; } = null!;
    public string DocStatus { get; set; } = null!;
    public decimal GrossWeight { get; set; }
    public string? VesselFlag { get; set; }
    public DateTime? VesselDeparture { get; set; }
    public string? VesselStatus { get; set; }
    public int? Jetty { get; set; }
    public string? DestinationPort { get; set; }
    public DateTime? BerthingDate { get; set; }
    public DateTime? AnchorageDate { get; set; }
    public string? Type { get; set; }
    public DateTime? JettyUpdate { get; set; }
    public DateOnly? ReportDate { get; set; }
    public DateTime? UnloadingDate { get; set; }
    public DateTime? FinishUnloadingDate { get; set; }
    public decimal? GrtWeight { get; set; }
    public string? InvoiceStatus { get; set; }
    public long? AgentId { get; set; }
    public string? AgentName { get; set; }
    public string StatusBms { get; set; } = null!;
    public long? SurveyorId { get; set; }
    public long? TradingId { get; set; }
    public Guid? JettyId { get; set; }
    public Guid? VesselId { get; set; }
    public Guid? MasterAgentId { get; set; }
    public Guid? MasterTradingId { get; set; }
    public Guid? MasterSurveyorId { get; set; }
    /// <summary>
    /// Aside date and time
    /// </summary>
    public DateTime? AsideDate { get; set; }

    /// <summary>
    /// Cast off date and time
    /// </summary>
    public DateTime? CastOfDate { get; set; }
    public Guid? PortOriginId { get; set; }
    public Guid? DestinationPortId { get; set; }


    // public List<CreateUpdateZoneDetailDto>? Items { get; set; }
    public List<CreateUpdateVesselItemDto>? Items { get; set; }
}