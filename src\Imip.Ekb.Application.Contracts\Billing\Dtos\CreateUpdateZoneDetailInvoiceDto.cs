using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.BoundedZone.Dtos;

public class CreateUpdateZoneDetailInvoiceDto : EntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public int? DocNum { get; set; }
    public string? NoInv { get; set; }
    public DateOnly? DateInv { get; set; }
    public string? Vendor { get; set; }
    public decimal? Value { get; set; }
    public string CreatedBy { get; set; } = null!;
    public string? UpdatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? Flags { get; set; }
    public string? ItemName { get; set; }
    public decimal Qty { get; set; }
    public string DocType { get; set; } = null!;
    public string TransType { get; set; } = null!;
    public string IsScan { get; set; } = null!;
    public string IsOriginal { get; set; } = null!;
    public string IsSend { get; set; } = null!;
    public string IsFeOri { get; set; } = null!;
    public string IsFeSend { get; set; } = null!;
    public string? SecretKey { get; set; }
    public string? Currency { get; set; }
    public string? FormE { get; set; }
    public decimal? ItemQty { get; set; }
    public string? UnitQty { get; set; }
    public decimal? GrossWeight { get; set; }
    public string? UnitWeight { get; set; }
    public string? MatchKey { get; set; }
    public string Deleted { get; set; } = null!;
    public string? ParentKey { get; set; }
    public int? Blkey { get; set; }
    public DateTime? DeletedAt { get; set; }
    public string? DeleteBy { get; set; }
    public string? OpenDate { get; set; }
    public string? UpdateDate { get; set; }
    public string? PackingList { get; set; }
    public decimal? Fob { get; set; }
    public decimal? CostOfRepair { get; set; }
    public string? InvoiceImportNo { get; set; }
    public DateOnly? InvoiceImportDate { get; set; }
    public Guid? ZoneDetailId { get; set; }
}