﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.Master.Agents;
using Imip.Ekb.Master.Cargos;
using Imip.Ekb.Master.DestinationPorts;
using Imip.Ekb.Master.Jetties;
using Imip.Ekb.Master.PortOfLoadings;
using Imip.Ekb.Master.Surveyors;
using Imip.Ekb.Master.Tradings;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.BoundedZone.ImportVessels;

[Table("T_MDOC_Header")]
public class ImportVessel : FullAuditedAggregateRoot<Guid>
{
    [Key]
    [Column("DocEntry")]
    public int DocEntry { get; set; }

    public int? DocNum { get; set; }

    [Column("BP")]
    [StringLength(255)]
    public string? Bp { get; set; }

    [Column("Cargo")]
    [StringLength(255)]
    public string? VesselName { get; set; }

    [Column("Shipement")]
    [StringLength(255)]
    public string? Shipment { get; set; }

    [Column("Shipement_no")]
    [StringLength(255)]
    public string? ShipmentNo { get; set; }

    [Column("Vessel_arrive")]
    public DateTime? VesselArrival { get; set; }

    [Column("Created_by")]
    [StringLength(255)]
    public string CreatedBy { get; set; } = null!;

    [Column("Updated_by")]
    [StringLength(255)]
    public string? UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    public DateOnly? PostingDate { get; set; }

    [StringLength(255)]
    public string? Color { get; set; }

    [StringLength(10)]
    public string? Flags { get; set; }

    public string? Remarks { get; set; }

    [Column("status")]
    public string? Status { get; set; }

    [Column("isLocked")]
    [StringLength(255)]
    public string IsLocked { get; set; } = null!;

    [Column("isChange")]
    [StringLength(255)]
    public string IsChange { get; set; } = null!;

    [StringLength(255)]
    public string TransType { get; set; } = null!;

    [StringLength(100)]
    public string DocType { get; set; } = null!;

    public int? BcType { get; set; }

    [Column("PortOfLoading")]
    [StringLength(255)]
    public string? PortOrigin { get; set; }

    [Column("EmailToPPJK")]
    public DateOnly? EmailToPpjk { get; set; }

    [StringLength(100)]
    public string? MatchKey { get; set; }

    [StringLength(255)]
    public string? Voyage { get; set; }

    [StringLength(10)]
    public string Deleted { get; set; } = null!;

    [StringLength(255)]
    public string DocStatus { get; set; } = null!;

    [Column(TypeName = "numeric(20, 5)")]
    public decimal GrossWeight { get; set; }

    [StringLength(255)]
    public string? VesselFlag { get; set; }

    public DateTime? VesselDeparture { get; set; }

    [StringLength(255)]
    public string? VesselStatus { get; set; }

    [Column("Jetty")]
    public int? Jetty { get; set; }

    [StringLength(200)]
    public string? DestinationPort { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? BerthingDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? AnchorageDate { get; set; }

    [StringLength(50)]
    public string? Type { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? JettyUpdate { get; set; }

    [Column("Reportdate")]
    public DateOnly? ReportDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? UnloadingDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? FinishUnloadingDate { get; set; }

    [Column(TypeName = "decimal(14, 4)")]
    public decimal? GrtWeight { get; set; }

    [StringLength(50)]
    public string? InvoiceStatus { get; set; }

    public long? AgentId { get; set; }

    [StringLength(255)]
    public string? AgentName { get; set; }

    [Column("status_bms")]
    [StringLength(100)]
    public string StatusBms { get; set; } = null!;

    public long? SurveyorId { get; set; }

    public long? TradingId { get; set; }

    public Guid? JettyId { get; set; }
    public Guid? VesselId { get; set; }
    public Guid? MasterAgentId { get; set; }
    public Guid? MasterTradingId { get; set; }
    public Guid? MasterSurveyorId { get; set; }
    /// <summary>
    /// Aside date and time
    /// </summary>
    public DateTime? AsideDate { get; set; }

    /// <summary>
    /// Cast off date and time
    /// </summary>
    public DateTime? CastOfDate { get; set; }
    public Guid? PortOriginId { get; set; }
    public Guid? DestinationPortId { get; set; }

    public virtual Agent? MasterAgent { get; set; }
    public virtual Trading? MasterTrading { get; set; }
    public virtual Surveyor? MasterSurveyor { get; set; }
    public virtual Jetty? MasterJetty { get; set; }
    public virtual Cargo? Vessel { get; set; }
    public virtual PortOfLoading? MasterPortOrigin { get; set; }
    public virtual DestinationPort? MasterDestinationPort { get; set; }
    public virtual ICollection<ZoneDetail>? Items { get; set; }
}
