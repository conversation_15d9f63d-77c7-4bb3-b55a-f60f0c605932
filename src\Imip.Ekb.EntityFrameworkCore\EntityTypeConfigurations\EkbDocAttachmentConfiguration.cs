using Imip.Ekb.Attachments;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbDocAttachmentConfiguration : IEntityTypeConfiguration<DocAttachment>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbDocAttachmentConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<DocAttachment> b)
    {
        b.ToTable("M_Doc_attachment", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        b.HasIndex(x => x.DocType);
        b.HasIndex(x => x.DocumentReferenceId);
        b.HasIndex(x => x.TransType);

        // Configure relationship with ZoneDetail using ReferenceId as foreign key
        b.HasOne(x => x.ZoneDetail)
            .WithMany(x => x.DocAttachment)
            .HasForeignKey(x => x.ReferenceId)
            .HasPrincipalKey(z => z.Id)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);
    }
}