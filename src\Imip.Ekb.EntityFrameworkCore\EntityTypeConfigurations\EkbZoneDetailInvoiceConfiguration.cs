using Imip.Ekb.BoundedZone.ImportVessels.ZoneDetailInvoice;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbZoneDetailInvoiceConfiguration : IEntityTypeConfiguration<ZoneDetailInvoice>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbZoneDetailInvoiceConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<ZoneDetailInvoice> b)
    {
        b.ToTable("T_MDOC_bl", EkbConsts.DbSchema);
        b.ConfigureByConvention();

        // Relationship: ZoneDetail has many ImportInvoices
        b.HasOne(x => x.ZoneDetail)
            .WithMany(z => z.ImportInvoices)
            .HasForeignKey(x => x.ZoneDetailId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);
    }
}