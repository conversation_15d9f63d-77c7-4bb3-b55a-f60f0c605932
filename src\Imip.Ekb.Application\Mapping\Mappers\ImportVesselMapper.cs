using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.BoundedZone.ImportVessels.Dtos;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Master.Cargos;
using Imip.Ekb.Master.Jetties;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;
using Imip.Ekb.BoundedZone.ImportVessel;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class ImportVesselMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(ImportVessel.Id), nameof(ImportVesselDto.Id))]
    [MapProperty(nameof(ImportVessel.DocEntry), nameof(ImportVesselDto.DocEntry))]
    [MapProperty(nameof(ImportVessel.DocNum), nameof(ImportVesselDto.DocNum))]
    [MapProperty(nameof(ImportVessel.Bp), nameof(ImportVesselDto.Bp))]
    [MapProperty(nameof(ImportVessel.VesselName), nameof(ImportVesselDto.VesselName))]
    [MapProperty(nameof(ImportVessel.Shipment), nameof(ImportVesselDto.Shipment))]
    [MapProperty(nameof(ImportVessel.ShipmentNo), nameof(ImportVesselDto.ShipmentNo))]
    [MapProperty(nameof(ImportVessel.VesselArrival), nameof(ImportVesselDto.VesselArrival))]
    [MapProperty(nameof(ImportVessel.CreatedBy), nameof(ImportVesselDto.CreatedBy))]
    [MapProperty(nameof(ImportVessel.UpdatedBy), nameof(ImportVesselDto.UpdatedBy))]
    [MapProperty(nameof(ImportVessel.CreatedAt), nameof(ImportVesselDto.CreatedAt))]
    [MapProperty(nameof(ImportVessel.UpdatedAt), nameof(ImportVesselDto.UpdatedAt))]
    [MapProperty(nameof(ImportVessel.PostingDate), nameof(ImportVesselDto.PostingDate))]
    [MapProperty(nameof(ImportVessel.Color), nameof(ImportVesselDto.Color))]
    [MapProperty(nameof(ImportVessel.Flags), nameof(ImportVesselDto.Flags))]
    [MapProperty(nameof(ImportVessel.Remarks), nameof(ImportVesselDto.Remarks))]
    [MapProperty(nameof(ImportVessel.Status), nameof(ImportVesselDto.Status))]
    [MapProperty(nameof(ImportVessel.IsLocked), nameof(ImportVesselDto.IsLocked))]
    [MapProperty(nameof(ImportVessel.IsChange), nameof(ImportVesselDto.IsChange))]
    [MapProperty(nameof(ImportVessel.TransType), nameof(ImportVesselDto.TransType))]
    [MapProperty(nameof(ImportVessel.DocType), nameof(ImportVesselDto.DocType))]
    [MapProperty(nameof(ImportVessel.BcType), nameof(ImportVesselDto.BcType))]
    [MapProperty(nameof(ImportVessel.PortOrigin), nameof(ImportVesselDto.PortOrigin))]
    [MapProperty(nameof(ImportVessel.EmailToPpjk), nameof(ImportVesselDto.EmailToPpjk))]
    [MapProperty(nameof(ImportVessel.MatchKey), nameof(ImportVesselDto.MatchKey))]
    [MapProperty(nameof(ImportVessel.Voyage), nameof(ImportVesselDto.Voyage))]
    [MapProperty(nameof(ImportVessel.Deleted), nameof(ImportVesselDto.Deleted))]
    [MapProperty(nameof(ImportVessel.DocStatus), nameof(ImportVesselDto.DocStatus))]
    [MapProperty(nameof(ImportVessel.GrossWeight), nameof(ImportVesselDto.GrossWeight))]
    [MapProperty(nameof(ImportVessel.VesselFlag), nameof(ImportVesselDto.VesselFlag))]
    [MapProperty(nameof(ImportVessel.VesselDeparture), nameof(ImportVesselDto.VesselDeparture))]
    [MapProperty(nameof(ImportVessel.VesselStatus), nameof(ImportVesselDto.VesselStatus))]
    [MapProperty(nameof(ImportVessel.Jetty), nameof(ImportVesselDto.Jetty))]
    [MapProperty(nameof(ImportVessel.DestinationPort), nameof(ImportVesselDto.DestinationPort))]
    [MapProperty(nameof(ImportVessel.BerthingDate), nameof(ImportVesselDto.BerthingDate))]
    [MapProperty(nameof(ImportVessel.AnchorageDate), nameof(ImportVesselDto.AnchorageDate))]
    [MapProperty(nameof(ImportVessel.Type), nameof(ImportVesselDto.Type))]
    [MapProperty(nameof(ImportVessel.JettyUpdate), nameof(ImportVesselDto.JettyUpdate))]
    [MapProperty(nameof(ImportVessel.ReportDate), nameof(ImportVesselDto.ReportDate))]
    [MapProperty(nameof(ImportVessel.UnloadingDate), nameof(ImportVesselDto.UnloadingDate))]
    [MapProperty(nameof(ImportVessel.FinishUnloadingDate), nameof(ImportVesselDto.FinishUnloadingDate))]
    [MapProperty(nameof(ImportVessel.GrtWeight), nameof(ImportVesselDto.GrtWeight))]
    [MapProperty(nameof(ImportVessel.InvoiceStatus), nameof(ImportVesselDto.InvoiceStatus))]
    [MapProperty(nameof(ImportVessel.AgentId), nameof(ImportVesselDto.AgentId))]
    [MapProperty(nameof(ImportVessel.AgentName), nameof(ImportVesselDto.AgentName))]
    [MapProperty(nameof(ImportVessel.StatusBms), nameof(ImportVesselDto.StatusBms))]
    [MapProperty(nameof(ImportVessel.SurveyorId), nameof(ImportVesselDto.SurveyorId))]
    [MapProperty(nameof(ImportVessel.TradingId), nameof(ImportVesselDto.TradingId))]
    [MapProperty(nameof(ImportVessel.JettyId), nameof(ImportVesselDto.JettyId))]
    [MapProperty(nameof(ImportVessel.VesselId), nameof(ImportVesselDto.VesselId))]
    public partial ImportVesselDto MapToDto(ImportVessel entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(ImportVessel.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(ImportVessel.DocEntry))] // Don't change existing DocEntry
    [MapperIgnoreTarget(nameof(ImportVessel.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(ImportVessel.CreatedAt))]
    [MapperIgnoreTarget(nameof(ImportVessel.CreationTime))]
    [MapperIgnoreTarget(nameof(ImportVessel.CreatorId))]
    [MapperIgnoreTarget(nameof(ImportVessel.LastModificationTime))]
    [MapperIgnoreTarget(nameof(ImportVessel.LastModifierId))]
    [MapperIgnoreTarget(nameof(ImportVessel.IsDeleted))]
    [MapperIgnoreTarget(nameof(ImportVessel.DeleterId))]
    [MapperIgnoreTarget(nameof(ImportVessel.DeletionTime))]
    [MapperIgnoreTarget(nameof(ImportVessel.ExtraProperties))]
    [MapperIgnoreTarget(nameof(ImportVessel.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterJetty))] // Navigation properties handled separately
    [MapperIgnoreTarget(nameof(ImportVessel.Vessel))]
    [MapperIgnoreTarget(nameof(ImportVessel.Items))]
    public partial void MapToEntity(CreateUpdateImportVesselDto dto, ImportVessel entity);

    // DTO to Entity mapping for creation
    [MapperIgnoreTarget(nameof(ImportVessel.Id))] // Will be set by EF Core
    [MapperIgnoreTarget(nameof(ImportVessel.DocEntry))] // Will be set by EF Core
    [MapperIgnoreTarget(nameof(ImportVessel.CreatedBy))] // Will be set by ABP
    [MapperIgnoreTarget(nameof(ImportVessel.CreatedAt))]
    [MapperIgnoreTarget(nameof(ImportVessel.CreationTime))]
    [MapperIgnoreTarget(nameof(ImportVessel.CreatorId))]
    [MapperIgnoreTarget(nameof(ImportVessel.LastModificationTime))]
    [MapperIgnoreTarget(nameof(ImportVessel.LastModifierId))]
    [MapperIgnoreTarget(nameof(ImportVessel.IsDeleted))]
    [MapperIgnoreTarget(nameof(ImportVessel.DeleterId))]
    [MapperIgnoreTarget(nameof(ImportVessel.DeletionTime))]
    [MapperIgnoreTarget(nameof(ImportVessel.ExtraProperties))]
    [MapperIgnoreTarget(nameof(ImportVessel.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterJetty))]
    [MapperIgnoreTarget(nameof(ImportVessel.Vessel))]
    [MapperIgnoreTarget(nameof(ImportVessel.Items))]
    public partial ImportVessel MapToEntity(CreateUpdateImportVesselDto dto);

    // Custom mapping methods for complex scenarios
    public ImportVessel CreateEntityWithId(CreateUpdateImportVesselDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (ImportVessel)Activator.CreateInstance(typeof(ImportVessel), true)!;

        // Set the ID using reflection
        var idProperty = typeof(ImportVessel).GetProperty("Id");
        idProperty?.SetValue(entity, id);

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<ImportVesselDto> MapToDtoList(List<ImportVessel> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<ImportVesselDto> MapToDtoEnumerable(IEnumerable<ImportVessel> entities);

    // Map with items included
    public ImportVesselWithItemsDto MapToDtoWithItems(ImportVessel entity, List<VesselItemDto> items)
    {
        var dto = MapToDto(entity);
        var withItemsDto = new ImportVesselWithItemsDto
        {
            Id = dto.Id,
            DocEntry = dto.DocEntry,
            DocNum = dto.DocNum,
            Bp = dto.Bp,
            VesselName = dto.VesselName,
            Shipment = dto.Shipment,
            ShipmentNo = dto.ShipmentNo,
            VesselArrival = dto.VesselArrival,
            CreatedBy = dto.CreatedBy,
            UpdatedBy = dto.UpdatedBy,
            CreatedAt = dto.CreatedAt,
            UpdatedAt = dto.UpdatedAt,
            PostingDate = dto.PostingDate,
            Color = dto.Color,
            Flags = dto.Flags,
            Remarks = dto.Remarks,
            Status = dto.Status,
            IsLocked = dto.IsLocked,
            IsChange = dto.IsChange,
            TransType = dto.TransType,
            DocType = dto.DocType,
            BcType = dto.BcType,
            PortOrigin = dto.PortOrigin,
            EmailToPpjk = dto.EmailToPpjk,
            MatchKey = dto.MatchKey,
            Voyage = dto.Voyage,
            Deleted = dto.Deleted,
            DocStatus = dto.DocStatus,
            GrossWeight = dto.GrossWeight,
            VesselFlag = dto.VesselFlag,
            VesselDeparture = dto.VesselDeparture,
            VesselStatus = dto.VesselStatus,
            Jetty = dto.Jetty,
            DestinationPort = dto.DestinationPort,
            BerthingDate = dto.BerthingDate,
            AnchorageDate = dto.AnchorageDate,
            Type = dto.Type,
            JettyUpdate = dto.JettyUpdate,
            ReportDate = dto.ReportDate,
            UnloadingDate = dto.UnloadingDate,
            FinishUnloadingDate = dto.FinishUnloadingDate,
            GrtWeight = dto.GrtWeight,
            InvoiceStatus = dto.InvoiceStatus,
            AgentId = dto.AgentId,
            AgentName = dto.AgentName,
            StatusBms = dto.StatusBms,
            SurveyorId = dto.SurveyorId,
            TradingId = dto.TradingId,
            JettyId = dto.JettyId,
            VesselId = dto.VesselId,
            MasterJetty = dto.MasterJetty,
            Vessel = dto.Vessel,
            Items = items
        };

        return withItemsDto;
    }
}