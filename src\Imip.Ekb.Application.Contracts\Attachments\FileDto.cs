using System;

namespace Imip.Ekb.Application.Contracts.Attachments;

/// <summary>
/// DTO for file download
/// </summary>
public class FileDto
{
    /// <summary>
    /// The name of the file
    /// </summary>
    public string FileName { get; set; } = null!;

    /// <summary>
    /// The content type of the file
    /// </summary>
    public string ContentType { get; set; } = null!;

    /// <summary>
    /// The file content as byte array
    /// </summary>
    public byte[] Content { get; set; } = null!;

    public FileDto(string fileName, string contentType, byte[] content)
    {
        FileName = fileName;
        ContentType = contentType;
        Content = content;
    }
}