﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class Added_VesselTableRelationship : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "MasterAgentId",
                table: "THEXP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MasterSurveyorId",
                table: "THEXP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MasterTradingId",
                table: "THEXP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MasterAgentId",
                table: "T_MDOC_Header",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MasterSurveyorId",
                table: "T_MDOC_Header",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MasterTradingId",
                table: "T_MDOC_Header",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MasterAgentId",
                table: "L_master",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MasterSurveyorId",
                table: "L_master",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MasterTradingId",
                table: "L_master",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "BusinessPartnerId",
                table: "BEXP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "JettyId",
                table: "BEXP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddUniqueConstraint(
                name: "AK_master_surveyors_Id",
                table: "master_surveyors",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_M_Agent_Id",
                table: "M_Agent",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_master_tradings_Id",
                table: "master_tradings",
                column: "Id");


            migrationBuilder.CreateIndex(
                name: "IX_THEXP_MasterAgentId",
                table: "THEXP",
                column: "MasterAgentId");

            migrationBuilder.CreateIndex(
                name: "IX_THEXP_MasterSurveyorId",
                table: "THEXP",
                column: "MasterSurveyorId");

            migrationBuilder.CreateIndex(
                name: "IX_THEXP_MasterTradingId",
                table: "THEXP",
                column: "MasterTradingId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_Header_MasterAgentId",
                table: "T_MDOC_Header",
                column: "MasterAgentId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_Header_MasterSurveyorId",
                table: "T_MDOC_Header",
                column: "MasterSurveyorId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_Header_MasterTradingId",
                table: "T_MDOC_Header",
                column: "MasterTradingId");

            migrationBuilder.CreateIndex(
                name: "IX_L_master_MasterAgentId",
                table: "L_master",
                column: "MasterAgentId");

            migrationBuilder.CreateIndex(
                name: "IX_L_master_MasterSurveyorId",
                table: "L_master",
                column: "MasterSurveyorId");

            migrationBuilder.CreateIndex(
                name: "IX_L_master_MasterTradingId",
                table: "L_master",
                column: "MasterTradingId");

            migrationBuilder.CreateIndex(
                name: "IX_BEXP_BusinessPartnerId",
                table: "BEXP",
                column: "BusinessPartnerId");

            migrationBuilder.CreateIndex(
                name: "IX_BEXP_JettyId",
                table: "BEXP",
                column: "JettyId");

            migrationBuilder.AddForeignKey(
                name: "FK_BEXP_M_BP_BusinessPartnerId",
                table: "BEXP",
                column: "BusinessPartnerId",
                principalTable: "M_BP",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BEXP_M_Jetty_JettyId",
                table: "BEXP",
                column: "JettyId",
                principalTable: "M_Jetty",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_L_master_M_Agent_MasterAgentId",
                table: "L_master",
                column: "MasterAgentId",
                principalTable: "M_Agent",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_L_master_master_surveyors_MasterSurveyorId",
                table: "L_master",
                column: "MasterSurveyorId",
                principalTable: "master_surveyors",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_L_master_master_tradings_MasterTradingId",
                table: "L_master",
                column: "MasterTradingId",
                principalTable: "master_tradings",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_Header_M_Agent_MasterAgentId",
                table: "T_MDOC_Header",
                column: "MasterAgentId",
                principalTable: "M_Agent",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_Header_master_surveyors_MasterSurveyorId",
                table: "T_MDOC_Header",
                column: "MasterSurveyorId",
                principalTable: "master_surveyors",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_Header_master_tradings_MasterTradingId",
                table: "T_MDOC_Header",
                column: "MasterTradingId",
                principalTable: "master_tradings",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_THEXP_M_Agent_MasterAgentId",
                table: "THEXP",
                column: "MasterAgentId",
                principalTable: "M_Agent",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_THEXP_master_surveyors_MasterSurveyorId",
                table: "THEXP",
                column: "MasterSurveyorId",
                principalTable: "master_surveyors",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_THEXP_master_tradings_MasterTradingId",
                table: "THEXP",
                column: "MasterTradingId",
                principalTable: "master_tradings",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BEXP_M_BP_BusinessPartnerId",
                table: "BEXP");

            migrationBuilder.DropForeignKey(
                name: "FK_BEXP_M_Jetty_JettyId",
                table: "BEXP");

            migrationBuilder.DropForeignKey(
                name: "FK_L_master_M_Agent_MasterAgentId",
                table: "L_master");

            migrationBuilder.DropForeignKey(
                name: "FK_L_master_master_surveyors_MasterSurveyorId",
                table: "L_master");

            migrationBuilder.DropForeignKey(
                name: "FK_L_master_master_tradings_MasterTradingId",
                table: "L_master");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_Header_M_Agent_MasterAgentId",
                table: "T_MDOC_Header");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_Header_master_surveyors_MasterSurveyorId",
                table: "T_MDOC_Header");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_Header_master_tradings_MasterTradingId",
                table: "T_MDOC_Header");

            migrationBuilder.DropForeignKey(
                name: "FK_THEXP_M_Agent_MasterAgentId",
                table: "THEXP");

            migrationBuilder.DropForeignKey(
                name: "FK_THEXP_master_surveyors_MasterSurveyorId",
                table: "THEXP");

            migrationBuilder.DropForeignKey(
                name: "FK_THEXP_master_tradings_MasterTradingId",
                table: "THEXP");

            migrationBuilder.DropIndex(
                name: "IX_THEXP_MasterAgentId",
                table: "THEXP");

            migrationBuilder.DropIndex(
                name: "IX_THEXP_MasterSurveyorId",
                table: "THEXP");

            migrationBuilder.DropIndex(
                name: "IX_THEXP_MasterTradingId",
                table: "THEXP");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_Header_MasterAgentId",
                table: "T_MDOC_Header");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_Header_MasterSurveyorId",
                table: "T_MDOC_Header");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_Header_MasterTradingId",
                table: "T_MDOC_Header");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_master_surveyors_Id",
                table: "master_surveyors");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_M_Agent_Id",
                table: "M_Agent");

            migrationBuilder.DropIndex(
                name: "IX_L_master_MasterAgentId",
                table: "L_master");

            migrationBuilder.DropIndex(
                name: "IX_L_master_MasterSurveyorId",
                table: "L_master");

            migrationBuilder.DropIndex(
                name: "IX_L_master_MasterTradingId",
                table: "L_master");

            migrationBuilder.DropIndex(
                name: "IX_BEXP_BusinessPartnerId",
                table: "BEXP");

            migrationBuilder.DropIndex(
                name: "IX_BEXP_JettyId",
                table: "BEXP");

            migrationBuilder.DropColumn(
                name: "MasterAgentId",
                table: "THEXP");

            migrationBuilder.DropColumn(
                name: "MasterSurveyorId",
                table: "THEXP");

            migrationBuilder.DropColumn(
                name: "MasterTradingId",
                table: "THEXP");

            migrationBuilder.DropColumn(
                name: "MasterAgentId",
                table: "T_MDOC_Header");

            migrationBuilder.DropColumn(
                name: "MasterSurveyorId",
                table: "T_MDOC_Header");

            migrationBuilder.DropColumn(
                name: "MasterTradingId",
                table: "T_MDOC_Header");

            migrationBuilder.DropColumn(
                name: "MasterAgentId",
                table: "L_master");

            migrationBuilder.DropColumn(
                name: "MasterSurveyorId",
                table: "L_master");

            migrationBuilder.DropColumn(
                name: "MasterTradingId",
                table: "L_master");

            migrationBuilder.DropColumn(
                name: "BusinessPartnerId",
                table: "BEXP");

            migrationBuilder.DropColumn(
                name: "JettyId",
                table: "BEXP");


        }
    }
}
