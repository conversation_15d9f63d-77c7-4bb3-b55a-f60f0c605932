﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.BoundedZone.LocalVessels;

public interface ILocalVesselRepository : IRepository<LocalVessel, Guid>
{
    Task<IQueryable<LocalVessel>> GetQueryableWithIncludesAsync();
    Task<IQueryable<LocalVessel>> GetQueryableWithItemsAsync();
    Task<LocalVessel?> GetQueryableWithItemsSplitAsync(Guid id);
    Task<LocalVessel?> GetWithItemsSplitCompiledAsync(Guid id);
    Task<int> CountAsync();
    Task<TProjection?> GetProjectedByIdAsync<TProjection>(Guid id, System.Linq.Expressions.Expression<Func<LocalVessel, TProjection>> selector);
}