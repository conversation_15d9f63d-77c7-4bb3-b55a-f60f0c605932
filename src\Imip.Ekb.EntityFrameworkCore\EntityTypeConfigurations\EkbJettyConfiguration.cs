using Imip.Ekb.Master.Jetties;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbJettyConfiguration : IEntityTypeConfiguration<Jetty>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbJettyConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<Jetty> b)
    {
        b.ToTable("M_Jetty", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        // Configure IsCustomArea property to handle integer to boolean conversion
        b.Property(x => x.IsCustomArea)
            .HasConversion(
                v => v == true ? 1 : 0,
                v => v == 1
            );
    }
}