using Imip.Ekb.Billing.ImportVesselBillings;
using Imip.Ekb.Billing.LocalVesselBillings.Dtos;
using Imip.Ekb.Billing.LocalVesselBillings.Interfaces;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class ImportVesselBillingMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(ImportVesselBilling.Id), nameof(ImportVesselBillingDto.Id))]
    [MapProperty(nameof(ImportVesselBilling.DocEntry), nameof(ImportVesselBillingDto.DocEntry))]
    [MapProperty(nameof(ImportVesselBilling.PortService), nameof(ImportVesselBillingDto.PortService))]
    [MapProperty(nameof(ImportVesselBilling.Status), nameof(ImportVesselBillingDto.Status))]
    [MapProperty(nameof(ImportVesselBilling.YearArrival), nameof(ImportVesselBillingDto.YearArrival))]
    [MapProperty(nameof(ImportVesselBilling.Jetty), nameof(ImportVesselBillingDto.Jetty))]
    [MapProperty(nameof(ImportVesselBilling.ImportId), nameof(ImportVesselBillingDto.ImportId))]
    [MapProperty(nameof(ImportVesselBilling.CreatedBy), nameof(ImportVesselBillingDto.CreatedBy))]
    [MapProperty(nameof(ImportVesselBilling.UpdatedBy), nameof(ImportVesselBillingDto.UpdatedBy))]
    [MapProperty(nameof(ImportVesselBilling.CreatedAt), nameof(ImportVesselBillingDto.CreatedAt))]
    [MapProperty(nameof(ImportVesselBilling.UpdatedAt), nameof(ImportVesselBillingDto.UpdatedAt))]
    [MapProperty(nameof(ImportVesselBilling.DocNum), nameof(ImportVesselBillingDto.DocNum))]
    [MapProperty(nameof(ImportVesselBilling.PostingDate), nameof(ImportVesselBillingDto.PostingDate))]
    [MapProperty(nameof(ImportVesselBilling.Remarks), nameof(ImportVesselBillingDto.Remarks))]
    [MapProperty(nameof(ImportVesselBilling.PeriodDate), nameof(ImportVesselBillingDto.PeriodDate))]
    [MapProperty(nameof(ImportVesselBilling.BillingNoteDate), nameof(ImportVesselBillingDto.BillingNoteDate))]
    [MapProperty(nameof(ImportVesselBilling.JettyId), nameof(ImportVesselBillingDto.JettyId))]
    public partial ImportVesselBillingDto MapToDto(ImportVesselBilling entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(ImportVesselBilling.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(ImportVesselBilling.DocEntry))] // Don't change existing DocEntry
    [MapperIgnoreTarget(nameof(ImportVesselBilling.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(ImportVesselBilling.CreatedAt))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.CreationTime))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.CreatorId))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.LastModificationTime))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.LastModifierId))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.IsDeleted))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.DeleterId))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.DeletionTime))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.ExtraProperties))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.MasterJetty))] // Navigation properties handled separately
    [MapperIgnoreTarget(nameof(ImportVesselBilling.Items))]
    public partial void MapToEntity(CreateUpdateImportVesselBillingDto dto, ImportVesselBilling entity);

    // DTO to Entity mapping for creation
    [MapperIgnoreTarget(nameof(ImportVesselBilling.Id))] // Will be set by EF Core
    [MapperIgnoreTarget(nameof(ImportVesselBilling.DocEntry))] // Will be set by EF Core
    [MapperIgnoreTarget(nameof(ImportVesselBilling.CreatedBy))] // Will be set by ABP
    [MapperIgnoreTarget(nameof(ImportVesselBilling.CreatedAt))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.CreationTime))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.CreatorId))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.LastModificationTime))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.LastModifierId))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.IsDeleted))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.DeleterId))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.DeletionTime))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.ExtraProperties))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.MasterJetty))]
    [MapperIgnoreTarget(nameof(ImportVesselBilling.Items))]
    public partial ImportVesselBilling MapToEntity(CreateUpdateImportVesselBillingDto dto);

    // Map list of entities to DTOs
    public partial List<ImportVesselBillingDto> MapToDtoList(List<ImportVesselBilling> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<ImportVesselBillingDto> MapToDtoEnumerable(IEnumerable<ImportVesselBilling> entities);

    // Map with items included
    public ImportVesselBillingWithItemsDto MapToDtoWithItems(ImportVesselBilling entity, List<BillingItemDto> items)
    {
        var dto = MapToDto(entity);
        var withItemsDto = new ImportVesselBillingWithItemsDto
        {
            Id = dto.Id,
            DocEntry = dto.DocEntry,
            PortService = dto.PortService,
            Status = dto.Status,
            YearArrival = dto.YearArrival,
            Jetty = dto.Jetty,
            ImportId = dto.ImportId,
            CreatedBy = dto.CreatedBy,
            UpdatedBy = dto.UpdatedBy,
            CreatedAt = dto.CreatedAt,
            UpdatedAt = dto.UpdatedAt,
            DocNum = dto.DocNum,
            PostingDate = dto.PostingDate,
            Remarks = dto.Remarks,
            PeriodDate = dto.PeriodDate,
            BillingNoteDate = dto.BillingNoteDate,
            JettyId = dto.JettyId,
            MasterJetty = dto.MasterJetty,
            Items = items
        };

        return withItemsDto;
    }
}