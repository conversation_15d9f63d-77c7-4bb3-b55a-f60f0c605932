using LocalVesselEntity = Imip.Ekb.BoundedZone.LocalVessels.LocalVessel;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.LocalVessels;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp;
using Imip.Ekb.Master.Agents.Dtos;
using Imip.Ekb.Master.Tradings.Dtos;
using Imip.Ekb.Master.Surveyors.Dtos;
using Imip.Ekb.Master.Jetties.Dtos;
using Imip.Ekb.Master.Cargos.Dtos;
using Imip.Ekb.BoundedZone.LocalVessel;
using Imip.Ekb.Master.Dtos;

namespace Imip.Ekb.Application.BoundedZone.LocalVessel;

[Authorize]
[RemoteService(false)]
public class LocalVesselCustomAppService : ApplicationService, ILocalVesselCustomAppService
{
    private readonly ILocalVesselRepository _localVesselRepository;
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly LocalVesselMapper _localVesselMapper;
    private readonly VesselMapper _vesselMapper;
    private readonly ZoneDetailMapper _zoneDetailMapper;
    private readonly ILogger<LocalVesselCustomAppService> _logger;

    public LocalVesselCustomAppService(
        ILocalVesselRepository localVesselRepository,
        IZoneDetailRepository zoneDetailRepository,
        LocalVesselMapper localVesselMapper,
        VesselMapper vesselMapper,
        ZoneDetailMapper zoneDetailMapper,
        ILogger<LocalVesselCustomAppService> logger)
    {
        _localVesselRepository = localVesselRepository;
        _zoneDetailRepository = zoneDetailRepository;
        _localVesselMapper = localVesselMapper;
        _vesselMapper = vesselMapper;
        _zoneDetailMapper = zoneDetailMapper;
        _logger = logger;
    }

    public async Task<PagedResultDto<LocalVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var entityQuery = await _localVesselRepository.GetQueryableWithIncludesAsync();
        entityQuery = ApplyDynamicQuery(entityQuery, parameters);

        System.Linq.Expressions.Expression<Func<LocalVesselEntity, LocalVesselProjectionDto>> selector = x => new LocalVesselProjectionDto
        {
            Id = x.Id,
            ConcurrencyStamp = x.ConcurrencyStamp,
            DocEntry = x.DocEntry,
            DocNum = x.DocNum,
            PostingDate = x.PostingDate,
            VesselType = x.VesselType,
            VesselArrival = x.VesselArrival,
            VesselDeparture = x.VesselDeparture,
            Shipment = x.Shipment,
            VesselQty = x.VesselQty,
            PortOrigin = x.PortOrigin,
            DestinationPort = x.DestinationPort,
            TransType = x.TransType,
            DocType = x.DocType,
            CreatedBy = x.CreatedBy,
            UpdatedBy = x.UpdatedBy,
            Voyage = x.Voyage,
            GrossWeight = x.GrossWeight,
            DocStatus = x.DocStatus,
            BerthingDate = x.BerthingDate,
            AnchorageDate = x.AnchorageDate,
            ReportDate = x.ReportDate,
            UnloadingDate = x.UnloadingDate,
            FinishUnloadingDate = x.FinishUnloadingDate,
            GrtWeight = x.GrtWeight,
            GrtVessel = x.GrtVessel,
            JettyId = x.JettyId,
            VesselId = x.VesselId,
            BargeId = x.BargeId,
            MasterAgentId = x.MasterAgentId,
            MasterTradingId = x.MasterTradingId,
            MasterSurveyorId = x.MasterSurveyorId,
            MasterAgent = x.MasterAgent == null ? null : new AgentProjectionDto
            {
                Id = x.MasterAgent.Id,
                Name = x.MasterAgent.Name,
                Status = x.MasterAgent.Status,
                Type = x.MasterAgent.Type,
                NpwpNo = x.MasterAgent.NpwpNo,
                BdmSapcode = x.MasterAgent.BdmSapcode,
                TaxCode = x.MasterAgent.TaxCode,
                AddressNpwp = x.MasterAgent.AddressNpwp,
                Address = x.MasterAgent.Address,
                SapcodeS4 = x.MasterAgent.SapcodeS4,
            },
            MasterTrading = x.MasterTrading == null ? null : new TradingProjectionDto
            {
                Id = x.MasterTrading.Id,
                Name = x.MasterTrading.Name,
                Address = x.MasterTrading.Address,
                Npwp = x.MasterTrading.Npwp,
            },
            MasterSurveyor = x.MasterSurveyor == null ? null : new SurveyorProjectionDto
            {
                Id = x.MasterSurveyor.Id,
                Name = x.MasterSurveyor.Name,
                Address = x.MasterSurveyor.Address,
                Npwp = x.MasterSurveyor.Npwp,
            },
            MasterJetty = x.MasterJetty == null ? null : new JettyProjectionDto
            {
                Id = x.MasterJetty.Id,
                Name = x.MasterJetty.Name,
                Alias = x.MasterJetty.Alias,
                Max = x.MasterJetty.Max,
                Port = x.MasterJetty.Port,
                IsCustomArea = x.MasterJetty.IsCustomArea,
            },
            Vessel = x.Vessel == null ? null : new CargoProjectionDto
            {
                Id = x.Vessel.Id,
                Name = x.Vessel.Name,
                Alias = x.Vessel.Alias,
                Flag = x.Vessel.Flag,
                GrossWeight = x.Vessel.GrossWeight,
                Type = x.Vessel.Type,
                LoaQty = x.Vessel.LoaQty,
            },
            Barge = x.Barge == null ? null : new CargoProjectionDto
            {
                Id = x.Barge.Id,
                Name = x.Barge.Name,
                Alias = x.Barge.Alias,
                Flag = x.Barge.Flag,
                GrossWeight = x.Barge.GrossWeight,
                Type = x.Barge.Type,
                LoaQty = x.Barge.LoaQty,
            },
        };

        var projectedQuery = entityQuery.Select(selector);

        var totalCount = await AsyncExecuter.CountAsync(projectedQuery);
        var items = await AsyncExecuter.ToListAsync(
            projectedQuery.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        return new PagedResultDto<LocalVesselProjectionDto>
        {
            TotalCount = totalCount,
            Items = items
        };
    }

    public async Task<LocalVesselWithItemsDto> GetWithItemsAsync(Guid id)
    {
        var vesselWithItems = await _localVesselRepository.GetProjectedByIdAsync(id, x => new LocalVesselWithItemsDto
        {
            Id = x.Id,
            ConcurrencyStamp = x.ConcurrencyStamp,
            DocEntry = x.DocEntry,
            DocNum = x.DocNum,
            PostingDate = x.PostingDate,
            VesselType = x.VesselType,
            VesselArrival = x.VesselArrival,
            VesselDeparture = x.VesselDeparture,
            Shipment = x.Shipment,
            VesselQty = x.VesselQty,
            PortOrigin = x.PortOrigin,
            DestinationPort = x.DestinationPort,
            TransType = x.TransType,
            DocType = x.DocType,
            CreatedBy = x.CreatedBy,
            UpdatedBy = x.UpdatedBy,
            Voyage = x.Voyage,
            GrossWeight = x.GrossWeight,
            DocStatus = x.DocStatus,
            BerthingDate = x.BerthingDate,
            AnchorageDate = x.AnchorageDate,
            ReportDate = x.ReportDate,
            UnloadingDate = x.UnloadingDate,
            FinishUnloadingDate = x.FinishUnloadingDate,
            GrtWeight = x.GrtWeight,
            GrtVessel = x.GrtVessel,
            JettyId = x.JettyId,
            VesselId = x.VesselId,
            BargeId = x.BargeId,
            MasterAgentId = x.MasterAgentId,
            MasterTradingId = x.MasterTradingId,
            MasterSurveyorId = x.MasterSurveyorId,
            MasterAgent = x.MasterAgent == null ? null : new AgentDto
            {
                Id = x.MasterAgent.Id,
                Name = x.MasterAgent.Name,
                Status = x.MasterAgent.Status,
                Type = x.MasterAgent.Type,
                NpwpNo = x.MasterAgent.NpwpNo,
                BdmSapcode = x.MasterAgent.BdmSapcode,
                TaxCode = x.MasterAgent.TaxCode,
                AddressNpwp = x.MasterAgent.AddressNpwp,
                Address = x.MasterAgent.Address,
                SapcodeS4 = x.MasterAgent.SapcodeS4,
            },
            MasterTrading = x.MasterTrading == null ? null : new TradingDto
            {
                Id = x.MasterTrading.Id,
                Name = x.MasterTrading.Name,
                Address = x.MasterTrading.Address,
                Npwp = x.MasterTrading.Npwp,
            },
            MasterSurveyor = x.MasterSurveyor == null ? null : new SurveyorDto
            {
                Id = x.MasterSurveyor.Id,
                Name = x.MasterSurveyor.Name,
                Address = x.MasterSurveyor.Address,
                Npwp = x.MasterSurveyor.Npwp,
            },
            MasterJetty = x.MasterJetty == null ? null : new JettyDto
            {
                Id = x.MasterJetty.Id,
                Name = x.MasterJetty.Name,
                Alias = x.MasterJetty.Alias,
                Max = x.MasterJetty.Max,
                Port = x.MasterJetty.Port,
                IsCustomArea = x.MasterJetty.IsCustomArea,
            },
            Vessel = x.Vessel == null ? null : new CargoDto
            {
                Id = x.Vessel.Id,
                Name = x.Vessel.Name,
                Alias = x.Vessel.Alias,
                Flag = x.Vessel.Flag,
                GrossWeight = x.Vessel.GrossWeight,
                Type = x.Vessel.Type,
                LoaQty = x.Vessel.LoaQty,
            },
            Barge = x.Barge == null ? null : new CargoDto
            {
                Id = x.Barge.Id,
                Name = x.Barge.Name,
                Alias = x.Barge.Alias,
                Flag = x.Barge.Flag,
                GrossWeight = x.Barge.GrossWeight,
                Type = x.Barge.Type,
                LoaQty = x.Barge.LoaQty,
            },
            Items = x.Items.Select(zoneDetail => new VesselItemDto
            {
                Id = zoneDetail.Id,
                DocEntry = zoneDetail.DocEntry,
                DocNum = zoneDetail.DocNum ?? 0,
                TenantName = zoneDetail.Tenant != null ? zoneDetail.Tenant.Name : null,
                ItemName = zoneDetail.ItemName,
                ItemQty = zoneDetail.ItemQty,
                UnitQty = zoneDetail.UnitQty,
                Cargo = zoneDetail.Cargo,
                Shipment = zoneDetail.Shipment,
                Remarks = zoneDetail.Remarks,
                NoBl = zoneDetail.NoBl,
                DateBl = zoneDetail.DateBl,
                AjuNo = zoneDetail.AjuNo,
                RegNo = zoneDetail.RegNo,
                RegDate = zoneDetail.RegDate,
                SppbNo = zoneDetail.SppbNo,
                SppbDate = zoneDetail.SppbDate,
                SppdNo = zoneDetail.SppdNo,
                SppdDate = zoneDetail.SppdDate,
                GrossWeight = zoneDetail.GrossWeight,
                UnitWeight = zoneDetail.UnitWeight,
                HeaderId = zoneDetail.HeaderId,
                LetterNo = zoneDetail.LetterNo,
                DocType = zoneDetail.DocType,
                VesselType = "LocalVessel",
                ShippingInstructionNo = zoneDetail.ShippingInstructionNo,
                ShippingInstructionDate = zoneDetail.ShippingInstructionDate,
                LetterDate = zoneDetail.LetterDate,
                TenantId = zoneDetail.TenantId,
                BcTypeId = zoneDetail.BcTypeId,
                BusinessPartnerId = zoneDetail.BusinessPartnerId,
                AgentId = zoneDetail.AgentId,
                MasterExportClassificationId = zoneDetail.MasterExportClassificationId,
                Tenant = zoneDetail.Tenant == null ? null : new TenantShortDto
                {
                    Id = zoneDetail.Tenant.Id,
                    Name = zoneDetail.Tenant.Name,
                    FullName = zoneDetail.Tenant.FullName,
                    DocEntry = zoneDetail.Tenant.DocEntry
                },
                BcType = zoneDetail.BcType == null ? null : new BcTypeShortDto
                {
                    Id = zoneDetail.BcType.Id,
                    Type = zoneDetail.BcType.Type,
                    TransName = zoneDetail.BcType.TransName,
                },
                BusinessPartner = zoneDetail.BusinessPartner == null ? null : new BusinessPartnerShortDto
                {
                    Id = zoneDetail.BusinessPartner.Id,
                    Name = zoneDetail.BusinessPartner.Name,
                    Npwp = zoneDetail.BusinessPartner.Npwp,
                    Nitku = zoneDetail.BusinessPartner.Nitku,
                    DocEntry = zoneDetail.BusinessPartner.DocEntry
                },
                MasterAgent = zoneDetail.MasterAgent == null ? null : new AgentShortDto
                {
                    Id = zoneDetail.MasterAgent.Id,
                    Name = zoneDetail.MasterAgent.Name,
                    Type = zoneDetail.MasterAgent.Type,
                },
                Attachments = zoneDetail.DocAttachment != null ? zoneDetail.DocAttachment.Select(att => new DocAttachmentSortDto
                {
                    Id = att.Id,
                    DocType = att.DocType,
                    TransType = att.TransType,
                    Description = att.Description,
                    BlobName = att.BlobName,
                    ReferenceId = att.ReferenceId,
                    FileName = att.FileName,
                    StreamUrl = att.FilePath,
                    TypePa = att.TypePa
                }).ToList() : new List<DocAttachmentSortDto>()
            }).ToList()
        });

        if (vesselWithItems == null)
        {
            throw new UserFriendlyException("LocalVessel not found");
        }

        return vesselWithItems;
    }

    private IQueryable<LocalVesselEntity> ApplyDynamicQuery(IQueryable<LocalVesselEntity> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<LocalVesselEntity>.ApplyFilters(query, parameters.FilterGroup);
        }

        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<LocalVesselEntity>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }
}