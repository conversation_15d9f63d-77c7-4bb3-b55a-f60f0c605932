using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.BoundedZone.ExportVessels;

public interface IExportVesselAppService :
    ICrudAppService<
        ExportVesselDto,
        Guid,
        QueryParametersDto,
        CreateUpdateExportVesselDto>
{
    Task<PagedResultDto<ExportVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters);
    Task<ExportVesselWithItemsDto> GetWithItemsAsync(Guid id);
}