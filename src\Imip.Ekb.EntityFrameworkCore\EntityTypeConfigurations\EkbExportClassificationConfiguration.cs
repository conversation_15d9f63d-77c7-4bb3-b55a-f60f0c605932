using Imip.Ekb.Master.ExportClassifications;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;


namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbExportClassificationConfiguration : IEntityTypeConfiguration<ExportClassification>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbExportClassificationConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<ExportClassification> b)
    {
        b.ToTable($"ExportClassification", schema);
        b.ConfigureByConvention();
    }
}