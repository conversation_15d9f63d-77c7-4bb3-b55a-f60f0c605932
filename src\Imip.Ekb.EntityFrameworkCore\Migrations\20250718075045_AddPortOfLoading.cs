﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class AddPortOfLoading : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "DestinationPortId",
                table: "THEXP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "PortOriginId",
                table: "THEXP",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DestinationPortId",
                table: "T_MDOC_Header",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "PortOriginId",
                table: "T_MDOC_Header",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DestinationPortId",
                table: "L_master",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "PortOriginId",
                table: "L_master",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddUniqueConstraint(
                name: "AK_M_PortOfLoading_Id",
                table: "M_PortOfLoading",
                column: "Id");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_M_DestinationPort_Id",
                table: "M_DestinationPort",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_THEXP_DestinationPortId",
                table: "THEXP",
                column: "DestinationPortId");

            migrationBuilder.CreateIndex(
                name: "IX_THEXP_PortOriginId",
                table: "THEXP",
                column: "PortOriginId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_Header_DestinationPortId",
                table: "T_MDOC_Header",
                column: "DestinationPortId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_Header_PortOriginId",
                table: "T_MDOC_Header",
                column: "PortOriginId");

            migrationBuilder.CreateIndex(
                name: "IX_L_master_DestinationPortId",
                table: "L_master",
                column: "DestinationPortId");

            migrationBuilder.CreateIndex(
                name: "IX_L_master_PortOriginId",
                table: "L_master",
                column: "PortOriginId");

            migrationBuilder.AddForeignKey(
                name: "FK_L_master_M_DestinationPort_DestinationPortId",
                table: "L_master",
                column: "DestinationPortId",
                principalTable: "M_DestinationPort",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_L_master_M_PortOfLoading_PortOriginId",
                table: "L_master",
                column: "PortOriginId",
                principalTable: "M_PortOfLoading",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_Header_M_DestinationPort_DestinationPortId",
                table: "T_MDOC_Header",
                column: "DestinationPortId",
                principalTable: "M_DestinationPort",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_Header_M_PortOfLoading_PortOriginId",
                table: "T_MDOC_Header",
                column: "PortOriginId",
                principalTable: "M_PortOfLoading",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_THEXP_M_DestinationPort_DestinationPortId",
                table: "THEXP",
                column: "DestinationPortId",
                principalTable: "M_DestinationPort",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_THEXP_M_PortOfLoading_PortOriginId",
                table: "THEXP",
                column: "PortOriginId",
                principalTable: "M_PortOfLoading",
                principalColumn: "Id");

            migrationBuilder.Sql("CREATE INDEX IX_L_master_DestinationPort ON L_master(DestinationPort);");
            migrationBuilder.Sql("CREATE INDEX IX_M_DestinationPort_Name ON M_DestinationPort(Name);");


            migrationBuilder.Sql(@"
                INSERT INTO M_PortOfLoading (Id, Name, DocType, CreationTime, ConcurrencyStamp)
                SELECT NEWID(), PortOfLoading, 'Import',  GETDATE(), NEWID()
                FROM (
                    SELECT DISTINCT PortOfLoading
                    FROM T_MDOC_Header
                    WHERE PortOfLoading IS NOT NULL
                ) AS DistinctPorts
            ");

            // Insert missing DestinationPorts from L_master
            migrationBuilder.Sql(@"
                INSERT INTO M_DestinationPort (Id, Name, DocType, CreationTime, ConcurrencyStamp)
                SELECT NEWID(), l.DestinationPort, 'Local', GETDATE(), NEWID()
                FROM L_master l
                LEFT JOIN M_DestinationPort d ON l.DestinationPort = d.Name
                WHERE l.DestinationPort IS NOT NULL AND d.Id IS NULL
                GROUP BY l.DestinationPort
            ");

            // Insert missing DestinationPorts from THEXP
            migrationBuilder.Sql(@"
                INSERT INTO M_DestinationPort (Id, Name, DocType, CreationTime, ConcurrencyStamp)
                SELECT NEWID(), x.DestinationPort, 'Export', GETDATE(), NEWID()
                FROM THEXP x
                LEFT JOIN M_DestinationPort d ON x.DestinationPort = d.Name
                WHERE x.DestinationPort IS NOT NULL AND d.Id IS NULL
                GROUP BY x.DestinationPort
            ");


            migrationBuilder.Sql(@"
                INSERT INTO M_PortOfLoading (Id, Name, DocType, CreationTime, ConcurrencyStamp)
                SELECT NEWID(), l.PortOrigin, 'Local', GETDATE(), NEWID()
                FROM L_master l
                LEFT JOIN M_PortOfLoading d ON l.PortOrigin = d.Name
                WHERE l.PortOrigin IS NOT NULL AND l.PortOrigin <> '' AND d.Id IS NULL
                GROUP BY l.PortOrigin;
            ");

            // Now update only when a match exists
            migrationBuilder.Sql(@"
                UPDATE l
                SET DestinationPortId = t.Id
                FROM L_master l
                INNER JOIN M_DestinationPort t ON l.DestinationPort = t.Name
                WHERE l.DestinationPort IS NOT NULL AND l.DestinationPort <> '';
            ");

            migrationBuilder.Sql(@"
                UPDATE l
                SET PortOriginId = t.Id
                FROM L_master l
                INNER JOIN M_PortOfLoading t ON l.PortOrigin = t.Name
                WHERE l.PortOrigin IS NOT NULL AND l.PortOrigin <> '';
            ");

            migrationBuilder.Sql(@"
                UPDATE x
                SET DestinationPortId = t.Id
                FROM THEXP x
                left JOIN M_DestinationPort t ON x.DestinationPort = t.Name;
            ");

            migrationBuilder.Sql(@"
                UPDATE x
                SET PortOriginId = t.Id
                FROM THEXP x
                left JOIN M_PortOfLoading t ON x.PortOrigin = t.Name;
            ");

            migrationBuilder.Sql(@"
                 UPDATE T_MDOC_Header SET DestinationPortId = (select id from M_DestinationPort where Name= 'PT. BINTANGDELAPAN MINERAL');
            ");

            migrationBuilder.Sql(@"
                UPDATE h
                SET PortOriginId = t.Id
                FROM T_MDOC_Header h
                left JOIN M_PortOfLoading t ON h.PortOfLoading = t.Name;
            ");

            migrationBuilder.Sql("DROP INDEX IX_L_master_DestinationPort ON L_master;");
            migrationBuilder.Sql("DROP INDEX IX_M_DestinationPort_Name ON M_DestinationPort;");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_L_master_M_DestinationPort_DestinationPortId",
                table: "L_master");

            migrationBuilder.DropForeignKey(
                name: "FK_L_master_M_PortOfLoading_PortOriginId",
                table: "L_master");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_Header_M_DestinationPort_DestinationPortId",
                table: "T_MDOC_Header");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_Header_M_PortOfLoading_PortOriginId",
                table: "T_MDOC_Header");

            migrationBuilder.DropForeignKey(
                name: "FK_THEXP_M_DestinationPort_DestinationPortId",
                table: "THEXP");

            migrationBuilder.DropForeignKey(
                name: "FK_THEXP_M_PortOfLoading_PortOriginId",
                table: "THEXP");

            migrationBuilder.DropIndex(
                name: "IX_THEXP_DestinationPortId",
                table: "THEXP");

            migrationBuilder.DropIndex(
                name: "IX_THEXP_PortOriginId",
                table: "THEXP");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_Header_DestinationPortId",
                table: "T_MDOC_Header");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_Header_PortOriginId",
                table: "T_MDOC_Header");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_M_PortOfLoading_Id",
                table: "M_PortOfLoading");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_M_DestinationPort_Id",
                table: "M_DestinationPort");

            migrationBuilder.DropIndex(
                name: "IX_L_master_DestinationPortId",
                table: "L_master");

            migrationBuilder.DropIndex(
                name: "IX_L_master_PortOriginId",
                table: "L_master");

            migrationBuilder.DropColumn(
                name: "DestinationPortId",
                table: "THEXP");

            migrationBuilder.DropColumn(
                name: "PortOriginId",
                table: "THEXP");

            migrationBuilder.DropColumn(
                name: "DestinationPortId",
                table: "T_MDOC_Header");

            migrationBuilder.DropColumn(
                name: "PortOriginId",
                table: "T_MDOC_Header");

            migrationBuilder.DropColumn(
                name: "DestinationPortId",
                table: "L_master");

            migrationBuilder.DropColumn(
                name: "PortOriginId",
                table: "L_master");
        }
    }
}
