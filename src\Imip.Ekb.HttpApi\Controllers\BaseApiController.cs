using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;

namespace Imip.Ekb.Controllers;

/// <summary>
/// Base controller for API endpoints that use Bearer token authentication
/// Disables antiforgery validation since these endpoints use token-based authentication
/// </summary>
[Authorize]
[IgnoreAntiforgeryToken]
public abstract class BaseApiController : AbpControllerBase
{
}