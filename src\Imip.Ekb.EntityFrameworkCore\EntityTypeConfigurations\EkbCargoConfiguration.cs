using Imip.Ekb.Master.Cargos;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbCargoConfiguration : IEntityTypeConfiguration<Cargo>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbCargoConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<Cargo> b)
    {
        b.ToTable("M_CARGO", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions
    }
}