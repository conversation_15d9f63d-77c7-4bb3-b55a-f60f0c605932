using TradingVesselEntity = Imip.Ekb.BoundedZone.TradingVessels.TradingVessel;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.TradingVessels;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp;
using Microsoft.Extensions.Configuration;

namespace Imip.Ekb.Application.BoundedZone.TradingVessel;

[Authorize]
public class TradingVesselAppService :
    CrudAppService<
        TradingVesselEntity,
        TradingVesselDto,
        Guid,
        QueryParametersDto,
        CreateUpdateTradingVesselDto>,
    ITradingVesselAppService
{
    private readonly ITradingVesselRepository _tradingVesselRepository;
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly TradingVesselMapper _tradingVesselMapper;
    private readonly VesselMapper _vesselMapper;
    private readonly IConfiguration _configuration;
    private readonly ILogger<TradingVesselAppService> _logger;

    public TradingVesselAppService(
        ITradingVesselRepository tradingVesselRepository,
        IZoneDetailRepository zoneDetailRepository,
        TradingVesselMapper tradingVesselMapper,
        VesselMapper vesselMapper,
        IConfiguration configuration,
        ILogger<TradingVesselAppService> logger)
        : base(tradingVesselRepository)
    {
        _configuration = configuration;
        _tradingVesselRepository = tradingVesselRepository;
        _zoneDetailRepository = zoneDetailRepository;
        _tradingVesselMapper = tradingVesselMapper;
        _vesselMapper = vesselMapper;
        _logger = logger;
    }

    public override async Task<PagedResultDto<TradingVesselDto>> GetListAsync(QueryParametersDto input)
    {
        await CheckGetListPolicyAsync();

        var query = await _tradingVesselRepository.GetQueryableWithIncludesAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            query = query.OrderBy(input.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);

        var entities = await AsyncExecuter.ToListAsync(
            query.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(MapToGetListOutputDto).ToList();

        return new PagedResultDto<TradingVesselDto>(totalCount, dtos);
    }

    public override async Task<TradingVesselDto> CreateAsync(CreateUpdateTradingVesselDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _tradingVesselMapper.MapToEntity(input);

        await _tradingVesselRepository.InsertAsync(entity, autoSave: true);

        // Load the entity with includes for proper mapping
        var createdEntity = await _tradingVesselRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(createdEntity);
    }

    public override async Task<TradingVesselDto> UpdateAsync(Guid id, CreateUpdateTradingVesselDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _tradingVesselRepository.GetAsync(id);
        _tradingVesselMapper.MapToEntity(input, entity);

        await _tradingVesselRepository.UpdateAsync(entity, autoSave: true);

        // Load the entity with includes for proper mapping
        var updatedEntity = await _tradingVesselRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(updatedEntity);
    }

    public async Task<TradingVesselWithItemsDto> GetWithItemsAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _tradingVesselRepository.GetAsync(id);
        if (entity == null)
        {
            throw new UserFriendlyException("TradingVessel not found");
        }

        var items = await GetItemsAsync(entity.DocEntry);

        var dto = new TradingVesselWithItemsDto
        {
            Id = entity.Id,
            DocEntry = entity.DocEntry,
            DocNum = entity.DocNum,
            Tenant = entity.Tenant,
            Bp = entity.Bp,
            PostDate = entity.PostDate,
            Contract = entity.Contract,
            Bc = entity.Bc,
            CreatedBy = entity.CreatedBy,
            UpdatedBy = entity.UpdatedBy,
            CreatedAt = entity.CreatedAt,
            UpdatedAt = entity.UpdatedAt,
            Deleted = entity.Deleted,
            Status = entity.Status,
            Remark = entity.Remark,
            ContractDate = entity.ContractDate,
            SubconType = entity.SubconType,
            DocStatus = entity.DocStatus,
            Items = items
        };

        return dto;
    }

    public async Task<List<VesselItemDto>> GetItemsAsync(int docEntry)
    {
        var zoneDetailQuery = await _zoneDetailRepository.GetQueryableWithVesselHeadersAsync();

        var query = zoneDetailQuery.Where(z => z.DocNum == docEntry && z.DocType == "TradingVessel");
        var items = await AsyncExecuter.ToListAsync(query);

        var vesselItems = new List<VesselItemDto>();
        foreach (var zoneDetail in items)
        {
            var itemDto = _vesselMapper.MapZoneDetailToItemDtoWithType(zoneDetail, "TradingVessel");

            // Use navigation property for Tenant
            if (zoneDetail.Tenant != null)
            {
                itemDto.Tenant = _vesselMapper.MapTenantToTenantShortDto(zoneDetail.Tenant);
            }

            // Map DocAttachments if available
            if (zoneDetail.DocAttachment != null && zoneDetail.DocAttachment.Any())
            {
                itemDto.Attachments = zoneDetail.DocAttachment.Select(att => _vesselMapper.MapDocAttachmentToDto(att)).ToList();
            }
            else
            {
                itemDto.Attachments = new List<DocAttachmentSortDto>();
            }

            vesselItems.Add(itemDto);
        }

        return vesselItems;
    }

    private IQueryable<TradingVesselEntity> ApplyDynamicQuery(IQueryable<TradingVesselEntity> query, QueryParametersDto parameters)
    {
        // Apply filters if provided
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<TradingVesselEntity>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<TradingVesselEntity>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }

    protected override TradingVesselDto MapToGetOutputDto(TradingVesselEntity entity)
    {
        return _tradingVesselMapper.MapToDto(entity);
    }

    protected override TradingVesselDto MapToGetListOutputDto(TradingVesselEntity entity)
    {
        return _tradingVesselMapper.MapToDto(entity);
    }

    protected override TradingVesselEntity MapToEntity(CreateUpdateTradingVesselDto createInput)
    {
        return _tradingVesselMapper.MapToEntity(createInput);
    }

    protected override void MapToEntity(CreateUpdateTradingVesselDto updateInput, TradingVesselEntity entity)
    {
        _tradingVesselMapper.MapToEntity(updateInput, entity);
    }

    public async Task<PagedResultDto<TradingVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters)
    {
        await CheckGetListPolicyAsync();

        var entityQuery = await _tradingVesselRepository.GetQueryableWithIncludesAsync();
        entityQuery = ApplyDynamicQuery(entityQuery, parameters);

        // Define the projection selector
        System.Linq.Expressions.Expression<Func<TradingVesselEntity, TradingVesselProjectionDto>> selector = x => new TradingVesselProjectionDto
        {
            Id = x.Id,
            DocEntry = x.DocEntry,
            DocNum = x.DocNum,
            Tenant = x.Tenant,
            Bp = x.Bp,
            PostDate = x.PostDate,
            Contract = x.Contract,
            Bc = x.Bc,
            CreatedBy = x.CreatedBy,
            UpdatedBy = x.UpdatedBy,
            CreatedAt = x.CreatedAt,
            UpdatedAt = x.UpdatedAt,
            Deleted = x.Deleted,
            Status = x.Status,
            Remark = x.Remark,
            ContractDate = x.ContractDate,
            SubconType = x.SubconType,
            DocStatus = x.DocStatus,
        };

        var projectedQuery = entityQuery.Select(selector);

        var totalCount = await AsyncExecuter.CountAsync(projectedQuery);
        var items = await AsyncExecuter.ToListAsync(
            projectedQuery.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        return new PagedResultDto<TradingVesselProjectionDto>
        {
            TotalCount = totalCount,
            Items = items
        };
    }
}