using System;
using System.ComponentModel.DataAnnotations;
using Imip.Ekb.Master.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.BoundedZone.LocalVessel;

public class LocalVesselProjectionDto : AuditedEntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public int DocEntry { get; set; }

    [StringLength(50)]
    public string DocNum { get; set; } = null!;

    public DateOnly PostingDate { get; set; }

    [StringLength(20)]
    public string VesselType { get; set; } = null!;

    public DateTime? VesselArrival { get; set; }

    public DateTime? VesselDeparture { get; set; }

    [StringLength(100)]
    public string Shipment { get; set; } = null!;

    public decimal VesselQty { get; set; }

    [StringLength(200)]
    public string PortOrigin { get; set; } = null!;

    [StringLength(200)]
    public string DestinationPort { get; set; } = null!;

    public string? Remark { get; set; }


    [StringLength(5)]
    public string TransType { get; set; } = null!;

    [StringLength(20)]
    public string DocType { get; set; } = null!;

    public long CreatedBy { get; set; }

    public long UpdatedBy { get; set; }

    [StringLength(40)]
    public string? Voyage { get; set; }

    public decimal? GrossWeight { get; set; }

    [StringLength(10)]
    public string? DocStatus { get; set; }

    [StringLength(100)]
    public string? Status { get; set; }

    public decimal? BeratTugboat { get; set; }

    public DateTime? BerthingDate { get; set; }

    public DateTime? AnchorageDate { get; set; }

    public DateOnly? ReportDate { get; set; }

    public DateTime? UnloadingDate { get; set; }

    public DateTime? FinishUnloadingDate { get; set; }

    public decimal? GrtWeight { get; set; }

    [StringLength(50)]
    public string? InvoiceStatus { get; set; }

    [StringLength(100)]
    public string StatusBms { get; set; } = null!;

    public decimal? GrtVessel { get; set; }

    public Guid? JettyId { get; set; }
    public Guid? VesselId { get; set; }
    public Guid? BargeId { get; set; }
    public Guid? MasterAgentId { get; set; }
    public Guid? MasterTradingId { get; set; }
    public Guid? MasterSurveyorId { get; set; }
    /// <summary>
    /// Aside date and time
    /// </summary>
    public DateTime? AsideDate { get; set; }

    /// <summary>
    /// Cast off date and time
    /// </summary>
    public DateTime? CastOfDate { get; set; }
    public Guid? PortOriginId { get; set; }
    public Guid? DestinationPortId { get; set; }
    public virtual AgentProjectionDto? MasterAgent { get; set; }
    public virtual TradingProjectionDto? MasterTrading { get; set; }
    public virtual SurveyorProjectionDto? MasterSurveyor { get; set; }
    public virtual JettyProjectionDto? MasterJetty { get; set; }
    public virtual CargoProjectionDto? Vessel { get; set; }
    public virtual CargoProjectionDto? Barge { get; set; }
    public virtual PortOfOriginProjectionDto? MasterPortOrigin { get; set; }
    public virtual DestinationPortProjectionDto? MasterDestinationPort { get; set; }
}
