﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class Added_ZoneDetailInvoice : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ZoneDetailId",
                table: "R_Inv",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_R_Inv_T_MDOC_ZoneDetailId",
                table: "R_Inv",
                column: "ZoneDetailId",
                principalTable: "T_MDOC",
                principalColumn: "Id");

            migrationBuilder.AddColumn<Guid>(
                name: "ZoneDetailId",
                table: "T_MDOC_bl",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_bl_T_MDOC_ZoneDetailId",
                table: "T_MDOC_bl",
                column: "ZoneDetailId",
                principalTable: "T_MDOC",
                principalColumn: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_R_Inv_ZoneDetailId",
                table: "R_Inv",
                column: "ZoneDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_bl_ZoneDetailId",
                table: "T_MDOC_bl",
                column: "ZoneDetailId");

            migrationBuilder.Sql(@"
                UPDATE T_MDOC_bl
                SET ZoneDetailId = (SELECT tbc.Id FROM T_MDOC tbc WHERE T_MDOC_bl.BLKey = tbc.DocEntry);
            ");

            migrationBuilder.Sql(@"
                UPDATE R_Inv
                SET ZoneDetailId = (SELECT tbc.Id FROM T_MDOC tbc WHERE R_Inv.DocNum = tbc.DocEntry);
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "R_Inv");

            migrationBuilder.DropTable(
                name: "T_MDOC_bl");
        }
    }
}
