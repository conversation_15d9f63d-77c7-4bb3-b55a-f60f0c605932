using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace Imip.Ekb.Web.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly ILogger<AuthController> _logger;

    public AuthController(ILogger<AuthController> logger)
    {
        _logger = logger;
    }

    [HttpGet("logout")]
    public async Task<IActionResult> Logout()
    {
        try
        {
            _logger.LogInformation("Custom logout requested for user: {UserName}", User?.Identity?.Name ?? "Unknown");

            // Check if user is authenticated before logout
            var isAuthenticated = User?.Identity?.IsAuthenticated ?? false;
            _logger.LogInformation("User authentication status before logout: {IsAuthenticated}", isAuthenticated);

            // Sign out the user from cookie authentication
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);

            // Clear all authentication cookies
            Response.Cookies.Delete(".Imip.Ekb.Auth");
            Response.Cookies.Delete(".Imip.Ekb.Antiforgery");
            Response.Cookies.Delete(".Imip.Ekb.Session");

            // Clear any other cookies that might be related to authentication
            foreach (var cookie in Request.Cookies.Keys)
            {
                if (cookie.StartsWith(".Imip.Ekb.") || cookie.StartsWith("AspNetCore."))
                {
                    Response.Cookies.Delete(cookie);
                    _logger.LogDebug("Deleted cookie: {CookieName}", cookie);
                }
            }

            _logger.LogInformation("User successfully logged out via custom endpoint");

            // Return a simple success response
            return Ok(new { message = "Logged out successfully" });
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, "Error during custom logout");
            return StatusCode(500, new { message = "Error during logout" });
        }
    }
}