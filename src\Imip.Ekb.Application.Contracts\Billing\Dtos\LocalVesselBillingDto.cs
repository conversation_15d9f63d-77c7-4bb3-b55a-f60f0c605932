using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Imip.Ekb.Master.Jetties.Dtos;

namespace Imip.Ekb.Billing.LocalVesselBillings.Dtos;

public class LocalVesselBillingDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }

    [StringLength(255)]
    public string PortService { get; set; } = null!;

    [StringLength(255)]
    public string Status { get; set; } = null!;

    [StringLength(255)]
    public string? YearArrival { get; set; }

    public int Jetty { get; set; }

    public int LocalId { get; set; }

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }

    public int? DocNum { get; set; }

    public DateOnly PostingDate { get; set; }

    [StringLength(255)]
    public string Remarks { get; set; } = null!;

    [StringLength(255)]
    public string Type { get; set; } = null!;

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public DateOnly? PeriodDate { get; set; }

    public DateOnly? BillingNoteDate { get; set; }

    public Guid? JettyId { get; set; }

    // Navigation properties
    public JettyDto? MasterJetty { get; set; }
}