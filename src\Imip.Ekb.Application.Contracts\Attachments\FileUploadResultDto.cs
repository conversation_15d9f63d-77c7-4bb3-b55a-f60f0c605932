using System;

namespace Imip.Ekb.Application.Contracts.Attachments;

/// <summary>
/// DTO for file upload result
/// </summary>
public class FileUploadResultDto
{
    /// <summary>
    /// The unique identifier of the uploaded file
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// The name of the uploaded file
    /// </summary>
    public string FileName { get; set; } = null!;

    /// <summary>
    /// The content type of the uploaded file
    /// </summary>
    public string ContentType { get; set; } = null!;

    /// <summary>
    /// The size of the uploaded file in bytes
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// The time when the file was uploaded
    /// </summary>
    public DateTime UploadTime { get; set; }

    /// <summary>
    /// The download URL for the file
    /// </summary>
    public string Url { get; set; } = null!;

    /// <summary>
    /// The stream URL for the file
    /// </summary>
    public string StreamUrl { get; set; } = null!;
}