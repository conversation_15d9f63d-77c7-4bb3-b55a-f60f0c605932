using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.BoundedZone.ImportVessels;

public interface IZoneDetailInvoiceAppService :
    ICrudAppService<
        ZoneDetailInvoiceDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateZoneDetailInvoiceDto>
{
    Task<IEnumerable<ZoneDetailInvoiceDto>> GetByZoneDetailAsync(Guid id);
}