﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.BoundedZone.TradingVessels;

[Table("R_Master")]
public class TradingVessel : FullAuditedAggregateRoot<Guid>
{
    [Key]
    [Column("DocEntry")]
    public int DocEntry { get; set; }

    [Column("DocNo")]
    public long DocNum { get; set; }

    public long Tenant { get; set; }

    [Column("BP")]
    [StringLength(255)]
    public string? Bp { get; set; }

    public DateOnly PostDate { get; set; }

    [StringLength(255)]
    public string? Contract { get; set; }

    [Column("BC")]
    public int Bc { get; set; }

    public int CreatedBy { get; set; }

    public int UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [Column("deleted")]
    [StringLength(1)]
    public string Deleted { get; set; } = null!;

    [Column("status")]
    [StringLength(255)]
    public string? Status { get; set; }

    [Column("remark")]
    public string? Remark { get; set; }

    public DateOnly? ContractDate { get; set; }

    [Column("subconType")]
    [StringLength(255)]
    public string? SubconType { get; set; }

    [StringLength(255)]
    public string DocStatus { get; set; } = null!;

    public virtual ICollection<ZoneDetail>? Items { get; set; }
}
