using Imip.Ekb.BoundedZone.LocalVessels;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Master.Cargos;
using Imip.Ekb.Master.Jetties;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class LocalVesselMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(LocalVessel.Id), nameof(LocalVesselDto.Id))]
    [MapProperty(nameof(LocalVessel.DocEntry), nameof(LocalVesselDto.DocEntry))]
    [MapProperty(nameof(LocalVessel.DocNum), nameof(LocalVesselDto.DocNum))]
    [MapProperty(nameof(LocalVessel.PostingDate), nameof(LocalVesselDto.PostingDate))]
    [MapProperty(nameof(LocalVessel.VesselType), nameof(LocalVesselDto.VesselType))]
    [MapProperty(nameof(LocalVessel.VesselName), nameof(LocalVesselDto.VesselName))]
    [MapProperty(nameof(LocalVessel.Tongkang), nameof(LocalVesselDto.Tongkang))]
    [MapProperty(nameof(LocalVessel.VesselArrival), nameof(LocalVesselDto.VesselArrival))]
    [MapProperty(nameof(LocalVessel.VesselDeparture), nameof(LocalVesselDto.VesselDeparture))]
    [MapProperty(nameof(LocalVessel.Shipment), nameof(LocalVesselDto.Shipment))]
    [MapProperty(nameof(LocalVessel.VesselQty), nameof(LocalVesselDto.VesselQty))]
    [MapProperty(nameof(LocalVessel.PortOrigin), nameof(LocalVesselDto.PortOrigin))]
    [MapProperty(nameof(LocalVessel.DestinationPort), nameof(LocalVesselDto.DestinationPort))]
    [MapProperty(nameof(LocalVessel.Remark), nameof(LocalVesselDto.Remark))]
    [MapProperty(nameof(LocalVessel.Deleted), nameof(LocalVesselDto.Deleted))]
    [MapProperty(nameof(LocalVessel.TransType), nameof(LocalVesselDto.TransType))]
    [MapProperty(nameof(LocalVessel.CreatedBy), nameof(LocalVesselDto.CreatedBy))]
    [MapProperty(nameof(LocalVessel.UpdatedBy), nameof(LocalVesselDto.UpdatedBy))]
    [MapProperty(nameof(LocalVessel.CreatedAt), nameof(LocalVesselDto.CreatedAt))]
    [MapProperty(nameof(LocalVessel.UpdatedAt), nameof(LocalVesselDto.UpdatedAt))]
    [MapProperty(nameof(LocalVessel.Voyage), nameof(LocalVesselDto.Voyage))]
    [MapProperty(nameof(LocalVessel.GrossWeight), nameof(LocalVesselDto.GrossWeight))]
    [MapProperty(nameof(LocalVessel.DocStatus), nameof(LocalVesselDto.DocStatus))]
    [MapProperty(nameof(LocalVessel.Jetty), nameof(LocalVesselDto.Jetty))]
    [MapProperty(nameof(LocalVessel.Status), nameof(LocalVesselDto.Status))]
    [MapProperty(nameof(LocalVessel.BeratTugboat), nameof(LocalVesselDto.BeratTugboat))]
    [MapProperty(nameof(LocalVessel.BerthingDate), nameof(LocalVesselDto.BerthingDate))]
    [MapProperty(nameof(LocalVessel.AnchorageDate), nameof(LocalVesselDto.AnchorageDate))]
    [MapProperty(nameof(LocalVessel.ReportDate), nameof(LocalVesselDto.ReportDate))]
    [MapProperty(nameof(LocalVessel.UnloadingDate), nameof(LocalVesselDto.UnloadingDate))]
    [MapProperty(nameof(LocalVessel.FinishUnloadingDate), nameof(LocalVesselDto.FinishUnloadingDate))]
    [MapProperty(nameof(LocalVessel.GrtWeight), nameof(LocalVesselDto.GrtWeight))]
    [MapProperty(nameof(LocalVessel.InvoiceStatus), nameof(LocalVesselDto.InvoiceStatus))]
    [MapProperty(nameof(LocalVessel.AgentId), nameof(LocalVesselDto.AgentId))]
    [MapProperty(nameof(LocalVessel.AgentName), nameof(LocalVesselDto.AgentName))]
    [MapProperty(nameof(LocalVessel.StatusBms), nameof(LocalVesselDto.StatusBms))]
    [MapProperty(nameof(LocalVessel.GrtVessel), nameof(LocalVesselDto.GrtVessel))]
    [MapProperty(nameof(LocalVessel.SurveyorId), nameof(LocalVesselDto.SurveyorId))]
    [MapProperty(nameof(LocalVessel.TradingId), nameof(LocalVesselDto.TradingId))]
    [MapProperty(nameof(LocalVessel.JettyId), nameof(LocalVesselDto.JettyId))]
    [MapProperty(nameof(LocalVessel.VesselId), nameof(LocalVesselDto.VesselId))]
    [MapProperty(nameof(LocalVessel.BargeId), nameof(LocalVesselDto.BargeId))]
    public partial LocalVesselDto MapToDto(LocalVessel entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(LocalVessel.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(LocalVessel.DocEntry))] // Don't change existing DocEntry
    [MapperIgnoreTarget(nameof(LocalVessel.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(LocalVessel.CreatedAt))]
    [MapperIgnoreTarget(nameof(LocalVessel.CreationTime))]
    [MapperIgnoreTarget(nameof(LocalVessel.CreatorId))]
    [MapperIgnoreTarget(nameof(LocalVessel.LastModificationTime))]
    [MapperIgnoreTarget(nameof(LocalVessel.LastModifierId))]
    [MapperIgnoreTarget(nameof(LocalVessel.IsDeleted))]
    [MapperIgnoreTarget(nameof(LocalVessel.DeleterId))]
    [MapperIgnoreTarget(nameof(LocalVessel.DeletionTime))]
    [MapperIgnoreTarget(nameof(LocalVessel.ExtraProperties))]
    [MapperIgnoreTarget(nameof(LocalVessel.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(LocalVessel.MasterJetty))] // Navigation properties handled separately
    [MapperIgnoreTarget(nameof(LocalVessel.Vessel))]
    [MapperIgnoreTarget(nameof(LocalVessel.Barge))]
    [MapperIgnoreTarget(nameof(LocalVessel.Items))]
    public partial void MapToEntity(CreateUpdateLocalVesselDto dto, LocalVessel entity);

    // DTO to Entity mapping for creation
    [MapperIgnoreTarget(nameof(LocalVessel.Id))] // Will be set by EF Core
    [MapperIgnoreTarget(nameof(LocalVessel.DocEntry))] // Will be set by EF Core
    [MapperIgnoreTarget(nameof(LocalVessel.CreatedBy))] // Will be set by ABP
    [MapperIgnoreTarget(nameof(LocalVessel.CreatedAt))]
    [MapperIgnoreTarget(nameof(LocalVessel.CreationTime))]
    [MapperIgnoreTarget(nameof(LocalVessel.CreatorId))]
    [MapperIgnoreTarget(nameof(LocalVessel.LastModificationTime))]
    [MapperIgnoreTarget(nameof(LocalVessel.LastModifierId))]
    [MapperIgnoreTarget(nameof(LocalVessel.IsDeleted))]
    [MapperIgnoreTarget(nameof(LocalVessel.DeleterId))]
    [MapperIgnoreTarget(nameof(LocalVessel.DeletionTime))]
    [MapperIgnoreTarget(nameof(LocalVessel.ExtraProperties))]
    [MapperIgnoreTarget(nameof(LocalVessel.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(LocalVessel.MasterJetty))]
    [MapperIgnoreTarget(nameof(LocalVessel.Vessel))]
    [MapperIgnoreTarget(nameof(LocalVessel.Barge))]
    [MapperIgnoreTarget(nameof(LocalVessel.Items))]
    public partial LocalVessel MapToEntity(CreateUpdateLocalVesselDto dto);

    // Map list of entities to DTOs
    public partial List<LocalVesselDto> MapToDtoList(List<LocalVessel> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<LocalVesselDto> MapToDtoEnumerable(IEnumerable<LocalVessel> entities);

    // Custom mapping methods for complex scenarios
    public LocalVessel CreateEntityWithId(CreateUpdateLocalVesselDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (LocalVessel)Activator.CreateInstance(typeof(LocalVessel), true)!;

        // Set the ID using reflection
        var idProperty = typeof(LocalVessel).GetProperty("Id");
        idProperty?.SetValue(entity, id);

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map with items included
    public LocalVesselWithItemsDto MapToDtoWithItems(LocalVessel entity, List<VesselItemDto> items)
    {
        var dto = MapToDto(entity);
        var withItemsDto = new LocalVesselWithItemsDto
        {
            Id = dto.Id,
            DocEntry = dto.DocEntry,
            DocNum = dto.DocNum,
            PostingDate = dto.PostingDate,
            VesselType = dto.VesselType,
            VesselName = dto.VesselName,
            Tongkang = dto.Tongkang,
            VesselArrival = dto.VesselArrival,
            VesselDeparture = dto.VesselDeparture,
            Shipment = dto.Shipment,
            VesselQty = dto.VesselQty,
            PortOrigin = dto.PortOrigin,
            DestinationPort = dto.DestinationPort,
            Remark = dto.Remark,
            Deleted = dto.Deleted,
            TransType = dto.TransType,
            CreatedBy = dto.CreatedBy,
            UpdatedBy = dto.UpdatedBy,
            CreatedAt = dto.CreatedAt,
            UpdatedAt = dto.UpdatedAt,
            Voyage = dto.Voyage,
            GrossWeight = dto.GrossWeight,
            DocStatus = dto.DocStatus,
            Jetty = dto.Jetty,
            Status = dto.Status,
            BeratTugboat = dto.BeratTugboat,
            BerthingDate = dto.BerthingDate,
            AnchorageDate = dto.AnchorageDate,
            ReportDate = dto.ReportDate,
            UnloadingDate = dto.UnloadingDate,
            FinishUnloadingDate = dto.FinishUnloadingDate,
            GrtWeight = dto.GrtWeight,
            InvoiceStatus = dto.InvoiceStatus,
            AgentId = dto.AgentId,
            AgentName = dto.AgentName,
            StatusBms = dto.StatusBms,
            GrtVessel = dto.GrtVessel,
            SurveyorId = dto.SurveyorId,
            TradingId = dto.TradingId,
            JettyId = dto.JettyId,
            VesselId = dto.VesselId,
            BargeId = dto.BargeId,
            MasterJetty = dto.MasterJetty,
            Vessel = dto.Vessel,
            Barge = dto.Barge,
            Items = items
        };

        return withItemsDto;
    }
}