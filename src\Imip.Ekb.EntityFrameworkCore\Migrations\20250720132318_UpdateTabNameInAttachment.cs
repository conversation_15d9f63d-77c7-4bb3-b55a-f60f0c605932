﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTabNameInAttachment : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'JAKARTA'
                WHERE DocType = 'Import' AND TransType = 'ImportDetails'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by) = 6;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'JAKARTA'
                WHERE DocType = 'Import' AND TransType = 'Inv'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by )  = 6;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'PPJK'
                WHERE DocType = 'Import' AND TransType = 'ImportDetails'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by and (ppjk_id is null or ppjk_id = 0) )  = 3;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'TENANT'
                WHERE DocType = 'Import' AND TransType = 'Inv'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by )  = 5;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'PPJK'
                WHERE DocType = 'Import' AND TransType = 'ImportDetails'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by and (ppjk_id is not null or ppjk_id <> 0) )  = 3;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'PPJK DRAFT'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Draft Trading'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 3;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'PPJK PROFORMA'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Proforma'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 3;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'PPJK ACTUAL'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Actual'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 3;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'TENANT PROFORMA'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Proforma'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 5;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'TENANT ACTUAL'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Actual'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 5;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'FIANCE PROFORMA'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Proforma'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 9;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = 'TENANT ACTUAL'
                WHERE DocType = 'Trading' AND TransType = 'Detail' AND TypePA = 'Actual'
                AND (select permission_id from users where username = M_Doc_attachment.Created_by)  = 9;
            ");

            migrationBuilder.Sql(@"
                UPDATE M_Doc_attachment
                SET TabName = TransType
                where TabName is null
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
