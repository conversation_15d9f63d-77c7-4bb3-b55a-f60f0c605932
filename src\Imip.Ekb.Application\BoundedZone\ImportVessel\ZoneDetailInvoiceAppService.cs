using Imip.Ekb.BoundedZone.ImportVessels.ZoneDetailInvoice;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Mapping.Mappers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Imip.Ekb.BoundedZone.ImportVessels;
using System.Collections.Generic;

namespace Imip.Ekb.Application.BoundedZone.ImportVessel;

[Authorize]
public class ZoneDetailInvoiceAppService :
    CrudAppService<
        ZoneDetailInvoice,
        ZoneDetailInvoiceDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateZoneDetailInvoiceDto>, IZoneDetailInvoiceAppService
{
    private readonly IZoneDetailInvoiceRepository _zoneDetailInvoiceRepository;
    private readonly ZoneDetailInvoiceMapper _zoneDetailInvoiceMapper;
    private readonly ILogger<ZoneDetailInvoiceAppService> _logger;

    public ZoneDetailInvoiceAppService(
        IZoneDetailInvoiceRepository zoneDetailInvoiceRepository,
        ZoneDetailInvoiceMapper zoneDetailInvoiceMapper,
        ILogger<ZoneDetailInvoiceAppService> logger)
        : base(zoneDetailInvoiceRepository)
    {
        _zoneDetailInvoiceRepository = zoneDetailInvoiceRepository;
        _zoneDetailInvoiceMapper = zoneDetailInvoiceMapper;
        _logger = logger;
    }

    public override async Task<PagedResultDto<ZoneDetailInvoiceDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();
        var query = await _zoneDetailInvoiceRepository.GetQueryableWithIncludesAsync();
        if (!string.IsNullOrWhiteSpace(input.Sorting))
            query = query.OrderBy(input.Sorting);
        else
            query = query.OrderByDescending(x => x.CreationTime);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var entities = await AsyncExecuter.ToListAsync(query.PageBy(input.SkipCount, input.MaxResultCount));
        var dtos = entities.Select(MapToGetListOutputDto).ToList();
        return new PagedResultDto<ZoneDetailInvoiceDto>(totalCount, dtos);
    }

    protected override ZoneDetailInvoiceDto MapToGetOutputDto(ZoneDetailInvoice entity)
        => _zoneDetailInvoiceMapper.MapToDto(entity);

    protected override ZoneDetailInvoiceDto MapToGetListOutputDto(ZoneDetailInvoice entity)
        => _zoneDetailInvoiceMapper.MapToDto(entity);

    protected override ZoneDetailInvoice MapToEntity(CreateUpdateZoneDetailInvoiceDto createInput)
        => _zoneDetailInvoiceMapper.MapToEntity(createInput);

    protected override void MapToEntity(CreateUpdateZoneDetailInvoiceDto updateInput, ZoneDetailInvoice entity)
        => _zoneDetailInvoiceMapper.MapToEntity(updateInput, entity);

    public async Task<IEnumerable<ZoneDetailInvoiceDto>> GetByZoneDetailAsync(Guid id)
    {
        var query = await _zoneDetailInvoiceRepository.GetQueryableListAsync();
        var entities = query.Where(z => z.ZoneDetailId == id).ToList();
        return _zoneDetailInvoiceMapper.MapToDtoList(entities);
    }
}