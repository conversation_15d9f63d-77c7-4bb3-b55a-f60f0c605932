using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.Master.Agents.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Agents;

[Authorize]
public class AgentAppService :
    CrudAppService<Agent, AgentDto, Guid, PagedAndSortedResultRequestDto, AgentCreateUpdateDto, AgentCreateUpdateDto>,
    IAgentAppService
{
    private readonly IAgentRepository _agentRepository;
    private readonly AgentMapper _mapper;
    private readonly ILogger<AgentAppService> _logger;

    public AgentAppService(
        IAgentRepository agentRepository,
        AgentMapper mapper,
        ILogger<AgentAppService> logger)
        : base(agentRepository)
    {
        _agentRepository = agentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<AgentDto> CreateAsync(AgentCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;

        await _agentRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<AgentDto> UpdateAsync(Guid id, AgentCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _agentRepository.GetAsync(id);

        // Preserve original creation info
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;

        _mapper.MapToEntity(input, entity);

        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;

        await _agentRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _agentRepository.GetAsync(id);

        // Soft delete
        entity.UpdatedAt = Clock.Now;

        await _agentRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<AgentDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _agentRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<AgentDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _agentRepository.GetQueryableAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(Agent.DocEntry) : input.Sorting);

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<AgentDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<AgentDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _agentRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<AgentDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<Agent> ApplyDynamicQuery(IQueryable<Agent> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Agent>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Agent>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}