using Imip.Ekb.BoundedZone.TradingVessels.TradingInvoice;
using Imip.Ekb.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.Ekb.Repositories;
public class TradingInvoiceRepository : EfCoreRepository<EkbDbContext, TradingInvoice, Guid>, ITradingInvoiceRepository
{
    public TradingInvoiceRepository(IDbContextProvider<EkbDbContext> dbContextProvider) : base(dbContextProvider) { }

    public virtual async Task<IQueryable<TradingInvoice>> GetQueryableWithIncludesAsync()
    {
        return (await GetQueryableAsync())
            .AsNoTracking()
            .Include(x => x.ZoneDetail);
    }
}