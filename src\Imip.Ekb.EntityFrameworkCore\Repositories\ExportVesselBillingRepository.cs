using Imip.Ekb.Billing.ExportVesselBillings;
using Imip.Ekb.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.Ekb.Repositories;
public class ExportVesselBillingRepository : EfCoreRepository<EkbDbContext, ExportVesselBilling, Guid>, IExportVesselBillingRepository
{
    public ExportVesselBillingRepository(IDbContextProvider<EkbDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

    public virtual async Task<IQueryable<ExportVesselBilling>> GetQueryableWithIncludesAsync()
    {
        return (await GetQueryableAsync())
            .AsNoTracking()
            .Include(x => x.MasterJetty)
            .Where(x => !x.Is<PERSON>ted);
    }

    public virtual async Task<int> CountAsync()
    {
        var query = await GetQueryableAsync();
        return await query.Where(x => !x.IsDeleted).CountAsync();
    }
}