using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.BoundedZone.Notuls;

[Table("notuls")]
public class Notul : FullAuditedAggregateRoot<Guid>
{
    [Key]
    [Column("DocEntry")]
    public int DocEntry { get; set; }

    [Column("sptnp_no")]
    [StringLength(255)]
    public string? SptnpNo { get; set; }

    [Column("sptnp_date")]
    public DateOnly? SptnpDate { get; set; }

    [Column("spktnp_no")]
    [StringLength(255)]
    public string? SpktnpNo { get; set; }

    [Column("spktnp_date")]
    public DateOnly? SpktnpDate { get; set; }

    [Column("spsa_no")]
    [StringLength(255)]
    public string? SpsaNo { get; set; }

    [Column("spsa_date")]
    public DateOnly? SpsaDate { get; set; }

    [Column("notul_no")]
    [StringLength(255)]
    public string? NotulNo { get; set; }

    [Column("spp_date")]
    public DateOnly? SppDate { get; set; }

    [Column("billing_date")]
    public DateOnly? BillingDate { get; set; }

    [Column("billing_amount", TypeName = "decimal(20, 4)")]
    public decimal? BillingAmount { get; set; }

    [Column("billing_expired_date")]
    public DateOnly? BillingExpiredDate { get; set; }

    [Column("bpn_ebilling_date")]
    public DateOnly? BpnEbillingDate { get; set; }

    [Column("remarks")]
    public string? Remarks { get; set; }

    [Column("doc_id")]
    public long DocId { get; set; }

    [Column("created_by")]
    public long CreatedBy { get; set; }

    [Column("updated_by")]
    public long? UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [Column("notul_header_id")]
    public long? NotulHeaderId { get; set; }

    [Column("notul_type")]
    [StringLength(255)]
    public string? NotulType { get; set; }

    [Column("notul_date")]
    public DateOnly? NotulDate { get; set; }
}
