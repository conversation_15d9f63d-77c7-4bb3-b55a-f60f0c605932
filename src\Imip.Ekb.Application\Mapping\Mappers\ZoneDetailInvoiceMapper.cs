using Imip.Ekb.BoundedZone.ImportVessels.ZoneDetailInvoice;
using Imip.Ekb.BoundedZone.Dtos;
using Riok.Mapperly.Abstractions;
using System;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class ZoneDetailInvoiceMapper : IMapperlyMapper
{
    [MapProperty(nameof(ZoneDetailInvoice.Id), nameof(ZoneDetailInvoiceDto.Id))]
    [MapProperty(nameof(ZoneDetailInvoice.DocEntry), nameof(ZoneDetailInvoiceDto.DocEntry))]
    public partial ZoneDetailInvoiceDto MapToDto(ZoneDetailInvoice entity);

    [MapperIgnoreTarget(nameof(ZoneDetailInvoice.Id))]
    [MapperIgnoreTarget(nameof(ZoneDetailInvoice.DocEntry))]
    public partial void MapToEntity(CreateUpdateZoneDetailInvoiceDto dto, ZoneDetailInvoice entity);

    [MapperIgnoreTarget(nameof(ZoneDetailInvoice.Id))]
    [MapperIgnoreTarget(nameof(ZoneDetailInvoice.DocEntry))]
    public partial ZoneDetailInvoice MapToEntity(CreateUpdateZoneDetailInvoiceDto dto);

    public partial System.Collections.Generic.List<ZoneDetailInvoiceDto> MapToDtoList(System.Collections.Generic.List<ZoneDetailInvoice> entities);
    public partial System.Collections.Generic.IEnumerable<ZoneDetailInvoiceDto> MapToDtoEnumerable(System.Collections.Generic.IEnumerable<ZoneDetailInvoice> entities);
}