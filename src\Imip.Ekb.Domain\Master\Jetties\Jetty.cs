﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.Billing.ExportVesselBillings;
using Imip.Ekb.Billing.ImportVesselBillings;
using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.BoundedZone.ExportVessels;
using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.BoundedZone.LocalVessels;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.Jetties;

[Table("M_Jetty")]
public class Jetty : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    [StringLength(255)]
    public string Alias { get; set; } = null!;

    [Column(TypeName = "decimal(20, 4)")]
    public decimal Max { get; set; }

    [StringLength(5)]
    public string Deleted { get; set; } = null!;

    public int CreatedBy { get; set; }

    public int UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(255)]
    public string? Port { get; set; }

    public bool? IsCustomArea { get; set; }

    public virtual ICollection<BillingItem>? BillingItems { get; set; }
    public virtual ICollection<ImportVessel>? ImportVessels { get; set; }
    public virtual ICollection<ExportVessel>? ExportVessels { get; set; }
    public virtual ICollection<LocalVessel>? LocalVessels { get; set; }
    public virtual ICollection<ImportVesselBilling>? ImportVesselBillings { get; set; }
    public virtual ICollection<ExportVesselBilling>? ExportVesselBillings { get; set; }
    public virtual ICollection<LocalVesselBilling>? LocalVesselBillings { get; set; }
}
