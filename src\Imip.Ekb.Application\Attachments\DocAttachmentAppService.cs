using Imip.Ekb.Attachments;
using Imip.Ekb.Application.Contracts.Attachments;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Renci.SshNet;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.BlobStoring;
using Volo.Abp.Users;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.ImportVessels.ZoneDetailInvoice;
using Imip.Ekb.Master.Tenants;
using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.BoundedZone.Notuls;
using Microsoft.IO;

namespace Imip.Ekb.Application.Attachments;

/// <summary>
/// Application service for DocAttachment entity
/// </summary>
[RemoteService(IsEnabled = false)]
public partial class DocAttachmentAppService :
    CrudAppService<DocAttachment, DocAttachmentDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateDocAttachmentDto, CreateUpdateDocAttachmentDto>,
    IDocAttachmentAppService
{
    private readonly IDocAttachmentRepository _docAttachmentRepository;
    private readonly DocAttachmentMapper _mapper;
    private readonly IBlobContainer _blobContainer;
    private readonly IConfiguration _configuration;
    private readonly ILogger<DocAttachmentAppService> _logger;
    private readonly ICurrentUser _currentUser;
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly IZoneDetailInvoiceRepository _zoneDetailInvoiceRepository;
    private readonly ITenantRepository _tenantRepository;
    private readonly IBillingItemRepository _billingItemRepository;
    private readonly INotulRepository _notulRepository;

    private static readonly RecyclableMemoryStreamManager _recyclableMemoryStreamManager = new();

    public DocAttachmentAppService(
        IDocAttachmentRepository docAttachmentRepository,
        DocAttachmentMapper mapper,
        IBlobContainerFactory blobContainerFactory,
        IConfiguration configuration,
        ILogger<DocAttachmentAppService> logger,
        ICurrentUser currentUser,
        IZoneDetailRepository zoneDetailRepository,
        IZoneDetailInvoiceRepository zoneDetailInvoiceRepository,
        ITenantRepository tenantRepository,
        INotulRepository notulRepository,
        IBillingItemRepository billingItemRepository)
        : base(docAttachmentRepository)
    {
        _docAttachmentRepository = docAttachmentRepository;
        _mapper = mapper;
        _blobContainer = blobContainerFactory.Create("docs");
        _configuration = configuration;
        _logger = logger;
        _currentUser = currentUser;
        _zoneDetailRepository = zoneDetailRepository;
        _tenantRepository = tenantRepository;
        _billingItemRepository = billingItemRepository;
        _notulRepository = notulRepository;
        _zoneDetailInvoiceRepository = zoneDetailInvoiceRepository;
    }

    public override async Task<DocAttachmentDto> CreateAsync(CreateUpdateDocAttachmentDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.MapToEntity(input);

        await _docAttachmentRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<DocAttachmentDto> UpdateAsync(Guid id, CreateUpdateDocAttachmentDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _docAttachmentRepository.GetAsync(id);

        // Map properties from input to the existing entity (Mapperly does not support mapping to existing instance by default)
        entity.FileName = input.FileName;
        entity.ContentType = input.ContentType;
        entity.CreatedBy = input.CreatedBy;
        entity.DocumentReferenceId = input.DocumentReferenceId;
        entity.ReferenceId = input.ReferenceId;
        entity.BlobName = input.BlobName;
        entity.Description = input.Description;
        entity.DocType = input.DocType;
        entity.TransType = input.TransType;
        entity.SecretKey = input.SecretKey;
        entity.TypePa = input.TypePa;
        entity.FilePath = input.FilePath;
        entity.NameNoExt = input.NameNoExt;
        entity.Size = input.Size;
        entity.Extension = input.Extension;

        await _docAttachmentRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<DocAttachmentDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _docAttachmentRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<DocAttachmentDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _docAttachmentRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = _mapper.MapToList(entities);

        return new PagedResultDto<DocAttachmentDto>(totalCount, dtos);
    }

    /// <summary>
    /// Get a filtered list of DocAttachments with dynamic query support
    /// </summary>
    public virtual async Task<PagedResultDto<DocAttachmentDto>> FilterListAsync(QueryParametersDto parameters)
    {
        await CheckGetListPolicyAsync();

        var query = await _docAttachmentRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);

        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        var dtos = _mapper.MapToList(items);

        return new PagedResultDto<DocAttachmentDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    /// <summary>
    /// Apply dynamic query filters and sorting to the queryable
    /// </summary>
    private IQueryable<DocAttachment> ApplyDynamicQuery(IQueryable<DocAttachment> query, QueryParametersDto parameters)
    {
        // Apply filters if provided
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<DocAttachment>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<DocAttachment>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }

    /// <summary>
    /// Uploads a file
    /// </summary>
    public async Task<FileUploadResultDto> UploadFileAsync(FileUploadDto input, string fileName, string contentType, byte[] fileBytes)
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(fileName))
        {
            throw new UserFriendlyException("FileName cannot be empty");
        }

        if (fileBytes == null || fileBytes.Length == 0)
        {
            throw new UserFriendlyException("File content cannot be empty");
        }

        if (string.IsNullOrWhiteSpace(input.DocType) || string.IsNullOrWhiteSpace(input.TransType))
        {
            throw new UserFriendlyException("DocType and TransType are required for path generation");
        }

        try
        {
            var fileSize = fileBytes.Length;

            // Generate the SFTP folder path based on business rules
            var folderPath = await GenerateSftpFolderPathAsync(input.DocType, input.TransType, input.ReferenceId, input.TypePa, input);
            var blobName = $"{folderPath}/{Guid.NewGuid():N}_{Path.GetFileName(fileName)}";

            // Save the file to blob storage
            using (var memoryStream = _recyclableMemoryStreamManager.GetStream())
            {
                await _blobContainer.SaveAsync(blobName, memoryStream);
            }

            // Use current user's username for CreatedBy
            var createdBy = _currentUser?.UserName ?? "System";
            var createdName = _currentUser?.Name ?? "System";

            // Create a doc attachment entity
            var docAttachment = new DocAttachment
            {
                FileName = fileName,
                ContentType = contentType,
                Size = fileSize.ToString(),
                BlobName = blobName,
                FilePath = $"docs/{blobName}",
                Description = input.Description,
                ReferenceId = input.ReferenceId,
                DocumentReferenceId = input.DocumentReferenceId,
                CreatedBy = createdBy,
                CreatedName = createdName,
                DocType = input.DocType,
                TransType = input.TransType,
                TabName = input.TabName,
                TypePa = input.TypePa
            };

            // Save the attachment metadata to the database
            await _docAttachmentRepository.InsertAsync(docAttachment);

            // Return the result
            return new FileUploadResultDto
            {
                Id = docAttachment.Id,
                FileName = docAttachment.FileName ?? "unknown_file",
                ContentType = docAttachment.ContentType ?? "application/octet-stream",
                Size = long.TryParse(docAttachment.Size, out var size) ? size : 0,
                UploadTime = docAttachment.CreationTime,
                Url = GetFileUrl(docAttachment.Id),
                StreamUrl = GetFileStreamUrl(docAttachment.Id)
            };
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException("Failed to upload file", ex.Message);
        }
    }

    /// <summary>
    /// Generates the SFTP folder path based on DocType, TransType, ReferenceId, TypePa, and input DTO.
    /// </summary>
    private async Task<string> GenerateSftpFolderPathAsync(string docType, string transType, Guid? referenceId, string? typePa, FileUploadDto input)
    {
        // Helper for fallback
        string Fallback(string path, object? id = null) => id != null ? $"{path}/{id}" : path;
        string ToIdString(object? value, Guid? fallback) =>
            value switch
            {
                int i when i != 0 => i.ToString(),
                long l when l != 0 => l.ToString(),
                string s when !string.IsNullOrWhiteSpace(s) => s,
                _ => fallback?.ToString() ?? "unknown"
            };

        // Billing
        if (docType == "Billing")
        {
            var billingItem = referenceId.HasValue
                ? (await _billingItemRepository.GetQueryableWithIncludesAsync()).FirstOrDefault(z => z.Id == referenceId.Value)
                : null;
            return transType switch
            {
                "BillingDetail" => Fallback("BILLING/BillingDetail", ToIdString(billingItem?.DocEntry, referenceId)),
                "BillingLocalIN" => Fallback("BillingLocalIN", ToIdString(billingItem?.LocalVesselBilling?.DocNum, referenceId)),
                "BillingLocalOUT" => Fallback("BillingLocalOUT", ToIdString(billingItem?.LocalVesselBilling?.DocNum, referenceId)),
                "BillingImport" => Fallback("BillingImport", ToIdString(billingItem?.ImportVesselBilling?.DocNum, referenceId)),
                "BillingOther" => Fallback("BILLING/BillingOther", referenceId),
                "BillingExport" => Fallback("BillingExport", ToIdString(billingItem?.ExportVesselBilling?.DocNum, referenceId)),
                _ => Fallback("BILLING/Unknown", referenceId)
            };
        }
        // Common
        if (docType == "Common")
        {
            return transType switch
            {
                "Agent" => "Agent",
                "TemporaryImport" => "TemporaryImport",
                "StockpiledMap" => "StockpiledMap",
                "StockpiledImage" => "StockpiledImage",
                "DynamicSign" => "DynamicSign",
                "ReleaseNote" => "ReleaseNote",
                "Export" => "Export",
                "ErrorCheck" => "ErrorCheck",
                "TemporaryExport" => "TemporaryExport",
                _ => "COMMON/Unknown"
            };
        }
        // Import
        if (docType == "Import")
        {
            if (transType == "Notul") return "NOTUL";
            if (referenceId.HasValue)
            {
                if (transType == "Inv")
                {
                    var zoneDetailInvoice = (await _zoneDetailInvoiceRepository.GetQueryableWithIncludesAsync()).FirstOrDefault(z => z.Id == referenceId.Value);
                    if (zoneDetailInvoice?.ZoneDetail?.ImportVessel != null)
                    {
                        var docNum = zoneDetailInvoice.ZoneDetail.ImportVessel.DocNum?.ToString() ?? "unknown";
                        var noBl = Slugify(zoneDetailInvoice.ZoneDetail.NoBl ?? zoneDetailInvoice.ZoneDetail.BlNo ?? "nobl");
                        var noInv = Slugify(zoneDetailInvoice.ZoneDetail.NoInv ?? "noinv");
                        return $"IMPORT/{docNum}/{noBl}/{noInv}";
                    }
                }
                if (transType == "ImportDetails")
                {
                    var zoneDetail = (await _zoneDetailRepository.GetQueryableWithImportVesselAsync()).FirstOrDefault(z => z.Id == referenceId.Value);
                    if (zoneDetail?.ImportVessel != null)
                    {
                        var docNum = zoneDetail.ImportVessel.DocNum?.ToString() ?? "unknown";
                        var noBl = Slugify(zoneDetail.NoBl ?? zoneDetail.BlNo ?? "nobl");
                        return $"IMPORT/{docNum}/{noBl}";
                    }
                }
            }
            return "IMPORT/unknown";
        }
        // Local
        if (docType == "Local" && transType == "detail")
        {
            if (referenceId.HasValue)
            {
                var zoneDetail = (await _zoneDetailRepository.GetQueryableWithLocalVesselAsync()).FirstOrDefault(z => z.Id == referenceId.Value);
                if (zoneDetail?.LocalVessel != null)
                {
                    var docNum = zoneDetail.LocalVessel.DocNum?.ToString() ?? "unknown";
                    return $"LOCAL/{docNum}";
                }
            }
            return "LOCAL/unknown";
        }
        // MasterTenant
        if (docType == "MasterTenant")
        {
            var tenant = referenceId.HasValue ? (await _tenantRepository.GetQueryableWithIncludesAsync()).FirstOrDefault(z => z.Id == referenceId.Value) : null;
            var tenantName = tenant?.Name ?? "unknown";
            return transType switch
            {
                "ESignExport" => $"TENANT/{tenantName}/ESignExport",
                "TemplateConfig" => $"TENANT/{tenantName}/TemplateConfig",
                "Logo" => $"TENANT/{tenantName}/Logo",
                "StockpiledImage" => $"TENANT/{tenantName}/StockpiledImage",
                "DraftBC20" => $"TENANT/{tenantName}/DraftBC20",
                "ESign" => $"TENANT/{tenantName}/ESign",
                "Map" => $"TENANT/{tenantName}/Map",
                "StockpiledReport" => $"TENANT/{tenantName}/StockpiledReport",
                "DraftBC23" => $"TENANT/{tenantName}/DraftBC23",
                "ESignReimport" => $"TENANT/{tenantName}/ESignReimport",
                _ => $"TENANT/{tenantName}/Unknown"
            };
        }
        // Trading
        if (docType == "Trading" && (transType == "Detail" || transType == "ImportDetails"))
        {
            if (referenceId.HasValue)
            {
                var zoneDetail = (await _zoneDetailRepository.GetQueryableWithTradingVesselAsync()).FirstOrDefault(z => z.Id == referenceId.Value);
                if (zoneDetail?.TradingVessel != null)
                {
                    var docNum = zoneDetail.TradingVessel.DocNum != 0 ? zoneDetail.TradingVessel.DocNum.ToString() : "unknown";
                    return $"TR/{docNum}";
                }
            }
            return $"TR/unknown";
        }
        // Fallback
        return $"DEFAULT";
    }

    private string Slugify(string input)
    {
        if (string.IsNullOrWhiteSpace(input)) return "empty";
        var slug = input.ToUpperInvariant();
        foreach (var c in new[] { ' ', '-', '/', '\\', '.', ',', ':', ';', '\'', '"', '&', '?', '!', '@', '#', '$', '%', '^', '*', '(', ')', '[', ']', '{', '}', '|', '<', '>', '`', '~', '=', '+', ':' })
        {
            slug = slug.Replace(c.ToString(), "-");
        }
        slug = MyRegex().Replace(slug, "-"); // collapse multiple underscores
        return slug.Trim('-');
    }

    /// <summary>
    /// Gets the download URL for a file
    /// </summary>
    private string GetFileUrl(Guid id)
    {
        // Get the base URL from configuration
        var baseUrl = _configuration["App:ServerRootAddress"]?.TrimEnd('/') ?? "";

        // Return the URL
        return $"{baseUrl}/api/filestream/download/{id}";
    }

    /// <summary>
    /// Gets the stream URL for a file
    /// </summary>
    private string GetFileStreamUrl(Guid id)
    {
        // Get the base URL from configuration
        var baseUrl = _configuration["App:ServerRootAddress"]?.TrimEnd('/') ?? "";

        // Return the URL
        return $"{baseUrl}/api/filestream/stream/{id}";
    }

    /// <summary>
    /// Uploads a file using a single DTO
    /// </summary>
    public async Task<FileUploadResultDto> UploadWithFileContentAsync(FileUploadInputDto input)
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(input.FileName))
        {
            throw new UserFriendlyException("FileName cannot be empty");
        }

        if (input.FileContent == null || input.FileContent.Length == 0)
        {
            throw new UserFriendlyException("File content cannot be empty");
        }

        try
        {
            var fileSize = input.FileContent.Length;

            // Generate a unique blob name
            var blobName = $"{Guid.NewGuid():N}_{Path.GetFileName(input.FileName)}";

            // Save the file to blob storage
            using (var memoryStream = _recyclableMemoryStreamManager.GetStream())
            {
                await _blobContainer.SaveAsync(blobName, memoryStream);
            }

            // Create a doc attachment entity
            var docAttachment = new DocAttachment
            {
                FileName = input.FileName,
                ContentType = input.ContentType,
                Size = fileSize.ToString(),
                BlobName = blobName,
                Description = input.Description,
                ReferenceId = input.ReferenceId,
                CreatedBy = "System", // Default value
                DocType = "DOC", // Default value
                TransType = "TRANS" // Default value
            };

            // Save the attachment metadata to the database
            await _docAttachmentRepository.InsertAsync(docAttachment);

            // Return the result
            return new FileUploadResultDto
            {
                Id = docAttachment.Id,
                FileName = docAttachment.FileName ?? "unknown_file",
                ContentType = docAttachment.ContentType ?? "application/octet-stream",
                Size = long.TryParse(docAttachment.Size, out var size2) ? size2 : 0,
                UploadTime = docAttachment.CreationTime,
                Url = GetFileUrl(docAttachment.Id),
                StreamUrl = GetFileStreamUrl(docAttachment.Id)
            };
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException("Failed to upload file", ex.Message);
        }
    }

    /// <summary>
    /// Downloads a file by ID
    /// </summary>
    public async Task<FileDto> DownloadAsync(Guid id)
    {
        // Get the attachment metadata
        var docAttachment = await _docAttachmentRepository.GetAsync(id);
        if (docAttachment == null)
        {
            throw new UserFriendlyException("File not found");
        }

        try
        {
            // Check if FilePath is null
            if (string.IsNullOrEmpty(docAttachment.BlobName))
            {
                throw new UserFriendlyException("File content not found");
            }

            // Get the file from blob storage
            var stream = await _blobContainer.GetAsync(docAttachment.BlobName);
            if (stream == null)
            {
                throw new UserFriendlyException("File content not found");
            }

            // Read the file content
            using var memoryStream = _recyclableMemoryStreamManager.GetStream();
            await stream.CopyToAsync(memoryStream);
            return new FileDto(
                docAttachment.FileName ?? "unknown_file",
                docAttachment.ContentType ?? "application/octet-stream",
                memoryStream.ToArray()
            );
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException("Failed to download file", ex.Message);
        }
    }

    /// <summary>
    /// Gets a list of attachments by reference ID and type
    /// </summary>
    public async Task<List<FileUploadResultDto>> GetByReferenceAsync(Guid referenceId)
    {
        // Get attachments by reference
        var attachments = await _docAttachmentRepository.GetListAsync(
            a => a.ReferenceId == referenceId
        );

        // Map to DTOs
        return attachments.Select(a => new FileUploadResultDto
        {
            Id = a.Id,
            FileName = a.FileName ?? "unknown_file",
            ContentType = a.ContentType ?? "application/octet-stream",
            Size = long.TryParse(a.Size, out var s) ? s : 0,
            UploadTime = a.CreationTime,
            Url = GetFileUrl(a.Id),
            StreamUrl = GetFileStreamUrl(a.Id)
        }).ToList();
    }

    /// <summary>
    /// Downloads a file by SFTP path
    /// </summary>
    public async Task<FileDto> DownloadByPathAsync(string filePath)
    {
        if (string.IsNullOrEmpty(filePath))
        {
            throw new UserFriendlyException("File path cannot be empty");
        }

        try
        {
            // Extract filename from path
            var fileName = Path.GetFileName(filePath);
            if (string.IsNullOrEmpty(fileName))
            {
                fileName = "unknown_file";
            }

            // Try to find the DocAttachment by filename first
            var docAttachments = await _docAttachmentRepository.GetListAsync(a => a.FileName == fileName);
            var docAttachment = docAttachments.FirstOrDefault();

            _logger.LogInformation("Found {Count} DocAttachment records for filename: {FileName}", docAttachments.Count, fileName);

            // If not found by exact filename, try to find by BlobName containing the filename
            if (docAttachment == null)
            {
                _logger.LogInformation("No exact filename match found, searching for BlobName containing filename: {FileName}", fileName);
                var blobNameMatches = await _docAttachmentRepository.GetListAsync(a => a.BlobName != null && a.BlobName.Contains(fileName));
                docAttachment = blobNameMatches.FirstOrDefault();
                _logger.LogInformation("Found {Count} DocAttachment records with BlobName containing filename: {FileName}", blobNameMatches.Count, fileName);
            }

            _logger.LogInformation("Found DocAttachment with BlobName: {BlobName}", docAttachment?.BlobName ?? "null");

            if (docAttachment != null && !string.IsNullOrEmpty(docAttachment.BlobName))
            {
                _logger.LogInformation("Found DocAttachment with BlobName: {BlobName}", docAttachment.BlobName);

                // Use the blob name from the database
                var stream = await _blobContainer.GetAsync(docAttachment.BlobName);
                if (stream != null)
                {
                    using var memoryStream = _recyclableMemoryStreamManager.GetStream();
                    await stream.CopyToAsync(memoryStream);

                    return new FileDto(
                        docAttachment.FileName ?? fileName,
                        docAttachment.ContentType ?? GetContentTypeFromExtension(fileName),
                        memoryStream.ToArray()
                    );
                }
                else
                {
                    _logger.LogWarning("Blob container returned null stream for BlobName: {BlobName}", docAttachment.BlobName);
                }
            }
            else if (docAttachment != null && string.IsNullOrEmpty(docAttachment.BlobName))
            {
                _logger.LogInformation("Found legacy DocAttachment with null BlobName, will try direct SFTP access for path: {FilePath}", filePath);
            }
            else
            {
                _logger.LogWarning("DocAttachment found but BlobName is null or empty. BlobName: '{BlobName}'",
                    docAttachment?.BlobName ?? "null");
            }

            // Fallback: try to get the file directly from the path using direct SFTP connection
            // Since the blob container has a different path structure, we'll use direct SFTP access
            _logger.LogInformation("Attempting to get file directly from SFTP path: {FilePath}", filePath);

            // Try multiple path variations for legacy files
            var pathVariations = new[]
            {
                filePath.StartsWith("/") ? filePath : $"/{filePath}",
                filePath.StartsWith("/") ? filePath.Substring(1) : filePath,
                $"/ekb/{filePath}",
                $"/ekb/{filePath.TrimStart('/')}",
                filePath
            };

            Stream? directStream = null;
            string? successfulPath = null;

            foreach (var path in pathVariations)
            {
                _logger.LogInformation("Trying SFTP path: {Path}", path);
                directStream = await GetFileFromSftpDirectlyAsync(path);
                if (directStream != null)
                {
                    successfulPath = path;
                    _logger.LogInformation("Successfully found file at path: {Path}", path);
                    break;
                }
            }

            if (directStream == null)
            {
                throw new UserFriendlyException($"File not found on SFTP server. Tried paths: {string.Join(", ", pathVariations)}, FileName: {fileName}");
            }

            // Read the file content
            using var fallbackMemoryStream = _recyclableMemoryStreamManager.GetStream();
            await directStream.CopyToAsync(fallbackMemoryStream);

            // Try to determine content type from file extension
            var contentType = GetContentTypeFromExtension(fileName);

            return new FileDto(fileName, contentType, fallbackMemoryStream.ToArray());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download file from SFTP path: {FilePath}", filePath);
            throw new UserFriendlyException("Failed to download file from SFTP", ex.Message);
        }
    }

    /// <summary>
    /// Gets content type from file extension
    /// </summary>
    private string GetContentTypeFromExtension(string fileName)
    {
        var extension = Path.GetExtension(fileName)?.ToLowerInvariant();
        return extension switch
        {
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xls" => "application/vnd.ms-excel",
            ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".txt" => "text/plain",
            _ => "application/octet-stream"
        };
    }

    /// <summary>
    /// Gets a file directly from SFTP using SSH.NET
    /// </summary>
    private async Task<Stream?> GetFileFromSftpDirectlyAsync(string sftpPath)
    {
        try
        {
            // Get SFTP configuration from appsettings
            var sftpConfig = _configuration.GetSection("BlobStoring:Default:SFTP");
            var host = sftpConfig["Host"];
            var port = sftpConfig.GetValue<int>("Port", 22);
            var userName = sftpConfig["UserName"];
            var password = sftpConfig["Password"];
            var connectionTimeout = sftpConfig.GetValue<int>("ConnectionTimeout", 30000);

            if (string.IsNullOrEmpty(host) || string.IsNullOrEmpty(userName) || string.IsNullOrEmpty(password))
            {
                _logger.LogError("SFTP configuration is incomplete - Host: {Host}, User: {User}, Password: {HasPassword}",
                    host, userName, !string.IsNullOrEmpty(password));
                return null;
            }

            _logger.LogInformation("Connecting to SFTP server - Host: {Host}, Port: {Port}, User: {User}, Path: {Path}",
                host, port, userName, sftpPath);

            // Create connection info
            var connectionInfo = new Renci.SshNet.ConnectionInfo(
                host,
                port,
                userName,
                new Renci.SshNet.AuthenticationMethod[]
                {
                    new Renci.SshNet.PasswordAuthenticationMethod(userName, password)
                }
            );

            // Set timeout
            connectionInfo.Timeout = TimeSpan.FromMilliseconds(connectionTimeout);

            using var client = new Renci.SshNet.SftpClient(connectionInfo);

            try
            {
                await Task.Run(() => client.Connect());

                if (!client.IsConnected)
                {
                    _logger.LogError("Failed to connect to SFTP server");
                    return null;
                }

                _logger.LogInformation("Successfully connected to SFTP server");

                // Check if file exists
                if (!await Task.Run(() => client.Exists(sftpPath)))
                {
                    _logger.LogWarning("File does not exist on SFTP server: {Path}", sftpPath);
                    return null;
                }

                _logger.LogInformation("File exists on SFTP server, downloading: {Path}", sftpPath);

                // Download the file
                var memoryStream = _recyclableMemoryStreamManager.GetStream();
                using var downloadStream = await Task.Run(() => client.OpenRead(sftpPath));
                await downloadStream.CopyToAsync(memoryStream);
                memoryStream.Position = 0;

                _logger.LogInformation("Successfully downloaded file from SFTP: {Path}, Size: {Size} bytes",
                    sftpPath, memoryStream.Length);
                return memoryStream;
            }
            finally
            {
                if (client.IsConnected)
                {
                    client.Disconnect();
                    _logger.LogInformation("Disconnected from SFTP server");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading file from SFTP: {Path}", sftpPath);
            return null;
        }
    }

    /// <summary>
    /// Deletes a file by ID
    /// </summary>
    public async Task<bool> DeleteAsync(Guid id)
    {
        // Get the attachment metadata
        var docAttachment = await _docAttachmentRepository.FindAsync(id);
        if (docAttachment == null)
        {
            return false;
        }

        try
        {
            // Delete the file from blob storage if FilePath is not null
            if (!string.IsNullOrEmpty(docAttachment.BlobName))
            {
                await _blobContainer.DeleteAsync(docAttachment.BlobName);
            }

            // Delete the attachment metadata from the database
            await _docAttachmentRepository.DeleteAsync(docAttachment);

            return true;
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException("Failed to delete file", ex.Message);
        }
    }

    [System.Text.RegularExpressions.GeneratedRegex("_+")]
    private static partial System.Text.RegularExpressions.Regex MyRegex();
}