using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.BoundedZone.Dtos;

public class CreateUpdateZoneDetailDto : EntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public int? DocEntry { get; set; }

    public int? BcTypeKey { get; set; }
    public int? TenantKey { get; set; }

    [StringLength(255)]
    public string? Bp { get; set; }

    [StringLength(255)]
    public string? Cargo { get; set; }

    public decimal? Weight { get; set; }

    [StringLength(255)]
    public string? BlNo { get; set; }

    public DateOnly? BlDate { get; set; }

    [StringLength(255)]
    public string? AjuNo { get; set; }

    [StringLength(255)]
    public string? RegNo { get; set; }

    public DateOnly? RegDate { get; set; }

    [StringLength(255)]
    public string? SppbNo { get; set; }

    public DateOnly? SppbDate { get; set; }

    [StringLength(255)]
    public string? SppdNo { get; set; }

    public DateOnly? SppdDate { get; set; }

    [StringLength(255)]
    public string? Shipment { get; set; }

    public string? Remarks { get; set; }

    [Required]
    [StringLength(255)]
    public string CreatedBy { get; set; } = string.Empty;

    public int? CreatedId { get; set; }

    [StringLength(255)]
    public string? UpdatedBy { get; set; }

    public int? UpdatedId { get; set; }

    [StringLength(255)]
    public string? Color { get; set; }

    public int? SapKbEntry { get; set; }

    [StringLength(255)]
    public string? NoBl { get; set; }

    public DateOnly? DateBl { get; set; }

    [StringLength(255)]
    public string? NoInv { get; set; }

    public DateOnly? DateInv { get; set; }

    [StringLength(255)]
    public string? ShipmentNo { get; set; }

    public DateOnly? EbillingDate { get; set; }

    [StringLength(255)]
    public string? Skep { get; set; }

    public DateOnly? SkepDate { get; set; }

    [StringLength(255)]
    public string? PibNo { get; set; }

    public DateOnly? PibDate { get; set; }

    public DateOnly? VesselArrive { get; set; }

    public DateOnly? ExpiredDate { get; set; }

    public string? Item { get; set; }

    public decimal? Qty { get; set; }

    public decimal? Amount { get; set; }

    [StringLength(255)]
    public string? Status { get; set; }

    [StringLength(255)]
    public string? SiteStatus { get; set; }

    public int? DocNum { get; set; }

    [StringLength(10)]
    public string? Flags { get; set; }

    [StringLength(255)]
    public string? OceanFreight { get; set; }

    [StringLength(255)]
    public string? Currency { get; set; }

    [StringLength(255)]
    public string? Ocean { get; set; }

    [StringLength(255)]
    public string? Cbmb { get; set; }

    public decimal? FreightValue { get; set; }

    [StringLength(255)]
    public string? Attachment { get; set; }

    public DateOnly? PostDate { get; set; }

    [Required]
    [StringLength(100)]
    public string DocType { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string IsScan { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string IsOriginal { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string IsSend { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string IsFeOri { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string IsFeSend { get; set; } = string.Empty;

    [StringLength(100)]
    public string? SecretKey { get; set; }

    public int? Ppjk { get; set; }

    [StringLength(20)]
    public string? PpjkcodeTemp { get; set; }

    [StringLength(255)]
    public string? PortOfLoading { get; set; }

    public DateOnly? EmailToPpjk { get; set; }

    [StringLength(255)]
    public string? LetterNo { get; set; }

    public string? ItemName { get; set; }

    public decimal? ItemQty { get; set; }

    [StringLength(20)]
    public string? UnitQty { get; set; }

    public decimal? GrossWeight { get; set; }

    [StringLength(20)]
    public string? UnitWeight { get; set; }

    [StringLength(100)]
    public string? MatchKey { get; set; }

    public int? Bpnum { get; set; }

    public int? CargoNum { get; set; }

    public int? LineNum { get; set; }

    [Required]
    [StringLength(10)]
    public string IsChange { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string Deleted { get; set; } = string.Empty;

    public DateOnly? SppbUpdateDate { get; set; }

    [StringLength(50)]
    public string? SppbNoUpdate { get; set; }

    public DateTime? SppbDateUpdate { get; set; }

    [StringLength(50)]
    public string? SppdNoUpdate { get; set; }

    public DateTime? SppdDateUpdate { get; set; }

    [StringLength(200)]
    public string? DeleteBy { get; set; }

    [StringLength(80)]
    public string? EBillingNo { get; set; }

    [StringLength(255)]
    public string? ContractNo { get; set; }

    [StringLength(100)]
    public string? OpenDate { get; set; }

    [StringLength(100)]
    public string? UpdateDate { get; set; }

    [StringLength(255)]
    public string? InternalCode { get; set; }

    public DateOnly? ContractDate { get; set; }

    [StringLength(255)]
    public string? RegType { get; set; }

    public long? Id { get; set; }

    public decimal? Cbm { get; set; }

    [StringLength(255)]
    public string? Notification { get; set; }

    [StringLength(255)]
    public string? Sppbstatus { get; set; }

    public int? Agent { get; set; }

    public int? BillingId { get; set; }

    [StringLength(100)]
    public string? InsuranceCurrency { get; set; }

    public decimal? InsuranceValue { get; set; }

    public int? DestinationPortId { get; set; }

    public decimal? NetWeight { get; set; }

    public decimal? UnitPrice { get; set; }

    public decimal? TotalInv { get; set; }

    public decimal? QtyEstimate { get; set; }

    public decimal? PriceEstimate { get; set; }

    [StringLength(200)]
    public string? BillingType { get; set; }

    [StringLength(200)]
    public string? ChargeTo { get; set; }

    public decimal? QtyRevised { get; set; }

    public decimal? PriceRevised { get; set; }

    [StringLength(255)]
    public string? NoNota { get; set; }

    public decimal? TotalEstimate { get; set; }

    public decimal? TotalRevised { get; set; }

    [StringLength(255)]
    public string? SerialNumber { get; set; }

    [StringLength(200)]
    public string? SerialNumber1 { get; set; }

    [StringLength(200)]
    public string? SerialNumber2 { get; set; }

    [StringLength(200)]
    public string? SerialNumber3 { get; set; }

    [StringLength(200)]
    public string? SerialNumber4 { get; set; }

    [StringLength(200)]
    public string? SerialNumber5 { get; set; }

    [StringLength(200)]
    public string? SerialNumber6 { get; set; }

    [StringLength(255)]
    public string? IsParent { get; set; }

    public decimal? GrtVessel { get; set; }

    [StringLength(255)]
    public string? NpwpBp { get; set; }

    public int? EsignDecimal { get; set; }

    public long? CargoId { get; set; }

    public long? BargeId { get; set; }

    [StringLength(255)]
    public string? Voyage { get; set; }

    [StringLength(255)]
    public string? VesselName { get; set; }

    [StringLength(100)]
    public string? ProcessName { get; set; }

    public decimal? Rate { get; set; }

    public decimal? Bm { get; set; }

    public decimal? Ppn { get; set; }

    public decimal? Pph { get; set; }

    public decimal? Bmad { get; set; }

    public decimal? Bmtp { get; set; }

    [StringLength(255)]
    public string? FormType { get; set; }

    public DateOnly? BillingDate { get; set; }

    [Required]
    [StringLength(10)]
    public string IsUrgent { get; set; } = string.Empty;

    public long? SurveyorId { get; set; }

    [StringLength(255)]
    public string? SurveyorName { get; set; }

    public DateOnly? EmailToBcDate { get; set; }

    public decimal? Container { get; set; }

    [StringLength(26)]
    public string? ExportClassificationId { get; set; }

    public long? WarehouseId { get; set; }

    public decimal? IncreaseValue { get; set; }

    public decimal? DecreaseValue { get; set; }

    public decimal? IncreaseValuePpn { get; set; }

    public decimal? DecreaseValuePpn { get; set; }

    public decimal? IncreaseValuePph { get; set; }

    public decimal? DecreaseValuePph { get; set; }

    public string? RepairLocation { get; set; }

    public long? RepresentativeId { get; set; }

    public long? InvoiceDetailId { get; set; }

    public decimal? CostOfRepair { get; set; }
    public DateOnly? LetterDate { get; set; }
    public string? ShippingInstructionNo { get; set; }
    public DateOnly? ShippingInstructionDate { get; set; }

    [StringLength(10)]
    public string? ItemCategoryCode { get; set; }

    [StringLength(150)]
    public string? ItemCategoryDescription { get; set; }

    [StringLength(100)]
    public string? SapBillingStatus { get; set; }
    [Required]
    public Guid TenantId { get; set; }
    public Guid? BcTypeId { get; set; }
    public Guid? HeaderId { get; set; }
    public Guid? BusinessPartnerId { get; set; }
    public Guid? AgentId { get; set; }
    public Guid? MasterExportClassificationId { get; set; }
}