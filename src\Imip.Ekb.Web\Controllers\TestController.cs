using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;
using Imip.Ekb.Web.Services.Interfaces;
using System.Linq;

namespace Imip.Ekb.Web.Controllers;

[Route("api/test")]
public class TestController : AbpController
{
    private readonly ILogger<TestController> _logger;
    private readonly ITokenService _tokenService;

    public TestController(
        ILogger<TestController> logger,
        ITokenService tokenService)
    {
        _logger = logger;
        _tokenService = tokenService;
    }

    [HttpGet("auth-status")]
    [Authorize]
    public async Task<IActionResult> GetAuthStatus()
    {
        try
        {
            var result = new
            {
                IsAuthenticated = User.Identity?.IsAuthenticated ?? false,
                UserName = User.Identity?.Name,
                UserId = User.FindFirstValue(ClaimTypes.NameIdentifier),
                Email = User.FindFirstValue(ClaimTypes.Email),
                Claims = User.Claims.Select(c => new { c.Type, c.Value }).ToList(),
                AccessToken = await _tokenService.GetAccessTokenAsync(),
                HasValidToken = await _tokenService.HasValidTokenAsync()
            };

            _logger.LogInformation("Auth status for user: {UserName}, HasToken: {HasToken}",
                result.UserName, !string.IsNullOrEmpty(result.AccessToken));

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting auth status");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpGet("token-info")]
    [Authorize]
    public async Task<IActionResult> GetTokenInfo()
    {
        try
        {
            var accessToken = await _tokenService.GetAccessTokenAsync();
            var hasValidToken = await _tokenService.HasValidTokenAsync();

            var result = new
            {
                HasToken = !string.IsNullOrEmpty(accessToken),
                TokenLength = accessToken?.Length ?? 0,
                HasValidToken = hasValidToken,
                UserName = User.Identity?.Name
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting token info");
            return StatusCode(500, new { error = ex.Message });
        }
    }
}