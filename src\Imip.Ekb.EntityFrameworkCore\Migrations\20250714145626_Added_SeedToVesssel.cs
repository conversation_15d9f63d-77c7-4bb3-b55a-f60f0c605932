﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class Added_SeedToVesssel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "AgentId",
                table: "T_MDOC",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MasterExportClassificationId",
                table: "T_MDOC",
                type: "uniqueidentifier",
                nullable: true);

            // Drop the old primary key
            migrationBuilder.DropPrimaryKey(
                name: "PK_ExportClassification_DocEntry",
                table: "ExportClassification");

            // Add the new primary key on Id
            migrationBuilder.AddPrimaryKey(
                name: "PK_ExportClassification",
                table: "ExportClassification",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_AgentId",
                table: "T_MDOC",
                column: "AgentId");

            migrationBuilder.CreateIndex(
                name: "IX_T_MDOC_MasterExportClassificationId",
                table: "T_MDOC",
                column: "MasterExportClassificationId");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_ExportClassification_MasterExportClassificationId",
                table: "T_MDOC",
                column: "MasterExportClassificationId",
                principalTable: "ExportClassification",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_T_MDOC_M_Agent_AgentId",
                table: "T_MDOC",
                column: "AgentId",
                principalTable: "M_Agent",
                principalColumn: "Id");


            migrationBuilder.Sql(@"
                UPDATE T_MDOC
                SET AgentId = (SELECT tbc.Id FROM M_Agent tbc WHERE T_MDOC.Agent = tbc.DocEntry);
            ");

            migrationBuilder.Sql(@"
                UPDATE T_MDOC_Header
                SET MasterAgentId = (SELECT tbc.Id FROM M_Agent tbc WHERE T_MDOC_Header.AgentId = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE T_MDOC_Header
                SET MasterSurveyorId = (SELECT tbc.Id FROM master_surveyors tbc WHERE T_MDOC_Header.SurveyorId = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE T_MDOC_Header
                SET MasterTradingId = (SELECT tbc.Id FROM master_tradings tbc WHERE T_MDOC_Header.TradingId = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE L_Master
                SET MasterAgentId = (SELECT tbc.Id FROM M_Agent tbc WHERE L_Master.AgentId = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE L_Master
                SET MasterSurveyorId = (SELECT tbc.Id FROM master_surveyors tbc WHERE L_Master.SurveyorId = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE L_Master
                SET MasterTradingId = (SELECT tbc.Id FROM master_tradings tbc WHERE L_Master.TradingId = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE THEXP
                SET MasterAgentId = (SELECT tbc.Id FROM M_Agent tbc WHERE THEXP.AgentId = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE THEXP
                SET MasterSurveyorId = (SELECT tbc.Id FROM master_surveyors tbc WHERE THEXP.SurveyorId = tbc.DocEntry);
            ");
            migrationBuilder.Sql(@"
                UPDATE THEXP
                SET MasterTradingId = (SELECT tbc.Id FROM master_tradings tbc WHERE THEXP.TradingId = tbc.DocEntry);
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_ExportClassification_MasterExportClassificationId",
                table: "T_MDOC");

            migrationBuilder.DropForeignKey(
                name: "FK_T_MDOC_M_Agent_AgentId",
                table: "T_MDOC");

            // Drop the new primary key
            migrationBuilder.DropPrimaryKey(
                name: "PK_ExportClassification",
                table: "ExportClassification");

            // Re-add the old primary key on DocEntry
            migrationBuilder.AddPrimaryKey(
                name: "PK_ExportClassification_DocEntry",
                table: "ExportClassification",
                column: "DocEntry");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_AgentId",
                table: "T_MDOC");

            migrationBuilder.DropIndex(
                name: "IX_T_MDOC_MasterExportClassificationId",
                table: "T_MDOC");

            migrationBuilder.DropColumn(
                name: "AgentId",
                table: "T_MDOC");

            migrationBuilder.DropColumn(
                name: "MasterExportClassificationId",
                table: "T_MDOC");
        }
    }
}
