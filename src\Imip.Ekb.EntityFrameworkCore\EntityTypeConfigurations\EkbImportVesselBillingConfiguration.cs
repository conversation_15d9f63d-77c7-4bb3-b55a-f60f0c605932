
using Imip.Ekb.Billing.ImportVesselBillings;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;
namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbImportVesselBillingConfiguration : IEntityTypeConfiguration<ImportVesselBilling>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbImportVesselBillingConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<ImportVesselBilling> b)
    {
        b.ToTable("BHIMP", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions

        b.HasOne(x => x.MasterJetty)
            .WithMany(z => z.ImportVesselBillings)
            .HasForeignKey(x => x.JettyId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);


        b.HasMany(x => x.Items)
            .WithOne(x => x.ImportVesselBilling)
            // .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);
    }
}