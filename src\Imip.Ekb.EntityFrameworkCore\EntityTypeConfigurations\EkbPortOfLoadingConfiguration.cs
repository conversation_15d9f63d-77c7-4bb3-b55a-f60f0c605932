using Imip.Ekb.Master.PortOfLoadings;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbPortOfLoadingConfiguration : IEntityTypeConfiguration<PortOfLoading>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbPortOfLoadingConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<PortOfLoading> b)
    {
        b.ToTable("M_PortOfLoading", EkbConsts.DbSchema);
        b.ConfigureByConvention(); // Auto configure conventions
    }
}